<!DOCTYPE html>
<html lang="en" dir="ltr">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">

    <!-- Page Title -->
    <title><PERSON> - Online Portfolio</title>

    <!-- Meta Tags -->
    <meta name="description" content="Porfolio online">
    <meta name="keywords"
        content=" resume, portfolio, personal page, cv, template, one page, responsive, html5, css3, creative, clean">
    <meta name="author" content="Lucas">

    <!-- Viewport Meta-->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">

    <!-- Template Favicon & Icons Start -->
    <link rel="icon" href="img/favicon/Asuka.png" sizes="any">
    <link rel="icon" href="img/favicon/Asuka.svg" type="image/svg+xml">
    <link rel="apple-touch-icon" href="img/favicon/apple-touch-icon.png">
    <link rel="manifest" href="img/favicon/manifest.webmanifest">
    <!-- Template Favicon & Icons End -->

    <!-- Facebook Metadata Start -->
    <meta property="og:image:height" content="1200">
    <meta property="og:image:width" content="1200">
    <meta property="og:title" content="Lucas Schoenherr - Online Portfolio">
    <meta property="og:description" content="Lucas Schoenherr - Online Portfolio">
    <!-- Facebook Metadata End -->

    <!-- Template Styles Start -->
    <link rel="stylesheet" type="text/css" href="css/loaders/loader.css">
    <link rel="stylesheet" type="text/css" href="css/plugins.css">
    <link rel="stylesheet" type="text/css" href="css/main.css">

    <!-- Template Styles End -->

    <!-- Custom Browser Color Start -->
    <meta name="theme-color" media="(prefers-color-scheme: light)" content="#BABEC8">
    <meta name="theme-color" media="(prefers-color-scheme: dark)" content="#141414">
    <meta name="msapplication-navbutton-color" content="#141414">
    <meta name="mobile-web-app-capable" content="yes">
    <!-- Custom Browser Color End -->

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/magnific-popup.js/1.1.0/magnific-popup.min.css">
</head>

<body data-bs-spy="scroll" data-bs-target=".sidebar-nav" data-bs-offset="200">

    <!-- Loader Start -->
    <div id="loader" class="loader">
        <div class="loader__wrapper">
            <div class="loader__content">
                <div class="loader__count">
                    <span class="count__text">0</span>
                    <span class="count__percent">%</span>
                </div>
            </div>
        </div>
    </div>
    <!-- Loader End -->

    <!-- Header Start -->
    <header id="header" class="header d-flex justify-content-center loading__fade loading-wrap">
        <!-- Navigation Menu Start -->
        <div class="header__navigation d-flex justify-content-start">
            <nav id="menu" class="menu">
                <ul class="menu__list d-flex justify-content-start">
                    <li class="menu__item">
                        <a class="menu__link btn" href="index.html">
                            <span class="menu__caption">Home</span>
                            <i class="ph ph-house-simple"></i>
                        </a>
                    </li>
                    <li class="menu__item">
                        <a class="menu__link btn" href="index.html#portfolio">
                            <span class="menu__caption">Portfólio</span>
                            <i class="ph ph-squares-four"></i>
                        </a>
                    </li>
                    <li class="menu__item">
                        <a class="menu__link btn" href="index.html#about">
                            <span class="menu__caption">Sobre</span>
                            <i class="ph ph-user"></i>
                        </a>
                    </li>
                    <li class="menu__item">
                        <a class="menu__link btn" href="index.html#services">
                            <span class="menu__caption">Especialidades</span>
                            <i class="ph ph-sticker"></i>
                        </a>
                    </li>
                    <li class="menu__item">
                        <a class="menu__link btn" href="index.html#resume">
                            <span class="menu__caption">Curriculum</span>
                            <i class="ph ph-article"></i>
                        </a>
                    </li>
                    <li class="menu__item">
                        <a class="menu__link btn" href="index.html#contact">
                            <span class="menu__caption">Contato</span>
                            <i class="ph ph-envelope"></i>
                        </a>
                    </li>
                </ul>
            </nav>
        </div>
        <!-- Navigation Menu End -->
    </header>
    <!-- Header End -->

    <!-- Fixed Logo Start -->
    <div class="logo loading__fade">
        <a href="index.html" class="logo__link">
            <!-- logo icon -->
            <img src="img/favicon/Asuka.svg" alt="Logo Icon" style="width: 35px; height: 35px;">
            <!-- logo text -->
            <span class="logo-text">Lucas Schoenherr</span>
        </a>
    </div>
    <!-- Fixed Logo End -->

   <!-- Fixed Color Switch Start -->
   <div class="color loading__fade">
    <button id="color-switcher" class="color-switcher" type="button" role="switch" aria-label="light/dark mode" aria-checked="true"></button>
  </div>
  <!-- Fixed Color Switch End -->

    <!-- Page Content Start -->
    <main id="page-content" class="page-content">
        <section id="project" class="inner">
            <div class="inner__wrapper">
                <div class="container-fluid p-0">
                    <div class="row g-0">
                        <!-- Project Content Start -->
                        <div class="col-12 col-xl-8 offset-xl-2">
                            <div class="inner__content">
                                <!-- Project Header -->
                                <div class="main-wrapper">
                                    <div class="project-header">
                                        <h1 class="project-title">Lighthouse App</h1>

                                        <div class="project-tags">
                                            <span class="tag vale">Vale</span>
                                            <span class="tag">Mobile App</span>
                                            <span class="tag">Data Visualization</span>
                                            <span class="tag">Design System</span>
                                            <span class="tag">MVP</span>
                                            <span class="tag">UX/UI Design</span>
                                        </div>

                                        <div class="project-description">
                                            Lighthouse é um aplicativo mobile desenvolvido para a Vale que revoluciona o monitoramento de produção em tempo real. Oferece visualização de dados dinâmicos, sistema de notificações personalizáveis e comunicação eficiente entre equipes, permitindo tomadas de decisão ágeis e precisas. Uma solução mobile-first desenvolvida como MVP para modernizar o sistema legado e otimizar a gestão operacional.
                                        </div>
                                        <div class="content__block loading__item disclaimer">
                                            <div class="disclaimer__content">
                                                <i class="ph ph-info"></i>
                                                <p class="disclaimer__text type-basic-160lh">
                                                    Todos os dados e métricas apresentados nas imagens são fictícios, gerados apenas para fins de demonstração. Os mesmos não refletem informações reais da empresa ou de suas operações.
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="custom-swiper swiper">
                                    <div class="swiper-button-prev">
                                        <i class="ph ph-arrow-left"></i>
                                    </div>
                                    <div class="swiper-button-next">
                                        <i class="ph ph-arrow-right"></i>
                                    </div>
                                    <div class="swiper-wrapper">
                                        <div class="swiper-slide">
                                            <img src="/img/works/ProductionApp/1.png" alt="Slide 1">
                                        </div>
                                        <div class="swiper-slide">
                                            <img src="/img/works/ProductionApp/2.png" alt="Slide 2">
                                        </div>
                                        <div class="swiper-slide">
                                            <img src="/img/works/ProductionApp/3.png" alt="Slide 3">
                                        </div>
                                        <div class="swiper-slide">
                                            <img src="/img/works/ProductionApp/4.png" alt="Slide 4">
                                        </div>
                                        <div class="swiper-slide">
                                            <img src="/img/works/ProductionApp/5.png" alt="Slide 5">
                                        </div>
                                        <div class="swiper-slide">
                                            <img src="/img/works/ProductionApp/6.png" alt="Slide 6">
                                        </div>
                                        <div class="swiper-slide">
                                            <img src="/img/works/ProductionApp/7.png" alt="Slide 7">
                                        </div>
                                        <div class="swiper-slide">
                                            <img src="/img/works/ProductionApp/8.png" alt="Slide 8">
                                        </div>
                                        <div class="swiper-slide">
                                            <img src="/img/works/ProductionApp/9.png" alt="Slide 9">
                                        </div>
                                        <div class="swiper-slide">
                                            <img src="/img/works/ProductionApp/10.png" alt="Slide 10">
                                        </div>
                                        <div class="swiper-slide">
                                            <img src="/img/works/ProductionApp/11.png" alt="Slide 11">
                                        </div>
                                        <div class="swiper-slide">
                                            <img src="/img/works/ProductionApp/12.png" alt="Slide 12">
                                        </div>
                                    </div>
                                </div>

                                <!-- Disclaimer Section -->
                                

                                <!-- Project Data Section Start -->
                                <div class="content__block loading__item">
                                    <div class="project__data">
                                        <div class="container-fluid p-0">
                                            <div class="row g-0">
                                                <!-- project data single item -->
                                                <div class="col-12 col-md-3 grid-item pdata__item">
                                                    <p class="data__title tagline-chapter small type-basic-160lh">
                                                        Tipo</p>
                                                    <p class="data__descr small type-basic-160lh">
                                                        Aplicativo Mobile</p>
                                                </div>
                                                <!-- project data single item -->
                                                <div class="col-12 col-md-3 grid-item pdata__item">
                                                    <p class="data__title tagline-chapter small type-basic-160lh">
                                                        Ano</p>
                                                    <p class="data__descr small type-basic-160lh">
                                                        2023</p>
                                                </div>
                                                <!-- project data single item -->
                                                <div class="col-12 col-md-3 grid-item pdata__item">
                                                    <p class="data__title tagline-chapter small type-basic-160lh">
                                                        Papel</p>
                                                    <p class="data__descr small type-basic-160lh">
                                                        Lead Product Designer</p>
                                                </div>
                                                <!-- project data single item -->
                                                <div class="col-12 col-md-3 grid-item pdata__item">
                                                    <p class="data__title tagline-chapter small type-basic-160lh">
                                                        Cliente</p>
                                                    <p class="data__descr small type-basic-160lh">
                                                        Vale</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- Project Data Section End -->

                                <!-- New Two-Column Layout Start -->
                                <div class="container-fluid p-0">
                                    <div class="row g-0">
                                        <div class="col-12 col-lg-3">
                                            <nav class="sidebar-nav loading__item" role="navigation" aria-label="Menu lateral">
                                                <ul class="nav flex-column list-unstyled">
                                                    <li class="nav-item">
                                                        <a class="nav-link" href="#overview"
                                                            aria-current="page">Overview</a>
                                                    </li>
                                                    <li class="nav-item">
                                                        <a class="nav-link" href="#desafio"
                                                            aria-current="page">Desafio</a>
                                                    </li>
                                                    <li class="nav-item">
                                                        <a class="nav-link" href="#papel">Papel</a>
                                                    </li>
                                                    <li class="nav-item">
                                                        <a class="nav-link" href="#solucao">Solução</a>
                                                    </li>
                                                    <li class="nav-item">
                                                        <a class="nav-link" href="#impacto">Entrega</a>
                                                    </li>
                                                    <li class="nav-item">
                                                        <a class="nav-link" href="#galeria">Galeria</a>
                                                    </li>
                                                </ul>
                                            </nav>
                                        </div>

                                        <!-- Main Content -->
                                        <div class="col-12 col-lg-9">
                                            <div class="main-content">
                                                <!-- Overview Section -->
                                                <div id="overview" class="project__block loading__item">
                                                    <h3>Overview do Projeto</h3>
                                                    <p class="type-basic-160lh">
                                                        O Lighthouse representa uma transformação significativa na forma como a Vale monitora e gerencia sua produção. Nascido da necessidade de modernizar um sistema legado, o projeto foi concebido para substituir uma solução baseada em intranet que já não atendia às demandas de segurança e usabilidade da empresa.
                                                    </p>

                                                    <p class="type-basic-160lh">
                                                        Como Lead Product Designer, liderei o desenvolvimento de uma solução mobile-first que unifica dados críticos de múltiplos corredores em uma única interface intuitiva. O aplicativo foi estruturado para atender dois objetivos principais: agilizar a tomada de decisão dos stakeholders através de visualizações de dados em tempo real, e criar um canal de comunicação eficiente entre as equipes através de um sistema de notificações personalizáveis.
                                                    </p>

                                                    <p class="type-basic-160lh">
                                                        Adotamos uma abordagem MVP focada inicialmente no corredor norte, priorizando funcionalidades core como visualização de dados em tempo real, sistema de permissões hierárquicas e comunicação entre equipes. Esta estratégia nos permitiu validar rapidamente o conceito e estabelecer as bases para uma futura expansão, incluindo recursos adicionais como geolocalização, upload de imagens e mapas interativos.
                                                    </p>
                                                    <div>
                                                        <div class="regular-swiper swiper">
                                                            <div class="swiper-button-prev">
                                                                <i class="ph ph-arrow-left"></i>
                                                            </div>
                                                            <div class="swiper-button-next">
                                                                <i class="ph ph-arrow-right"></i>
                                                            </div>


                                                            <div class="swiper-wrapper">
                                                                <div
                                                                    class="swiper-slide swiper-slide--one mobile-stack">
                                                                    <img src="img/works/ProductionApp/Overview/Overview1.png" alt="Slide 1">
                                                                </div>
                                                                <div
                                                                    class="swiper-slide swiper-slide--two mobile-stack">
                                                                    <img src="img/works/ProductionApp/Overview/Overview2.png" alt="Slide 2">
                                                                </div>
                                                                <div
                                                                    class="swiper-slide swiper-slide--three mobile-stack">
                                                                    <img src="img/works/ProductionApp/Overview/Overview3.png" alt="Slide 3">
                                                                </div>
                                                                <div
                                                                    class="swiper-slide swiper-slide--four mobile-stack">
                                                                    <img src="img/works/ProductionApp/Overview/Overview4.png" alt="Slide 4">
                                                                </div>
                                                                <div
                                                                    class="swiper-slide swiper-slide--five mobile-stack">
                                                                    <img src="img/works/ProductionApp/Overview/Overview5.png" alt="Slide 5">
                                                                </div>
                                                            </div>
                                                            <div class="swiper-pagination"></div>
                                                        </div>
                                                    </div>
                                                </div>


                                                <!-- Challenge Section -->
                                                <div id="desafio" class="project__block loading__item">
                                                    <h3>O Desafio</h3>
                                                    <p class="type-basic-160lh">
                                                        Para compreender profundamente as necessidades dos usuários e as limitações do sistema existente, iniciamos o projeto com sessões de Design Thinking envolvendo stakeholders e usuários-chave. Este processo revelou desafios críticos que precisavam ser endereçados em diferentes níveis.
                                                    </p>
                                                
                                                    <p class="type-basic-160lh">
                                                        O sistema legado, baseado em intranet, apresentava deficiências significativas: interface desatualizada, problemas de segurança e uma experiência do usuário que não atendia às necessidades modernas da Vale. Os gerentes, principais usuários da plataforma, enfrentavam dificuldades para acessar informações críticas em tempo hábil, impactando diretamente a velocidade e qualidade das decisões operacionais.
                                                    </p>
                                                
                                                    <p class="type-basic-160lh">
                                                        O maior desafio de design foi criar uma interface mobile que pudesse apresentar um grande volume de dados complexos de forma clara e acessível. A solução precisava equilibrar a densidade de informações com a usabilidade em telas menores, considerando diferentes níveis de permissão e perfis de usuário.
                                                    </p>

                                                    <div class="content__block grid-block">
                                                        <div class="container-fluid px-0">
                                                            <div class="my-gallery" itemscope itemtype="http://schema.org/ImageGallery">
                                                                <figure class="full-width-image-container" itemprop="associatedMedia" itemscope itemtype="http://schema.org/ImageObject">
                                                                    <a href="img/works/ProductionApp/Desafio/Workflow.png" 
                                                                       data-size="4885x2443" 
                                                                       itemprop="contentUrl" 
                                                                       class="gallery__link">
                                                                        <img src="img/works/ProductionApp/Desafio/Workflow_t.png" 
                                                                             alt="Imagem de impacto do projeto"
                                                                             class="full-width-image" 
                                                                             itemprop="thumbnail">
                                                                    </a>
                                                                </figure>
                                                            </div>
                                                        </div>
                                                        <div class="container-fluid px-0"></div>
                                                            <div class="my-gallery" itemscope itemtype="http://schema.org/ImageGallery">
                                                                <figure class="full-width-image-container" itemprop="associatedMedia" itemscope itemtype="http://schema.org/ImageObject">
                                                                    <a href="img/works/ProductionApp/Desafio/Workflow2.png" 
                                                                       data-size="4885x2443" 
                                                                       itemprop="contentUrl" 
                                                                       class="gallery__link">
                                                                        <img src="img/works/ProductionApp/Desafio/Workflow2_t.png" 
                                                                             alt="Imagem de impacto do projeto"
                                                                             class="full-width-image" 
                                                                             itemprop="thumbnail">
                                                                    </a>
                                                                </figure>
                                                            </div>
                                                        </div>
                                                    </div>

                                                <!-- Role Section -->
                                                <div id="papel" class="project__block loading__item">
                                                    <h3>Meu Papel</h3>
                                                    <p class="type-basic-160lh">
                                                        Utilizando a metodologia ágil para projetos, desenvolvi um processo de design focado em escalabilidade, com ênfase em componentização e melhores práticas para interfaces mobile e web. Esta abordagem sistemática permitiu criar um produto robusto e preparado para futuras expansões, ao mesmo tempo que atendia às necessidades imediatas do MVP.
                                                    </p>

                                                    <p class="type-basic-160lh">
                                                        Um aspecto fundamental do meu papel foi a gestão de relacionamentos e expectativas. Conduzi reuniões semanais de alinhamento com stakeholders, equipe de desenvolvimento e usuários-chave, onde apresentava atualizações, coletava feedback e negociava prazos. Esta comunicação constante foi essencial para manter o projeto dentro do escopo e cronograma planejados.
                                                    </p>

                                                    <p class="type-basic-160lh">
                                                        O projeto resultou na entrega de um protótipo otimizado e completamente navegável, com design system integrado e preparado para escala. Apesar dos desafios de prazo e conflitos de agenda, a experiência proporcionou aprendizados valiosos em gestão de projetos, negociação e a importância da componentização para produtos escaláveis.
                                                    </p>
                                                </div>

                                                <!-- Solution Section -->
                                                <div id="solucao" class="project__block loading__item">
                                                    <h3>A Solução</h3>
                                                    <p class="type-basic-160lh">
                                                        A solução foi desenvolvida com foco em modularidade e escalabilidade, priorizando uma experiência mobile intuitiva que permite acesso rápido às informações críticas. O coração do sistema são cards responsivos que apresentam dados em tempo real, com um sistema de cores intuitivo (verde, amarelo, azul e vermelho) para indicar diferentes níveis de prioridade e status.
                                                    </p>
                                                
                                                    <p class="type-basic-160lh">
                                                        Desenvolvemos um sistema robusto de componentes que inclui oito variações de cards de informação, cada um otimizado para diferentes tipos de dados e contextos. A hierarquia visual foi cuidadosamente planejada para priorizar informações-chave no feed principal, permitindo que usuários acessem detalhamentos mais específicos através de interações intuitivas.
                                                    </p>
                                                
                                                    <p class="type-basic-160lh">
                                                        O sistema de notificações foi projetado para ser altamente personalizável, permitindo que usuários filtrem e recebam alertas específicos por área (porto, minas, ferrovias, entre outros). Complementando a experiência, implementamos componentes essenciais como filtros dinâmicos, indicadores de status e visualizações gráficas, estabelecendo uma base sólida para futuras implementações como sistema de comentários, upload de imagens e mapas interativos.
                                                    </p>
                                                
                                                    <p class="type-basic-160lh">
                                                        Esta arquitetura modular não apenas resolve o desafio inicial de densidade informacional, mas também estabelece um framework flexível que pode evoluir junto com as necessidades da Vale. Cada componente foi projetado pensando em reusabilidade e consistência, garantindo uma experiência coesa em todas as partes do aplicativo.
                                                    </p>
                                                     <!-- Nova seção de imagem responsiva -->
                                                
                                                <div class="infinite-scroll-section"></div>
                                                    <div class="tag-list">
                                                        <div class="fade"></div>

                                                        <div class="loop-slider"
                                                            style="--duration:15951ms; --direction:normal;">
                                                            <div class="inner">
                                                                <div class="image-card">
                                                                    <img src="img/works/ProductionApp/Solucao/Cardg.webp" alt="Projeto 1">
                                                                </div>
                                                                <div class="image-card">
                                                                    <img src="img/works/ProductionApp/Solucao/Cardb.webp" alt="Projeto 2">
                                                                </div>
                                                                <div class="image-card">
                                                                    <img src="img/works/ProductionApp/Solucao/Cardy.webp" alt="Projeto 1">
                                                                </div>
                                                                <div class="image-card">
                                                                    <img src="img/works/ProductionApp/Solucao/Cardr.webp" alt="Projeto 2">
                                                                </div>
                                                                <div class="image-card">
                                                                    <img src="img/works/ProductionApp/Solucao/Cardg.webp" alt="Projeto 1">
                                                                </div>
                                                                <div class="image-card">
                                                                    <img src="img/works/ProductionApp/Solucao/Cardb.webp" alt="Projeto 2">
                                                                </div>
                                                                <div class="image-card">
                                                                    <img src="img/works/ProductionApp/Solucao/Cardy.webp" alt="Projeto 1">
                                                                </div>
                                                                <div class="image-card">
                                                                    <img src="img/works/ProductionApp/Solucao/Cardr.webp" alt="Projeto 2">
                                                                </div>
                                                               
                                                            </div>

                                                        </div>

                                                        <!-- Segunda linha - direção reversa -->
                                                        <div class="loop-slider"
                                                            style="--duration:15951ms; --direction:reverse;">
                                                            <div class="inner">
                                                                <div class="image-card">
                                                                    <img src="img/works/ProductionApp/Solucao/compactg.webp" alt="Projeto 1">
                                                                </div>
                                                                <div class="image-card">
                                                                    <img src="img/works/ProductionApp/Solucao/compactb.webp" alt="Projeto 2">
                                                                </div>
                                                                <div class="image-card">
                                                                    <img src="img/works/ProductionApp/Solucao/compacty.webp" alt="Projeto 1">
                                                                </div>
                                                                <div class="image-card">
                                                                    <img src="img/works/ProductionApp/Solucao/compactr.webp" alt="Projeto 2">
                                                                </div>
                                                                <div class="image-card">
                                                                    <img src="img/works/ProductionApp/Solucao/compactg.webp" alt="Projeto 1">
                                                                </div>
                                                                <div class="image-card">
                                                                    <img src="img/works/ProductionApp/Solucao/compactb.webp" alt="Projeto 2">
                                                                </div>
                                                                <div class="image-card">
                                                                    <img src="img/works/ProductionApp/Solucao/compacty.webp" alt="Projeto 1">
                                                                </div>
                                                                <div class="image-card">
                                                                    <img src="img/works/ProductionApp/Solucao/compactr.webp" alt="Projeto 2">
                                                                </div>
                                                            </div>

                                                        </div>

                                                        <!-- Terceira linha - direção normal -->
                                                        <div class="loop-slider"
                                                            style="--duration:15951ms; --direction:normal;">
                                                            <div class="inner">
                                                                <div class="image-card">
                                                                    <img src="img/works/ProductionApp/Solucao/ultrag.webp" alt="Projeto 1">
                                                                </div>
                                                                <div class="image-card">
                                                                    <img src="img/works/ProductionApp/Solucao/ultrab.webp" alt="Projeto 2">
                                                                </div>
                                                                <div class="image-card">
                                                                    <img src="img/works/ProductionApp/Solucao/ultray.webp" alt="Projeto 1">
                                                                </div>
                                                                <div class="image-card">
                                                                    <img src="img/works/ProductionApp/Solucao/ultragr.webp" alt="Projeto 2">
                                                                </div>
                                                                <div class="image-card">
                                                                    <img src="img/works/ProductionApp/Solucao/ultrag.webp" alt="Projeto 1">
                                                                </div>
                                                                <div class="image-card">
                                                                    <img src="img/works/ProductionApp/Solucao/ultrab.webp" alt="Projeto 2">
                                                                </div>
                                                                <div class="image-card">
                                                                    <img src="img/works/ProductionApp/Solucao/ultray.webp" alt="Projeto 1">
                                                                </div>
                                                                <div class="image-card">
                                                                    <img src="img/works/ProductionApp/Solucao/ultragr.webp" alt="Projeto 2">
                                                                </div>
                                                            </div>

                                                        </div>

                                                    </div>
                                                </div>

                                                <!-- Delivery Section -->
                                                <div id="impacto" class="project__block loading__item">
                                                    <h3>A Entrega</h3>
                                                    <p class="type-basic-160lh">
                                                        A apresentação final do MVP foi recebida com grande entusiasmo pelos stakeholders. A demonstração detalhada das funcionalidades, métodos de implementação e projeções futuras evidenciou o potencial do projeto, gerando feedback positivo e imediato interesse em sua expansão.
                                                    </p>
                                                
                                                    <p class="type-basic-160lh">
                                                        O protótipo entregue cumpriu todos os objetivos estabelecidos inicialmente, com destaque para a otimização na visualização e compartilhamento de dados em tempo real. O design moderno e intuitivo, aliado à eficiência na apresentação de informações críticas, foram especialmente elogiados pelos futuros usuários e gestores do projeto.
                                                    </p>
                                                
                                                    <p class="type-basic-160lh">
                                                        Com a conclusão bem-sucedida desta fase, o projeto foi encaminhado para o time de desenvolvimento avançado da Vale, acompanhado de documentação detalhada e um design system robusto. Esta transição estruturada estabeleceu bases sólidas para o desenvolvimento futuro, incluindo as implementações previstas de funcionalidades adicionais já mapeadas durante a fase de prototipação.
                                                    </p>
                                                    <div class="container-fluid px-0">
                                                            <div class="my-gallery" itemscope itemtype="http://schema.org/ImageGallery">
                                                                <figure class="full-width-image-container" itemprop="associatedMedia" itemscope itemtype="http://schema.org/ImageObject">
                                                                    <a href="img/works/ProductionApp/Impacto/Porto.png" 
                                                                       data-size="4885x2443" 
                                                                       itemprop="contentUrl" 
                                                                       class="gallery__link">
                                                                        <img src="img/works/ProductionApp/Impacto/Portot.png" 
                                                                             alt="Imagem de impacto do projeto"
                                                                             class="full-width-image" 
                                                                             itemprop="thumbnail">
                                                                    </a>
                                                                </figure>
                                                            </div>
                                                        </div>
                                                    
                                                </div>

                                               

                                                <!-- Gallery Section -->
                                                <div id="galeria" class="project__block loading__item">
                                                    <h3>Galeria</h3>
                                                    <p class="type-basic-160lh">
                                                        Esta seção apresenta materiais complementares do projeto, incluindo detalhamentos de componentes, telas adicionais e materiais de apresentação. São artefatos que, embora não façam parte do fluxo principal, oferecem uma visão mais ampla do processo de design e da complexidade do projeto.
                                                    </p>
                                                    <div class="content__block grid-block">
                                                        <div class="container-fluid px-0 inner__gallery">
                                                    
                                                            <div class="row my-gallery" itemscope itemtype="http://schema.org/ImageGallery">
                                                                <!-- First row -->
                                                                <div class="row mb-4">
                                                                    <!-- First column -->
                                                                    <figure class="col-12 col-md-6 gallery__item" itemprop="associatedMedia" itemscope itemtype="http://schema.org/ImageObject">
                                                                        <a href="img/works/ProductionApp/Galeria/MVP.png" data-image="img/works/ProductionApp/Galeria/MVPt.png" class="gallery__link" itemprop="contentUrl" data-size="1100x1400">
                                                                            <img src="img/works/ProductionApp/Galeria/MVPt.png" class="gallery__image" itemprop="thumbnail" alt="Descrição da imagem">
                                                                        </a>
                                                                    </figure>
                                                    
                                                                    <!-- Second column -->
                                                                    <figure class="col-12 col-md-6 gallery__item" itemprop="associatedMedia" itemscope itemtype="http://schema.org/ImageObject">
                                                                        <a href="img/works/ProductionApp/Galeria/mock.png" data-image="img/works/ProductionApp/Galeria/mockt.png" class="gallery__link" itemprop="contentUrl" data-size="1100x1400">
                                                                            <img src="img/works/ProductionApp/Galeria/mockt.png" class="gallery__image" itemprop="thumbnail" alt="Image description">
                                                                        </a>
                                                                    </figure>
                                                    
                                                                    <figure class="col-12 gallery__item" itemprop="associatedMedia" itemscope itemtype="http://schema.org/ImageObject">
                                                                    <a href="img/works/ProductionApp/Galeria/Components.png" data-image="img/works/ProductionApp/Galeria/Componentst.png" class="gallery__link" itemprop="contentUrl" data-size="8000x3000">
                                                                        <img src="img/works/ProductionApp/Galeria/Componentst.png" class="gallery__image" itemprop="thumbnail" alt="Descrição da imagem em largura total">
                                                                    </a>
                                                                    </figure>
                                                            </div>
                                                    </div>
                                                </div>

                                                <!-- Footer Section -->
                                                <div class="project__block loading__item footer-section">
                                                    <div class="footer-content">
                                                        <div class="footer-grid">
                                                            <!-- Text Content -->
                                                            <div class="footer-text">
                                                                <h2>Vamos Conversar!</h2>
                                                                <p class="type-basic-160lh">
                                                                    Interessado em me ter na sua equipe? Deixe-me uma mensagem e retornarei o mais rápido possível.
                                                                </p>
                                                            </div>
                                                            <!-- Social Icons -->
                                                            <div class="social-icons">
                                                                <a href="mailto:<EMAIL>?subject=Mensagem%20do%20Portfolio" class="social-icon">
                                                                    <i class="ph ph-envelope"></i>
                                                                </a>
                                                                <a href="https://www.linkedin.com/in/lucas-scho/" class="social-icon">
                                                                    <i class="ph ph-linkedin-logo"></i>
                                                                </a>
                                                                <a href="https://www.instagram.com/lucaslsm" class="social-icon">
                                                                    <i class="ph ph-instagram-logo"></i>
                                                                </a>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- New Two-Column Layout End -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

    </main>

    <!-- Mobile Menu Bottom Placeholder Start -->
    <div class="header-offset"></div>
    <!-- Mobile Menu Bottom Placeholder End -->

    <!-- To Top Button Start -->
    <a href="#0" id="to-top" class="btn btn-to-top slide-up">
        <i class="ph ph-arrow-up"></i>
    </a>
    <!-- To Top Button End -->

    </div>
        <!-- Root element of PhotoSwipe. Must have class pswp. -->
        <div class="pswp" tabindex="-1" role="dialog" aria-hidden="true">

            <!-- Background of PhotoSwipe.
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        It's a separate element, as animating opacity is faster than rgba(). -->
            <div class="pswp__bg"></div>
    
            <!-- Slides wrapper with overflow:hidden. -->
            <div class="pswp__scroll-wrap">
    
                <!-- Container that holds slides. PhotoSwipe keeps only 3 slides in DOM to save memory. -->
                <!-- don't modify these 3 pswp__item elements, data is added later on. -->
                <div class="pswp__container">
                    <div class="pswp__item"></div>
                    <div class="pswp__item"></div>
                    <div class="pswp__item"></div>
                </div>
    
                <!-- Default (PhotoSwipeUI_Default) interface on top of sliding area. Can be changed. -->
                <div class="pswp__ui pswp__ui--hidden">
    
                    <div class="pswp__top-bar">
    
                        <!--  Controls are self-explanatory. Order can be changed. -->
    
                        <div class="pswp__counter"></div>
    
                        <button class="pswp__button pswp__button--close link-s" title="Close (Esc)"></button>
    
                        <button class="pswp__button pswp__button--share link-s" title="Share"></button>
    
                        <button class="pswp__button pswp__button--fs link-s" title="Toggle fullscreen"></button>
    
                        <button class="pswp__button pswp__button--zoom link-s" title="Zoom in/out"></button>
    
                        <!-- Preloader demo http://codepen.io/dimsemenov/pen/yyBWoR -->
                        <!-- element will get class pswp__preloader-active when preloader is running -->
                        <div class="pswp__preloader">
                            <div class="pswp__preloader__icn">
                                <div class="pswp__preloader__cut">
                                    <div class="pswp__preloader__donut"></div>
                                </div>
                            </div>
                        </div>
                    </div>
    
                    <div class="pswp__share-modal pswp__share-modal--hidden pswp__single-tap">
                        <div class="pswp__share-tooltip"></div>
                    </div>
    
                    <button class="pswp__button pswp__button--arrow--left link-s" title="Previous (arrow left)"></button>
    
                    <button class="pswp__button pswp__button--arrow--right link-s" title="Next (arrow right)"></button>
    
                    <div class="pswp__caption">
                        <div class="pswp__caption__center"></div>
                    </div>
    
                </div>
    
            </div>
    
        </div>

    <!-- Load Scripts Start -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollTrigger.min.js"></script>
    <script src="js/libs.min.js"></script>
    <script src="js/gallery-init.js"></script>
    <script src="js/app.js"></script>
    <script src="js/carossel.js"></script>
    <script src="https://code.jquery.com/ui/1.13.2/jquery-ui.min.js"></script>
    <script>
        document.querySelectorAll('.lightboxgallery-link').forEach(item => {
            item.addEventListener('click', event => {
                event.preventDefault();
                const imgSrc = event.currentTarget.href;
                const overlay = document.createElement('div');
                overlay.classList.add('lightboxgallery-overlay');
                overlay.innerHTML = `<img src="${imgSrc}">`;
                document.body.appendChild(overlay);

                overlay.addEventListener('click', () => {
                    document.body.removeChild(overlay);
                });
            });
        });
    </script>

    <!-- Load Scripts End -->
</body>

</html>