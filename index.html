<!DOCTYPE html>
<html lang="en" dir="ltr">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">

    <!-- Page Title -->
    <title><PERSON> - Online Portfolio</title>

    <!-- Meta Tags -->
    <meta name="description" content="Porfolio online">
    <meta name="keywords"
        content=" resume, portfolio, personal page, cv, template, one page, responsive, html5, css3, creative, clean">
    <meta name="author" content="<PERSON>">

    <!-- Viewport Meta-->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">

    <!-- Template Favicon & Icons Start -->
    <link rel="icon" href="img/favicon/Asuka.png" sizes="any">
    <link rel="icon" href="img/favicon/Asuka.svg" type="image/svg+xml">
    <link rel="apple-touch-icon" href="img/favicon/apple-touch-icon.png">
    <link rel="manifest" href="img/favicon/manifest.webmanifest">
    <!-- Template Favicon & Icons End -->

    <!-- Facebook Metadata Start -->
    <meta property="og:image:height" content="1200">
    <meta property="og:image:width" content="1200">
    <meta property="og:title" content="Lucas Schoenherr - Online Portfolio">
    <meta property="og:description" content="Lucas Schoenherr - Online Portfolio">
    <!-- Facebook Metadata End -->

    <!-- Template Styles Start -->
    <link rel="stylesheet" type="text/css" href="css/loaders/loader.css">
    <link rel="stylesheet" type="text/css" href="css/plugins.css">
    <link rel="stylesheet" type="text/css" href="css/main.css">

    <!-- Template Styles End -->

    <!-- Custom Browser Color Start -->
    <meta name="theme-color" media="(prefers-color-scheme: light)" content="#BABEC8">
    <meta name="theme-color" media="(prefers-color-scheme: dark)" content="#141414">
    <meta name="msapplication-navbutton-color" content="#141414">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <!-- Custom Browser Color End -->
</head>

<body>

    <!-- Loader Start -->
    <div id="loader" class="loader">
        <div class="loader__wrapper">
            <div class="loader__content">
                <div class="loader__count">
                    <span class="count__text">0</span>
                    <span class="count__percent">%</span>
                </div>
            </div>
        </div>
    </div>
    <!-- Loader End -->

    <!-- Header Start -->
    <header id="header" class="header d-flex justify-content-center loading__fade">
        <!-- Navigation Menu Start -->
        <div class="header__navigation d-flex justify-content-start">
            <nav id="menu" class="menu">
                <ul class="menu__list d-flex justify-content-start">
                    <li class="menu__item">
                        <a class="menu__link btn" href="#home">
                            <span class="menu__caption">Home</span>
                            <i class="ph ph-house-simple"></i>
                        </a>
                    </li>
                    <li class="menu__item">
                        <a class="menu__link btn" href="#portfolio">
                            <span class="menu__caption">Portfólio</span>
                            <i class="ph ph-squares-four"></i>
                        </a>
                    </li>
                    <li class="menu__item">
                        <a class="menu__link btn" href="#about">
                            <span class="menu__caption">Sobre</span>
                            <i class="ph ph-user"></i>
                        </a>
                    </li>
                    <li class="menu__item">
                        <a class="menu__link btn" href="#services">
                            <span class="menu__caption">Especialidades</span>
                            <i class="ph ph-sticker"></i>
                        </a>
                    </li>
                    <li class="menu__item">
                        <a class="menu__link btn" href="#resume">
                            <span class="menu__caption">Curriculum</span>
                            <i class="ph ph-article"></i>
                        </a>
                    </li>
                    <li class="menu__item">
                        <a class="menu__link btn" href="#contact">
                            <span class="menu__caption">Contato</span>
                            <i class="ph ph-envelope"></i>
                        </a>
                    </li>
                </ul>
            </nav>
        </div>
        <!-- Navigation Menu End -->
    </header>
    <!-- Header End -->

    <!-- Fixed Logo Start -->
    <div class="logo loading__fade">
        <a href="#home" class="logo__link">
            <!-- logo icon -->
            <img src="img/favicon/Asuka.svg" alt="Logo Icon" style="width: 35px; height: 35px;">
            <!-- logo text -->
            <span class="logo-text">Lucas Schoenherr</span>
        </a>
    </div>
    <!-- Fixed Logo End -->

    <!-- Fixed Color Switch Start -->
    <div class="color loading__fade">
        <button id="color-switcher" class="color-switcher" type="button" role="switch" aria-label="light/dark mode"
            aria-checked="true"></button>
    </div>
    <!-- Fixed Color Switch End -->

    <!-- Page Content Start -->
    <main id="page-content" class="page-content">

        <!-- Main Section Start -->
        <section id="home" class="main home">

            <!-- Main Section Intro Start -->
            <div class="main__intro">

                <!-- Intro Background Start -->
                <div class="intro__background intro-bg-01">
                    <div class="intro-bg-01__01" data-speed="0.6">
                        <img src="img/backgrounds/test4.webp" alt="Background Objects">
                        <div class="intro-bg__shadow"></div>
                    </div>
                    <div class="intro-bg-01__02" data-speed="0.8">
                        <img src="img/backgrounds/test.webp" alt="Background Objects">
                        <div class="intro-bg__shadow"></div>
                    </div>
                </div>
                <!-- Intro Background End -->

                <div class="container-fluid p-0 fullheight-desktop">
                    <div class="row g-0 fullheight-desktop align-items-xl-stretch">

                        <!-- Intro Data Line #1 (if needed) Start -->
                        <div class="col-12 col-xl-2"></div>
                        <!-- Intro Data Line #1 (if needed) End -->

                        <!-- Intro Content Start -->
                        <div class="col-12 col-xl-8 fullheight-desktop">

                            <!-- Headline Start -->
                            <div id="headline" class="headline d-flex align-items-start flex-column loading-wrap">
                                <p class="headline__subtitle space-top animated-type loading__item">Ola! Eu sou o Lucas
                                    Schoenherr.
                                    <br>
                                    <span id="typed-strings">
                                        <b>Product Design Specialist</b>
                                        <b>UI/UX Specialist</b>
                                        <b>Team Leader</b>
                                    </span>
                                    <span id="typed"></span>
                                </p>
                                <h1 class="headline__title loading__item">Product Design Specialist</h1>
                                <div class="headline__btn loading__item">
                                    <a class="btn btn-line-small icon-right slide-right-down" href="#portfolio">
                                        <span class="btn-caption">Conheça meu trabalho</span>
                                        <i class="ph ph-arrow-down-right"></i>
                                    </a>
                                </div>
                            </div>
                            <!-- Headline End -->

                        </div>
                        <!-- Intro Content End -->

                        <!-- Intro Data Line #2 (if needed) Start -->
                        <div class="col-12 col-xl-2"></div>
                        <!-- Intro Data Line #2 (if needed) End -->

                    </div>
                </div>
            </div>
            <!-- Main Section Intro End -->

            <!-- Main Section Media Start -->
            <div class="main__media media-grid-bottom">
                <div class="container-fluid p-0">
                    <div class="row g-0">

                        <!-- Media Data Line #1 (if needed) Start -->
                        <div class="col-12 col-xl-2"></div>
                        <!-- Media Data Line #1 (if needed) End -->

                        <!-- Media Content Start -->
                        <div class="col-12 col-xl-8">

                            <!-- Content Block - Image Divider Start -->
                            <!-- Content Block - Image Divider Start -->
                            <div class="content__block">
                                <div class="container-fluid p-0">
                                    <div class="row g-0">
                                        <div class="col-12">
                                            <div class="divider divider-image animate-in-up">
                                                <div class="flex-container">
                                                    <div class="flex-item">
                                                        <img class="fitter" src="img/backgrounds/Work1.webp" />
                                                    </div>
                                                    <div class="flex-item">
                                                        <img class="fitter" src="img/backgrounds/Work2.webp" />
                                                    </div>
                                                    <div class="flex-item">
                                                        <img class="fitter" src="img/backgrounds/Work3.webp" />
                                                    </div>
                                                    <div class="flex-item">
                                                        <img class="fitter" src="img/backgrounds/Work4.webp" />
                                                    </div>
                                                    <div class="flex-item">
                                                        <img class="fitter" src="img/backgrounds/Work5.webp" />
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- Content Block - Image Divider End -->

                        </div>
                    </div>
                </div>
            </div>
            <!-- Main Section Media End -->

        </section>
        <!-- Main Section End -->

        <!-- Inner Section - Portfolio - Start -->
        <!-- Portfolio Section Start -->
        <section id="portfolio" class="inner inner-grid-bottom portfolio">
            <div class="inner__wrapper">
                <div class="container-fluid p-0">
                    <div class="row g-0">

                        <!-- Inner Section Name Start -->
                        <div class="col-12 col-xl-2">
                            <div class="inner__name">

                                <!-- Content Block - Section Name Start -->
                                <div class="content__block name-block">
                                    <span class="section-name icon-right animate-in-up">
                                        <span class="section-name-caption">Portfólio</span>
                                        <i class="ph ph-arrow-down-right"></i>
                                    </span>
                                </div>
                                <!-- Content Block - Section Name Start -->

                            </div>
                        </div>
                        <!-- Inner Section Name End -->

                        <!-- Inner Section Content Start -->
                        <div class="col-12 col-xl-8">
                            <div class="inner__content">

                                <!-- Content Block - H2 Section Title Start -->
                                <div class="content__block section-grid-text-title">
                                    <div class="block__descr">
                                        <h2 class="reveal-type animate-in-up">Design, UI, UX<br>e um pouco de magia.
                                        </h2>
                                        <p class="h2__text type-basic-160lh animate-in-up">Transformo desafios complexos
                                            em soluções de
                                            design elegantes e funcionais. Com 13 anos de experiência, minha paixão é
                                            criar produtos digitais
                                            que fazem a diferença na vida dos usuários e impulsionam o sucesso dos
                                            negócios.</p>
                                    </div>
                                </div>
                                <!-- Content Block - Newsletter Section Start -->
                                <div class="content__block grid-block">
                                    <div class="grid-container">
                                        <!-- Newsletter 1 -->
                                        <section id="newsletter">
                                            <div class="container">
                                                <article class="newsletter animate-in-up">
                                                    <!-- Imagem -->
                                                    <div class="image-wrapper">
                                                        <img src="img/works/ProductionApp/Capa.png" alt="Descrição da imagem" />
                                                    </div>

                                                    <!-- Conteúdo -->
                                                    <div class="content-wrapper">
                                                        <!-- Title -->
                                                        <div class="title-wrapper">
                                                            <span class="title p">Lighthouse App</span>
                                                        </div>

                                                        <!-- Tags -->
                                                        <div class="tags">
                                                             <span class="tag vale">Vale</span>
                                                             <span class="tag">Mobile App</span>
                                                             <span class="tag">Data Visualization</span>
                                                             <span class="tag">Design System</span>
                                                             <span class="tag">MVP</span>
                                                             <span class="tag">UX/UI Design</span>
                                                        </div>

                                                        <!-- Description -->
                                                        <div class="description">
                                                            Lighthouse é um aplicativo mobile desenvolvido para a Vale que revoluciona o monitoramento de produção em tempo real. Oferece visualização de dados dinâmicos, sistema de notificações personalizáveis e comunicação eficiente entre equipes, permitindo tomadas de decisão ágeis e precisas. Uma solução mobile-first desenvolvida como MVP para modernizar sistemas legados e otimizar a gestão operacional.                                                        </div>

                                                        <!-- Button -->
                                                        <a href="lighthouse-app.html" class="btn btn-line icon-right slide-right">
                                                            <span class="btn-caption">Ver projeto</span>
                                                            <i class="ph ph-arrow-right"></i>
                                                        </a>
                                                    </div>
                                                </article>
                                            </div>
                                        </section>

                                        <!-- Newsletter 2 -->
                                        <section id="newsletter">
                                            <div class="container">
                                                <article class="newsletter newsletter-reverse animate-in-up">
                                                    <!-- Copiar estrutura do primeiro newsletter -->
                                                    <div class="image-wrapper">
                                                        <img src="img/works/Sports/Capa.png" alt="Descrição da imagem" />
                                                    </div>
                                                    <div class="content-wrapper">
                                                        <div class="title-wrapper">
                                                            <span class="title p">Sports Experience - Copa do mundo Qatar</span>
                                                        </div>
                                                        <div class="tags">
                                                            <span class="tag direcTV">DirecTV</span>
                                                            <span class="tag">Multiplatform</span>
                                                            <span class="tag">Statistics</span>
                                                            <span class="tag">Design System</span>
                                                            <span class="tag">Real-time Data</span>
                                                            <span class="tag">UX/UI Design</span>
                                                            <span class="tag">Product Design</span>
                                                        </div>
                                                        <div class="description">
                                                            O Sports Experience é um hub multiplataforma desenvolvido para a DirecTV durante a Copa do Mundo 2022 no Qatar. O projeto oferece transmissão de jogos ao vivo, estatísticas em tempo real, e acompanhamento completo do torneio através de mobile, web e TV. Com versões específicas para Brasil e América Latina, o sistema integra dados oficiais da FIFA para proporcionar uma experiência imersiva e completa e em tempo real aos usuários da DirecTV GO.
                                                        </div>
                                                        <a href="Sportsexp.html" class="btn btn-line icon-right slide-right">
                                                            <span class="btn-caption">Ver projeto</span>
                                                            <i class="ph ph-arrow-right"></i>
                                                        </a>
                                                    </div>
                                                </article>
                                            </div>
                                        </section>
                                    </div>
                                </div>
                                <!-- Content Block - Newsletter Section End -->

                                <!-- Content Block - Portfolio Gallery Masonry Grid Start -->
                                <div class="content__block grid-block">
                                    <div class="container-fluid px-0 inner__gallery">

                                        <!-- Portfolio Gallery Start -->
                                        <div class="row gx-0 my-gallery" itemscope
                                            itemtype="http://schema.org/ImageGallery"
                                            data-masonry='{"percentPosition": true, "itemSelector": ".grid-item", "columnWidth": ".grid-sizer"}'>
                                            <!-- grid sizer element for masonry calculations -->
                                            <div class="grid-sizer col-md-6"></div>
                                            <!-- portfolio gallery single item -->
                                            <figure class="col-12 col-md-6 gallery__item grid-item animate-card-2"
                                                itemprop="associatedMedia" itemscope
                                                itemtype="http://schema.org/ImageObject">
                                                <a href="img/works/Sirio.png"
                                                    data-image="img/works/Sirio.png"
                                                    class="gallery__link" itemprop="contentUrl" data-size="1400x1400">
                                                    <img src="img/works/Sirio.png"
                                                        class="gallery__image" itemprop="thumbnail"
                                                        alt="Image description">
                                                </a>
                                                <figcaption class="gallery__descr" itemprop="caption description">
                                                    <h5>Aplicativo Sírio-libanês
                                                        <small>Mobile App - UI/UX Design</small>
                                                    </h5>
                                                </figcaption>
                                            </figure>
                                            <!-- portfolio gallery single item -->
                                            <figure class="col-12 col-md-6 gallery__item grid-item animate-card-2"
                                                itemprop="associatedMedia" itemscope
                                                itemtype="http://schema.org/ImageObject">
                                                <a href="img/works/valenews.png"
                                                    data-image="img/works/valenews.png"
                                                    class="gallery__link" itemprop="contentUrl" data-size="1100x1400">
                                                    <img src="img/works/valenews.png"
                                                        class="gallery__image" itemprop="thumbnail"
                                                        alt="Image description">
                                                </a>
                                                <figcaption class="gallery__descr opposite"
                                                    itemprop="caption description">
                                                    <h5 class="opposite">Vale News
                                                        <small>Mobile App - UI/UX Design</small>
                                                    </h5>
                                                </figcaption>
                                            </figure>
                                            <!-- portfolio gallery single item -->
                                            <figure class="col-12 col-md-6 gallery__item grid-item animate-card-2"
                                                itemprop="associatedMedia" itemscope
                                                itemtype="http://schema.org/ImageObject">
                                                <a href="img/works/Cracha.png"
                                                    data-image="img/works/Cracha.png"
                                                    class="gallery__link" itemprop="contentUrl" data-size="1400x1400">
                                                    <img src="img/works/Cracha.png"
                                                        class="gallery__image" itemprop="thumbnail"
                                                        alt="Image description">
                                                </a>
                                                <figcaption class="gallery__descr opposite"
                                                    itemprop="caption description">
                                                    <h5 class="opposite">Vale - Leap to the new branding
                                                        <small>Múltiplas mídias gráficas digitais e físicas.</small>
                                                    </h5>
                                                </figcaption>
                                            </figure>
                                            <!-- portfolio gallery single item -->
                                            <figure class="col-12 col-md-6 gallery__item grid-item animate-card-2"
                                                itemprop="associatedMedia" itemscope
                                                itemtype="http://schema.org/ImageObject">
                                                <a href="img/works/Data.png"
                                                    data-image="img/works/Data.png"
                                                    class="gallery__link" itemprop="contentUrl" data-size="1400x1400">
                                                    <img src="img/works/Data.png"
                                                        class="gallery__image" itemprop="thumbnail"
                                                        alt="Image description">
                                                </a>
                                                <figcaption class="gallery__descr " itemprop="caption description">
                                                    <h5>Vale PowerBI Dashboard
                                                        <small>PowerBI - UI/UX Design</small>
                                                    </h5>
                                                </figcaption>
                                            </figure>
                                            <!-- portfolio gallery single item -->
                                            <figure class="col-12 col-md-6 gallery__item grid-item animate-card-2"
                                                itemprop="associatedMedia" itemscope
                                                itemtype="http://schema.org/ImageObject">
                                                <a href="img/works/AI.png"
                                                    data-image="img/works/AI.png"
                                                    class="gallery__link" itemprop="contentUrl" data-size="1400x1400">
                                                    <img src="img/works/AI.png"
                                                        class="gallery__image" itemprop="thumbnail"
                                                        alt="Image description">
                                                </a>
                                                <figcaption class="gallery__descr" itemprop="caption description">
                                                    <h5>Accenture - Múltiplos sistemas de AI
                                                        <small> AI - UI/UX Design</small>
                                                    </h5>
                                                </figcaption>
                                            </figure>
                                        </div>
                                        <!-- Portfolio Gallery End -->

                                    </div>
                                </div>
                                <!-- Content Block - Portfolio Gallery Masonry Grid End -->

                            </div>
                        </div>
                        <!-- Inner Section Content End -->

                        <!-- Inner Section Aside Start -->
                        <div class="col-12 col-xl-2"></div>
                        <!-- Inner Section Aside End -->

                    </div>
                </div>
            </div>
        </section>
        <!-- Inner Section - Portfolio - End -->

        <!-- Inner Section - About - Start -->
        <!-- About Section Start -->
        <section id="about" class="inner inner-grid-bottom about">
            <div class="inner__wrapper">
                <div class="container-fluid p-0">
                    <div class="row g-0">

                        <!-- Inner Section Name Start -->
                        <div class="col-12 col-xl-2">
                            <div class="inner__name">

                                <!-- Content Block - Section Name Start -->
                                <div class="content__block name-block">
                                    <span class="section-name icon-right animate-in-up">
                                        <span class="section-name-caption">Sobre</span>
                                        <i class="ph ph-arrow-down-right"></i>
                                    </span>
                                </div>
                                <!-- Content Block - Section Name Start -->

                            </div>
                        </div>
                        <!-- Inner Section Name End -->

                        <!-- Inner Section Content Start -->
                        <div class="col-12 col-xl-8">
                            <div class="inner__content">

                                <!-- Content Block - H2 Section Title Start -->
                                <div class="content__block section-grid-title">
                                    <div class="block__descr">
                                        <h2 class="reveal-type animate-in-up">Conheça-me<br>um pouco melhor</h2>
                                    </div>
                                </div>
                                <!-- Content Block - H2 Section Title End -->

                                <!-- Content Block - Image Divider Start -->
                                <div class="content__block grid-block">
                                    <div class="container-fluid p-0">
                                        <div class="row g-0">
                                            <div class="col-12 grid-item">
                                                <!-- change the background image in the main.css file - .about-image-1 -->
                                                <div class="divider divider-image about-image-1 animate-in-up"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- Content Block - Image Divider End -->

                                <!-- Content Block - About Me Data Start -->
                                <div class="content__block grid-block ">
                                    <div class="container-fluid p-0">
                                        <div class="row g-0 justify-content-between">

                                            <div
                                                class="col-12 col-md-8 col-lg-7 col-xxl-9 grid-item about-descr pre-grid">
                                                <p class="about-descr__text type-basic-160lh animate-in-up">Designer de
                                                    Produto Especialista com
                                                    13 anos de experiência, combinando criatividade e estratégia para
                                                    transformar ideias em
                                                    produtos inovadores e funcionais. Sou especializado em UI e UX, com
                                                    foco em prototipação
                                                    eficiente, o que me permite
                                                    criar soluções que realmente atendem às necessidades dos usuários e
                                                    clientes.<br>
                                                    <br>
                                                    Ao longo da minha carreira, liderei equipes multidisciplinares e
                                                    participei de projetos
                                                    desafiadores para grandes empresas como
                                                    Vale e Accenture, incluindo o desenvolvimento de protótipos para
                                                    produtos com inteligência
                                                    artificial e soluções internas que
                                                    otimizam processos. Minha abordagem é focada na colaboração, gestão
                                                    eficiente de projetos,
                                                    pesquisa, testes, prototipação,
                                                    desenvolvimento e na entrega de resultados que geram impacto real ao
                                                    usuário.
                                                </p>
                                                <div class="btn-group about-descr__btnholder animate-in-up">
                                                    <a class="btn btn-default hover-default"
                                                        href="Lucas Schoenherr CV.pdf">
                                                        <em></em>
                                                        <span class="btn-caption">
                                                            Visualizar Currículo
                                                        </span>
                                                    </a>
                                                    &nbsp;&nbsp;
                                                    <a class="btn btn-default hover-default"
                                                        href="Currículo-EN.pdf">
                                                        <em></em>
                                                        <span class="btn-caption">
                                                            View Resume in English
                                                        </span>
                                                    </a>
                                                </div>
                                            </div>

                                            <div class="col-12 col-md-4 col-xxl-3 grid-item about-info pre-grid">
                                                <div class="about-info__item animate-in-up">
                                                    <h6>Lucas Schoenherr</h6>
                                                </div>
                                                <div class="about-info__item animate-in-up">
                                                    <h6>
                                                        <a class="link-inline text-link" href="tel:+12127089400">+55
                                                            (21) 98800-5234</a>
                                                    </h6>
                                                </div>
                                                <div class="about-info__item animate-in-up">
                                                    <h6>
                                                        <a class="link-inline text-link"
                                                            href="mailto:<EMAIL>?subject=Message%20from%20your%20site"><EMAIL></a>
                                                    </h6>
                                                </div>
                                                <div class="about-info__item animate-in-up">
                                                    <h6>
                                                        <a class="link-inline text-link"
                                                            href="https://maps.app.goo.gl/xMJXTEUeHkv6kYRQ6"
                                                            target="_blank">Rio de Janeiro, Brasil</a>
                                                    </h6>
                                                </div>
                                            </div>

                                        </div>
                                    </div>
                                </div>
                                <!-- Content Block - About Me Data End -->

                            </div>
                        </div>
                        <!-- Inner Section Content End -->

                        <!-- Inner Section Aside Start -->
                        <div class="col-12 col-xl-2"></div>
                        <!-- Inner Section Aside End -->

                    </div>
                </div>
            </div>
        </section>
        <!-- Inner Section - About - End -->

        <!-- Inner Section - Services - Start -->
        <section id="services" class="inner inner-stack-bottom services">
            <div class="inner__wrapper">
                <div class="container-fluid p-0">
                    <div class="row g-0">

                        <!-- Inner Section Name Start -->
                        <div class="col-12 col-xl-2">
                            <div class="inner__name">

                                <!-- Content Block - Section Name Start -->
                                <div class="content__block name-block">
                                    <span class="section-name icon-right animate-in-up">
                                        <span class="section-name-caption">Especialidades</span>
                                        <i class="ph ph-arrow-down-right"></i>
                                    </span>
                                </div>
                                <!-- Content Block - Section Name Start -->

                            </div>
                        </div>
                        <!-- Inner Section Name End -->

                        <!-- Inner Section Content Start -->
                        <div class="col-12 col-xl-8">
                            <div class="inner__content">

                                <!-- Content Block - H2 Section Title Start -->
                                <div class="content__block pre-stack-text-block">
                                    <div class="block__descr">
                                        <h2 class="reveal-type animate-in-up">Soluções de Design <br>que Impulsionam
                                            Resultados</h2>
                                    </div>
                                </div>
                                <!-- Content Block - H2 Section Title End -->

                                <!-- Content Block - Services/Features Stacking Cards Block Start -->
                                <div class="content__block">
                                    <div class="stack-wrapper">
                                        <div class="stack-offset"></div>
                                        <div class="services-stack">
                                            <!-- services stack single item -->
                                            <div class="stack-item">
                                                <div class="services-stack__inner">
                                                    <div class="services-stack__title">
                                                        <h3>UI<br>Design de interfaces</h3>
                                                    </div>
                                                    <div class="services-stack__descr">
                                                        <i class="ph-thin ph-bezier-curve"></i>
                                                        <p class="services-stack__text type-basic-160lh">Desenvolvo
                                                            interfaces intuitivas que
                                                            encantam e geram resultados.</p>
                                                    </div>
                                                    <div class="services-stack__image">
                                                        <img class="service-img service-img-s"
                                                            src="img/services/UI5.webp" alt="Service/Feature Image">
                                                        <img class="service-img service-img-m"
                                                            src="img/services/UI5.webp" alt="Service/Feature Image">
                                                    </div>
                                                </div>
                                            </div>
                                            <!-- services stack single item -->
                                            <div class="stack-item">
                                                <div class="services-stack__inner">
                                                    <div class="services-stack__title">
                                                        <h3>UX<br>Experiência do usuário</h3>
                                                    </div>
                                                    <div class="services-stack__descr">
                                                        <i class="ph-thin ph-code"></i>
                                                        <p class="services-stack__text type-basic-160lh">Desenvolvendo
                                                            jornadas de usuário fluidas
                                                            que aumentam a satisfação e retenção.
                                                        </p>
                                                    </div>
                                                    <div class="services-stack__image">
                                                        <img class="service-img service-img-s"
                                                            src="img/services/UX.webp" alt="Service/Feature Image">
                                                        <img class="service-img service-img-m"
                                                            src="img/services/UX.webp" alt="Service/Feature Image">
                                                    </div>
                                                </div>
                                            </div>
                                            <!-- services stack single item -->
                                            <div class="stack-item">
                                                <div class="services-stack__inner">
                                                    <div class="services-stack__title">
                                                        <h3>DS<br>Design Systems</h3>
                                                    </div>
                                                    <div class="services-stack__descr">
                                                        <i class="ph-thin ph-cube"></i>
                                                        <p class="services-stack__text type-basic-160lh">Construindo
                                                            sistemas de design escaláveis
                                                            para manter consistência e agilizar o desenvolvimento da sua
                                                            marca.</p>
                                                    </div>
                                                    <div class="services-stack__image">
                                                        <img class="service-img service-img-s"
                                                            src="img/services/DS.webp" alt="Service/Feature Image">
                                                        <img class="service-img service-img-m"
                                                            src="img/services/DS.webp" alt="Service/Feature Image">
                                                    </div>
                                                </div>
                                            </div>
                                            <!-- services stack single item -->
                                            <div class="stack-item">
                                                <div class="services-stack__inner">
                                                    <div class="services-stack__title">
                                                        <h3>Liderança<br>& Gerenciamento</h3>
                                                    </div>
                                                    <div class="services-stack__descr">
                                                        <i class="ph-thin ph-graph"></i>
                                                        <p class="services-stack__text type-basic-160lh">Lidero equipes
                                                            para entregar design
                                                            inovador e soluções que superam expectativas</p>
                                                    </div>
                                                    <div class="services-stack__image">
                                                        <img class="service-img service-img-s" src="img/services/L.webp"
                                                            alt="Service/Feature Image">
                                                        <img class="service-img service-img-m" src="img/services/L.webp"
                                                            alt="Service/Feature Image">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- Content Block - Services/Features Stacking Cards Block End -->

                            </div>
                        </div>
                        <!-- Inner Section Content End -->

                        <!-- Inner Section Aside Start -->
                        <div class="col-12 col-xl-2"></div>
                        <!-- Inner Section Aside End -->

                    </div>
                </div>
            </div>
        </section>
        <!-- Inner Section - Services - End -->

        <!-- Inner Section - Resume - Start -->
        <section id="resume" class="inner inner-grid-bottom resume">
            <div class="inner__wrapper">
                <div class="container-fluid p-0">
                    <div class="row g-0">

                        <!-- Inner Section Name Start -->
                        <div class="col-12 col-xl-2">
                            <div class="inner__name">

                                <!-- Content Block - Section Name Start -->
                                <div class="content__block name-block">
                                    <span class="section-name icon-right animate-in-up">
                                        <span class="section-name-caption">curriculum</span>
                                        <i class="ph ph-arrow-down-right"></i>
                                    </span>
                                </div>
                                <!-- Content Block - Section Name Start -->

                            </div>
                        </div>
                        <!-- Inner Section Name End -->

                        <!-- Inner Section Content Start -->
                        <div class="col-12 col-xl-8">
                            <div class="inner__content">

                                <!-- Content Block - H2 Section Title Start -->
                                <div class="content__block section-tagline-title">
                                    <div class="block__descr">
                                        <h2 class="reveal-type animate-in-up">Minha jornada<br>digital</h2>
                                    </div>
                                </div>

                                <!-- Content Block - H2 Section Title End -->

                                <!-- Content Block - Work Experience Start -->
                                <div class="content__block pre-text-items">

                                    <!-- Section Subtitle Start -->
                                    <div class="block__subtitle">
                                        <p class="tagline-chapter animate-in-up">Experiência</p>
                                    </div>
                                    <!-- Section Subtitle End -->

                                    <!-- Resume Lines V1 Start -->
                                    <div class="container-fluid p-0 resume-lines">
                                        <div class="resume-divider animate-in-up"></div>
                                        <!-- resume single item -->
                                        <div class="row g-0 resume-lines__item">
                                            <div class="col-12 col-md-4 col-lg-2">
                                                <p class="resume-lines__date type-basic-160lh animate-in-up">2022 -
                                                    Abr 2024</p>
                                            </div>
                                            <div class="col-12 col-md-4 col-lg-5">
                                                <h4 class="resume-lines__title animate-in-up">Designer de Produto
                                                    Especialista (UI/UX)</h4>
                                                <p class="resume-lines__source small animate-in-up">
                                                    em
                                                    <a class="link-small-underline" href="#0" target="_blank">Accenture
                                                        - One Studio</a>
                                                </p>
                                            </div>
                                            <div class="col-12 col-md-4 col-lg-5">
                                                <ul class="resume-lines__descr type-basic-160lh animate-in-up">
                                                    <li>Lead designer end-to-end em múltiplos desenvolvimentos de projetos utilizando LLMs in-house na VIVO.</li>
                                                    <li>Projetos para hospitais e instituições de saúde.</li>
                                                    <li>Colaboração no projeto de transmissão da Copa do Mundo com a FIFA.</li>
                                                    <li>Projetos voltados para a segurança de barragens e minas para a Vale.</li>
                                                    <li>Participação em diversos projetos de relevância para a accenture.</li>
                                                    <li>Experiência individual e colaborativa na entrega de protótipos e provas de conceito (POCs) em diferentes áreas da empresa, tanto nacional quanto internacionalmente.</li>
                                                </ul>
                                            </div>
                                        </div>
                                        <div class="resume-divider animate-in-up"></div>
                                        <!-- resume single item -->
                                        <div class="row g-0 resume-lines__item">
                                            <div class="col-12 col-md-4 col-lg-2">
                                                <p class="resume-lines__date type-basic-160lh animate-in-up">2020 - 2022
                                                </p>
                                            </div>
                                            <div class="col-12 col-md-4 col-lg-5">
                                                <h4 class="resume-lines__title animate-in-up">Designer de Produto Sênior
                                                    (UI/UX)</h4>
                                                <p class="resume-lines__source small animate-in-up">
                                                    em
                                                    <a class="link-small-underline" href="#0" target="_blank">Accenture
                                                        - Vale Digital Lab</a>
                                                </p>
                                            </div>
                                            <div class="col-12 col-md-4 col-lg-5">
                                                <ul class="resume-lines__descr type-basic-160lh animate-in-up">
                                                    <li>Liderança e gerenciamento do time de design do Digital Lab.</li>
                                                    <li>Desenvolvimento de um sistema de pesquisa baseado em diversas metodologias de UX design</li>
                                                    <li>Aprimoramento da esteira de entrega para protótipos e POCs.</li>
                                                    <li>Condução de projetos variados relacionados à Vale, incluindo:
                                                        <ul class="resume-lines__nested">
                                                            <li>Interfaces para controle robótico</li>
                                                            <li>Segurança on-site</li>
                                                            <li>Prevenção de quarentena</li>
                                                            <li>Processos financeiros</li>
                                                            <li>Otimização de rotas</li>
                                                        </ul>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                        <div class="resume-divider animate-in-up"></div>
                                        <!-- resume single item -->
                                        <div class="row g-0 resume-lines__item">
                                            <div class="col-12 col-md-4 col-lg-2">
                                                <p class="resume-lines__date type-basic-160lh animate-in-up">2019 - 2020
                                                </p>
                                            </div>
                                            <div class="col-12 col-md-4 col-lg-5">
                                                <h4 class="resume-lines__title animate-in-up">Analista de Sistemas
                                                    Sênior</h4>
                                                <p class="resume-lines__source small animate-in-up">
                                                    em
                                                    <a class="link-small-underline" href="#0" target="_blank">Infobase
                                                        TI</a>
                                                </p>
                                            </div>
                                            <div class="col-12 col-md-4 col-lg-5">
                                                <ul class="resume-lines__descr type-basic-160lh animate-in-up">
                                                    <li>Pesquisa e prototipação de múltiplos projetos voltados para TI, variando de serviços, portais, aplicativos mobile, aplicativos Desktop, entre outros.</li>
                                                    <li>Melhoria e reestruturação da experiência do usuário para o sistema de transição de serviços em projetos da Vale.</li>
                                                    <li>Criação do design para o portal de acesso da Vale.</li>
                                                    <li>Desenvolvimento de um design system baseado no branding da Vale para garantir consistência em projetos.</li>
                                                </ul>
                                            </div>
                                        </div>
                                        <div class="resume-divider animate-in-up"></div>
                                        <!-- resume single item -->
                                        <div class="row g-0 resume-lines__item">
                                            <div class="col-12 col-md-4 col-lg-2">
                                                <p class="resume-lines__date type-basic-160lh animate-in-up">2011 - 2019
                                                </p>
                                            </div>
                                            <div class="col-12 col-md-4 col-lg-5">
                                                <h4 class="resume-lines__title animate-in-up">Outras Experiências</h4>
                                                <p class="resume-lines__source small animate-in-up">
                                                    em
                                                    <a class="link-small-underline" href="#0" target="_blank">Múltiplas
                                                        empresas.</a>
                                                </p>
                                            </div>
                                            <div class="col-12 col-md-4 col-lg-5">
                                                <ul class="resume-lines__descr type-basic-160lh animate-in-up">
                                                    <li>Freelancer - Designer de Arquitetura e Cenários / Designer Gráfico.</li>
                                                    <li>PTT Eletronuclear - Técnico de informática em áreas controladas.</li>
                                                    <li>Interart - Brasil Designer Modelador 3D e Cenógrafo.</li>
                                                    <li>Freelancer - Internacional Designer de Marketing e Arquitetura</li>
                                                    <li>Fidelity - Brasil Estagiário em Design Gráfico e TI.</li>
                                                </ul>
                                            </div>
                                        </div>
                                        <div class="resume-divider animate-in-up"></div>
                                    </div>

                                    <!-- Resume Lines V1 End -->

                                </div>
                                <!-- Content Block - Work Experience End -->

                                <!-- Content Block - My Education Start -->
                                <div class="content__block pre-text-items">

                                    <!-- Section Subtitle Start -->
                                    <div class="block__subtitle">
                                        <p class="tagline-chapter animate-in-up">Educação</p>
                                    </div>
                                    <!-- Section Subtitle End -->

                                    <!-- Resume Lines V1 Start -->
                                    <div class="container-fluid p-0 resume-lines">
                                        <div class="resume-divider animate-in-up"></div>
                                        <!-- resume single item -->
                                        <div class="row g-0 resume-lines__item">
                                            <div class="col-12 col-md-4 col-lg-2">
                                                <p class="resume-lines__date type-basic-160lh animate-in-up">2022</p>
                                            </div>
                                            <div class="col-12 col-md-4 col-lg-5">
                                                <h4 class="resume-lines__title animate-in-up">User Experience Design and
                                                    Beyond (UX/UI) </h4>
                                            </div>
                                            <div class="col-12 col-md-4 col-lg-5">
                                                <p class="resume-lines__descr type-basic-160lh animate-in-up">PUC RS Pós
                                                    Graduação</p>
                                            </div>
                                        </div>
                                        <div class="resume-divider animate-in-up"></div>
                                        <!-- resume single item -->
                                        <div class="row g-0 resume-lines__item">
                                            <div class="col-12 col-md-4 col-lg-2">
                                                <p class="resume-lines__date type-basic-160lh animate-in-up">2014</p>
                                            </div>
                                            <div class="col-12 col-md-4 col-lg-5">
                                                <h4 class="resume-lines__title animate-in-up">Cambridge Advance English
                                                    Exam and Classes.</h4>
                                            </div>
                                            <div class="col-12 col-md-4 col-lg-5">
                                                <p class="resume-lines__descr type-basic-160lh animate-in-up">Cambridge
                                                    Certificate Training
                                                    Center.
                                                </p>
                                            </div>
                                        </div>
                                        <div class="resume-divider animate-in-up"></div>
                                        <!-- resume single item -->
                                        <div class="row g-0 resume-lines__item">
                                            <div class="col-12 col-md-4 col-lg-2">
                                                <p class="resume-lines__date type-basic-160lh animate-in-up">2013</p>
                                            </div>
                                            <div class="col-12 col-md-4 col-lg-5">
                                                <h4 class="resume-lines__title animate-in-up">3D para Interiores e
                                                    Cenários / Design Gráfico
                                                    para
                                                    Marketing Digital. </h4>
                                            </div>
                                            <div class="col-12 col-md-4 col-lg-5">
                                                <p class="resume-lines__descr type-basic-160lh animate-in-up">
                                                    Universidade Veiga de Almeida.
                                                </p>
                                            </div>
                                        </div>
                                        <div class="resume-divider animate-in-up"></div>
                                        <!-- resume single item -->
                                        <div class="row g-0 resume-lines__item">
                                            <div class="col-12 col-md-4 col-lg-2">
                                                <p class="resume-lines__date type-basic-160lh animate-in-up">2012</p>
                                            </div>
                                            <div class="col-12 col-md-4 col-lg-5">
                                                <h4 class="resume-lines__title animate-in-up">Design Gráfico, Ilustração
                                                    e Animação.</h4>
                                            </div>
                                            <div class="col-12 col-md-4 col-lg-5">
                                                <p class="resume-lines__descr type-basic-160lh animate-in-up">
                                                    Universidade Veiga de Almeida.
                                                </p>
                                            </div>
                                        </div>
                                        <div class="resume-divider animate-in-up"></div>
                                    </div>
                                    <!-- Resume Lines V1 End -->
                                </div>
                                <!-- Content Block - My Education End -->

                                <!-- Content Block - Language Start -->
                                <div class="content__block pre-text-items">

                                    <!-- Section Subtitle Start -->
                                    <div class="block__subtitle">
                                        <p class="tagline-chapter animate-in-up">Linguas</p>
                                    </div>
                                    <!-- Section Subtitle End -->

                                    <!-- Resume Lines V1 Start -->
                                    <div class="container-fluid p-0 resume-lines">
                                        <div class="resume-divider animate-in-up"></div>
                                        <!-- resume single item -->
                                        <div class="row g-0 resume-lines__item">
                                            <div class="col-12 col-md-4 col-lg-2">
                                                <p class="resume-lines__date type-basic-160lh animate-in-up">
                                                    &#x1F1E7;&#x1F1F7;</p>
                                            </div>
                                            <div class="col-12 col-md-4 col-lg-5">
                                                <h4 class="resume-lines__title animate-in-up">Português</h4>
                                            </div>
                                            <div class="col-12 col-md-4 col-lg-5">
                                                <p class="resume-lines__descr type-basic-160lh animate-in-up">Nativo
                                                </p>
                                            </div>
                                        </div>
                                        <div class="resume-divider animate-in-up"></div>
                                        <!-- resume single item -->
                                        <div class="row g-0 resume-lines__item">
                                            <div class="col-12 col-md-4 col-lg-2">
                                                <p class="resume-lines__date type-basic-160lh animate-in-up">
                                                    &#x1F1FA;&#x1F1F8;</p>
                                            </div>
                                            <div class="col-12 col-md-4 col-lg-5">
                                                <h4 class="resume-lines__title animate-in-up">Inglês</h4>
                                            </div>
                                            <div class="col-12 col-md-4 col-lg-5">
                                                <p class="resume-lines__descr type-basic-160lh animate-in-up">Fluente
                                                </p>
                                            </div>
                                        </div>
                                        <div class="resume-divider animate-in-up"></div>
                                        <!-- resume single item -->
                                        <div class="row g-0 resume-lines__item">
                                            <div class="col-12 col-md-4 col-lg-2">
                                                <p class="resume-lines__date type-basic-160lh animate-in-up">
                                                    &#x1F1EA;&#x1F1F8;</p>
                                            </div>
                                            <div class="col-12 col-md-4 col-lg-5">
                                                <h4 class="resume-lines__title animate-in-up">Espanhol</h4>
                                            </div>
                                            <div class="col-12 col-md-4 col-lg-5">
                                                <p class="resume-lines__descr type-basic-160lh animate-in-up">
                                                    Intermadiário</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="resume-divider animate-in-up"></div>
                                </div>
                                <!-- Resume Lines V1 End -->
                            </div>
                            <!-- Content Block - Language End -->

                            <!-- Content Block - Tools Cards Start -->
                            <div class="content__block grid-block pre-text-items">

                                <!-- Section Subtitle Start -->
                                <div class="block__subtitle grid-block-subtitle">
                                    <p class="tagline-chapter animate-in-up">Minhas ferramentas favoritas</p>
                                </div>
                                <!-- Section Subtitle End -->

                                <!-- Tools Cards Start -->
                                <div class="tools-cards d-flex justify-content-start flex-wrap">
                                    <!-- tools single item -->
                                    <div class="tools-cards__item d-flex grid-item animate-card-4">
                                        <div class="tools-cards__card">
                                            <img class="tools-cards__icon animate-in-up" src="img/icons/icon-figma.svg"
                                                alt="Tools Icon">
                                            <h6 class="tools-cards__caption tagline-tool animate-in-up">Figma</h6>
                                        </div>
                                    </div>
                                    <!-- tools single item -->
                                    <div class="tools-cards__item d-flex grid-item animate-card-4">
                                        <div class="tools-cards__card">
                                            <img class="tools-cards__icon animate-in-up"
                                                src="img/icons/icon-photoshop.svg" alt="Tools Icon">
                                            <h6 class="tools-cards__caption tagline-tool animate-in-up">Photoshop</h6>
                                        </div>
                                    </div>
                                    <!-- tools single item -->
                                    <div class="tools-cards__item d-flex grid-item animate-card-4">
                                        <div class="tools-cards__card">
                                            <img class="tools-cards__icon animate-in-up" src="img/icons/Figjam.png"
                                                alt="Tools Icon">
                                            <h6 class="tools-cards__caption tagline-tool animate-in-up">FigJam</h6>
                                        </div>
                                    </div>
                                    <!-- tools single item -->
                                    <div class="tools-cards__item d-flex grid-item animate-card-4">
                                        <div class="tools-cards__card">
                                            <img class="tools-cards__icon animate-in-up" src="img/icons/Group 5.png"
                                                alt="Tools Icon">
                                            <h6 class="tools-cards__caption tagline-tool animate-in-up">Design Systems
                                            </h6>
                                        </div>
                                    </div>
                                    <!-- tools single item -->
                                    <div class="tools-cards__item d-flex grid-item animate-card-4">
                                        <div class="tools-cards__card">
                                            <img class="tools-cards__icon animate-in-up" src="img/icons/Maze.png"
                                                alt="Tools Icon">
                                            <h6 class="tools-cards__caption tagline-tool animate-in-up">Maze</h6>
                                        </div>
                                    </div>
                                    <!-- tools single item -->
                                    <div class="tools-cards__item d-flex grid-item animate-card-4">
                                        <div class="tools-cards__card">
                                            <img class="tools-cards__icon animate-in-up" src="img/icons/mural3.png"
                                                alt="Tools Icon">
                                            <h6 class="tools-cards__caption tagline-tool animate-in-up">Mural</h6>
                                        </div>
                                    </div>
                                    <!-- tools single item -->
                                    <div class="tools-cards__item d-flex grid-item animate-card-4">
                                        <div class="tools-cards__card">
                                            <img class="tools-cards__icon animate-in-up"
                                                src="img/icons/Azure Devops.svg" alt="Tools Icon">
                                            <h6 class="tools-cards__caption tagline-tool animate-in-up">Azure Devops
                                            </h6>
                                        </div>
                                    </div>
                                    <!-- tools single item -->
                                    <div class="tools-cards__item d-flex grid-item animate-card-4">
                                        <div class="tools-cards__card">
                                            <img class="tools-cards__icon animate-in-up" src="img/icons/icon-notion.svg"
                                                alt="Tools Icon">
                                            <h6 class="tools-cards__caption tagline-tool animate-in-up">Notion</h6>
                                        </div>
                                    </div>
                                </div>
                                <!-- Tools Cards End -->

                            </div>
                            <!-- Content Block - Tools Cards End -->

                        </div>
                    </div>
                    <!-- Inner Section Content End -->

                    <!-- Inner Section Aside Start -->
                    <div class="col-12 col-xl-2"></div>
                    <!-- Inner Section Aside End -->

                </div>
            </div>

            </div>
        </section>
        <!-- Inner Section - Resume - End -->

        <!-- Inner Section - Contact - Start -->
        <section id="contact" class="inner contact inner-grid-bottom no-padding-bottom">
            <div class="inner__wrapper">
                <div class="container-fluid p-0">
                    <div class="row g-0">

                        <!-- Inner Section Name Start -->
                        <div class="col-12 col-xl-2">
                            <div class="inner__name">

                                <!-- Content Block - Section Name Start -->
                                <div class="content__block name-block">
                                    <span class="section-name icon-right animate-in-up">
                                        <span class="section-name-caption">Contato</span>
                                        <i class="ph ph-arrow-down-right"></i>
                                    </span>
                                </div>
                                <!-- Content Block - Section Name Start -->

                            </div>
                        </div>
                        <!-- Inner Section Name End -->

                        <!-- Inner Section Content Start -->
                        <div class="col-12 col-xl-8">
                            <div class="inner__content">

                                <!-- Content Block - H2 Section Title Start -->
                                <div class="content__block grid-block">
                                    <div class="block__descr">
                                        <h2 class="reveal-type animate-in-up">Vamos Conversar!</h2>
                                        <p class="h2__text text-twothirds type-basic-160lh animate-in-up">Interessado em
                                            me ter na sua
                                            equipe? Deixe-me uma mensagem e retornarei o mais rápido possível.</p>
                                    </div>
                                </div>
                                <!-- Content Block - H2 Section Title End -->
                                <!-- Content Block - Socials Cards Start -->
                                <div class="content__block grid-block">
                                    <ul class="socials-cards d-flex justify-content-start flex-wrap">
                                        <!-- socials cards single item -->
                                        <li class="socials-cards__item grid-item d-flex animate-card-5">
                                            <a class="socials-cards__link d-flex align-items-center justify-content-center"
                                                href="mailto:<EMAIL>?subject=Mensagem%do%portifólio"
                                                target="_blank">
                                                <i class="ph ph-envelope"></i>
                                            </a>
                                        </li>
                                        <!-- socials cards single item -->
                                        <li class="socials-cards__item grid-item d-flex animate-card-5">
                                            <a class="socials-cards__link d-flex align-items-center justify-content-center"
                                                href="https://www.linkedin.com/in/lucas-scho/" target="_blank">
                                                <em></em>
                                                <i class="ph ph-linkedin-logo"></i>
                                            </a>
                                        </li>
                                        <!-- socials cards single item -->
                                        <li class="socials-cards__item grid-item d-flex animate-card-5">
                                            <a class="socials-cards__link d-flex align-items-center justify-content-center"
                                                href="https://www.instagram.com/lucaslsm" target="_blank">
                                                <em></em>
                                                <i class="ph ph-instagram-logo"></i>
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                                <!-- Content Block - Socials Cards End -->
                            </div>
                        </div>
                        <!-- Inner Section Content End -->

                        <!-- Inner Section Aside Start -->
                        <div class="col-12 col-xl-2"></div>
                        <!-- Inner Section Aside End -->

                    </div>
                </div>

                <!-- Inner Section Off-canvas Content (Footer Marquee Block) Start -->
                <div class="footer footer-marquee">
                    <div class="container-fluid p-0">
                        <div class="row g-0">
                            <div class="col-12">

                            </div>
                        </div>
                    </div>
                </div>
                <!-- Inner Section Off-canvas Content (Footer Marquee Block) Start -->

            </div>
        </section>
        <!-- Inner Section - Contact - End -->

    </main>
    <!-- Page Content End -->

    <!-- Bottom Background Images Start -->
    <div class="bottom__background bottom-bg-01">
        <div class="bottom-bg-01__02 animate-card-2">
            <img src="img/backgrounds/test5.webp" alt="Template background image">
        </div>
        <div class="bottom-bg-01__01 animate-card-2">
            <img src="img/backgrounds/test2.webp" alt="Template background image">
        </div>
    </div>
    <!-- Bottom Background Images End -->

    <!-- Mobile Menu Bottom Placeholder Start -->
    <div class="header-offset"></div>
    <!-- Mobile Menu Bottom Placeholder End -->

    <!-- To Top Button Start -->
    <a href="#0" id="to-top" class="btn btn-to-top slide-up">
        <i class="ph ph-arrow-up"></i>
    </a>
    <!-- To Top Button End -->

    <!-- Root element of PhotoSwipe. Must have class pswp. -->
    <div class="pswp" tabindex="-1" role="dialog" aria-hidden="true">

        <!-- Background of PhotoSwipe.
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    It's a separate element, as animating opacity is faster than rgba(). -->
        <div class="pswp__bg"></div>

        <!-- Slides wrapper with overflow:hidden. -->
        <div class="pswp__scroll-wrap">

            <!-- Container that holds slides. PhotoSwipe keeps only 3 slides in DOM to save memory. -->
            <!-- don't modify these 3 pswp__item elements, data is added later on. -->
            <div class="pswp__container">
                <div class="pswp__item"></div>
                <div class="pswp__item"></div>
                <div class="pswp__item"></div>
            </div>

            <!-- Default (PhotoSwipeUI_Default) interface on top of sliding area. Can be changed. -->
            <div class="pswp__ui pswp__ui--hidden">

                <div class="pswp__top-bar">

                    <!--  Controls are self-explanatory. Order can be changed. -->

                    <div class="pswp__counter"></div>

                    <button class="pswp__button pswp__button--close link-s" title="Close (Esc)"></button>

                    <button class="pswp__button pswp__button--share link-s" title="Share"></button>

                    <button class="pswp__button pswp__button--fs link-s" title="Toggle fullscreen"></button>

                    <button class="pswp__button pswp__button--zoom link-s" title="Zoom in/out"></button>

                    <!-- Preloader demo http://codepen.io/dimsemenov/pen/yyBWoR -->
                    <!-- element will get class pswp__preloader-active when preloader is running -->
                    <div class="pswp__preloader">
                        <div class="pswp__preloader__icn">
                            <div class="pswp__preloader__cut">
                                <div class="pswp__preloader__donut"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="pswp__share-modal pswp__share-modal--hidden pswp__single-tap">
                    <div class="pswp__share-tooltip"></div>
                </div>

                <button class="pswp__button pswp__button--arrow--left link-s" title="Previous (arrow left)"></button>

                <button class="pswp__button pswp__button--arrow--right link-s" title="Next (arrow right)"></button>

                <div class="pswp__caption">
                    <div class="pswp__caption__center"></div>
                </div>

            </div>

        </div>

    </div>

    <!-- Load Scripts Start -->
    <script src="js/libs.min.js"></script>
    <script src="js/gallery-init.js"></script>
    <script src="js/app.js"></script>

    <script>
        document.querySelectorAll('.lightboxgallery-link').forEach(item => {
            item.addEventListener('click', event => {
                event.preventDefault();
                const imgSrc = event.currentTarget.href;
                const overlay = document.createElement('div');
                overlay.classList.add('lightboxgallery-overlay');
                overlay.innerHTML = `<img src="${imgSrc}">`;
                document.body.appendChild(overlay);

                overlay.addEventListener('click', () => {
                    document.body.removeChild(overlay);
                });
            });
        });
    </script>

    <script>
        // Additional script to ensure masonry layout works on first load
        document.addEventListener("DOMContentLoaded", function() {
            // Wait for images to load
            const gallery = document.querySelector('.my-gallery');
            if (gallery) {
                const imgs = gallery.querySelectorAll('img');
                let imagesLoaded = 0;
                
                // Function to check if all images are loaded
                const imageLoaded = () => {
                    imagesLoaded++;
                    if (imagesLoaded === imgs.length) {
                        // All images loaded, force masonry layout
                        setTimeout(() => {
                            if (typeof jQuery !== 'undefined' && jQuery('.my-gallery').data('masonry')) {
                                jQuery('.my-gallery').masonry('layout');
                            }
                        }, 300);
                    }
                };
                
                // Add load event to each image
                imgs.forEach(img => {
                    if (img.complete) {
                        imageLoaded();
                    } else {
                        img.addEventListener('load', imageLoaded);
                        img.addEventListener('error', imageLoaded); // Also count errors as "loaded"
                    }
                });
                
                // Force layout after a timeout regardless of image loading
                setTimeout(() => {
                    if (typeof jQuery !== 'undefined' && jQuery('.my-gallery').data('masonry')) {
                        jQuery('.my-gallery').masonry('layout');
                    }
                }, 1000);
            }
        });
    </script>

    <script>
        document.addEventListener("DOMContentLoaded", function () {
            document.documentElement.setAttribute("data-theme", "dark");
        });
    </script>
    <!-- Load Scripts End -->


</body>

</html>