/*!
 * jQuery JavaScript Library v3.7.1
 * https://jquery.com/
 *
 * Copyright OpenJS Foundation and other contributors
 * Released under the MIT license
 * https://jquery.org/license
 *
 * Date: 2023-08-28T13:37Z
 */
!function(t,e){"use strict";"object"==typeof module&&"object"==typeof module.exports?module.exports=t.document?e(t,!0):function(t){if(!t.document)throw new Error("jQuery requires a window with a document");return e(t)}:e(t)}("undefined"!=typeof window?window:this,(function(t,e){"use strict";var n=[],i=Object.getPrototypeOf,r=n.slice,o=n.flat?function(t){return n.flat.call(t)}:function(t){return n.concat.apply([],t)},s=n.push,a=n.indexOf,l={},c=l.toString,u=l.hasOwnProperty,h=u.toString,f=h.call(Object),d={},p=function(t){return"function"==typeof t&&"number"!=typeof t.nodeType&&"function"!=typeof t.item},g=function(t){return null!=t&&t===t.window},m=t.document,v={type:!0,src:!0,nonce:!0,noModule:!0};function y(t,e,n){var i,r,o=(n=n||m).createElement("script");if(o.text=t,e)for(i in v)(r=e[i]||e.getAttribute&&e.getAttribute(i))&&o.setAttribute(i,r);n.head.appendChild(o).parentNode.removeChild(o)}function _(t){return null==t?t+"":"object"==typeof t||"function"==typeof t?l[c.call(t)]||"object":typeof t}var b="3.7.1",w=/HTML$/i,x=function(t,e){return new x.fn.init(t,e)};function T(t){var e=!!t&&"length"in t&&t.length,n=_(t);return!p(t)&&!g(t)&&("array"===n||0===e||"number"==typeof e&&e>0&&e-1 in t)}function E(t,e){return t.nodeName&&t.nodeName.toLowerCase()===e.toLowerCase()}x.fn=x.prototype={jquery:b,constructor:x,length:0,toArray:function(){return r.call(this)},get:function(t){return null==t?r.call(this):t<0?this[t+this.length]:this[t]},pushStack:function(t){var e=x.merge(this.constructor(),t);return e.prevObject=this,e},each:function(t){return x.each(this,t)},map:function(t){return this.pushStack(x.map(this,(function(e,n){return t.call(e,n,e)})))},slice:function(){return this.pushStack(r.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},even:function(){return this.pushStack(x.grep(this,(function(t,e){return(e+1)%2})))},odd:function(){return this.pushStack(x.grep(this,(function(t,e){return e%2})))},eq:function(t){var e=this.length,n=+t+(t<0?e:0);return this.pushStack(n>=0&&n<e?[this[n]]:[])},end:function(){return this.prevObject||this.constructor()},push:s,sort:n.sort,splice:n.splice},x.extend=x.fn.extend=function(){var t,e,n,i,r,o,s=arguments[0]||{},a=1,l=arguments.length,c=!1;for("boolean"==typeof s&&(c=s,s=arguments[a]||{},a++),"object"==typeof s||p(s)||(s={}),a===l&&(s=this,a--);a<l;a++)if(null!=(t=arguments[a]))for(e in t)i=t[e],"__proto__"!==e&&s!==i&&(c&&i&&(x.isPlainObject(i)||(r=Array.isArray(i)))?(n=s[e],o=r&&!Array.isArray(n)?[]:r||x.isPlainObject(n)?n:{},r=!1,s[e]=x.extend(c,o,i)):void 0!==i&&(s[e]=i));return s},x.extend({expando:"jQuery"+(b+Math.random()).replace(/\D/g,""),isReady:!0,error:function(t){throw new Error(t)},noop:function(){},isPlainObject:function(t){var e,n;return!(!t||"[object Object]"!==c.call(t)||(e=i(t))&&("function"!=typeof(n=u.call(e,"constructor")&&e.constructor)||h.call(n)!==f))},isEmptyObject:function(t){var e;for(e in t)return!1;return!0},globalEval:function(t,e,n){y(t,{nonce:e&&e.nonce},n)},each:function(t,e){var n,i=0;if(T(t))for(n=t.length;i<n&&!1!==e.call(t[i],i,t[i]);i++);else for(i in t)if(!1===e.call(t[i],i,t[i]))break;return t},text:function(t){var e,n="",i=0,r=t.nodeType;if(!r)for(;e=t[i++];)n+=x.text(e);return 1===r||11===r?t.textContent:9===r?t.documentElement.textContent:3===r||4===r?t.nodeValue:n},makeArray:function(t,e){var n=e||[];return null!=t&&(T(Object(t))?x.merge(n,"string"==typeof t?[t]:t):s.call(n,t)),n},inArray:function(t,e,n){return null==e?-1:a.call(e,t,n)},isXMLDoc:function(t){var e=t&&t.namespaceURI,n=t&&(t.ownerDocument||t).documentElement;return!w.test(e||n&&n.nodeName||"HTML")},merge:function(t,e){for(var n=+e.length,i=0,r=t.length;i<n;i++)t[r++]=e[i];return t.length=r,t},grep:function(t,e,n){for(var i=[],r=0,o=t.length,s=!n;r<o;r++)!e(t[r],r)!==s&&i.push(t[r]);return i},map:function(t,e,n){var i,r,s=0,a=[];if(T(t))for(i=t.length;s<i;s++)null!=(r=e(t[s],s,n))&&a.push(r);else for(s in t)null!=(r=e(t[s],s,n))&&a.push(r);return o(a)},guid:1,support:d}),"function"==typeof Symbol&&(x.fn[Symbol.iterator]=n[Symbol.iterator]),x.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),(function(t,e){l["[object "+e+"]"]=e.toLowerCase()}));var C=n.pop,S=n.sort,k=n.splice,A="[\\x20\\t\\r\\n\\f]",O=new RegExp("^"+A+"+|((?:^|[^\\\\])(?:\\\\.)*)"+A+"+$","g");x.contains=function(t,e){var n=e&&e.parentNode;return t===n||!(!n||1!==n.nodeType||!(t.contains?t.contains(n):t.compareDocumentPosition&&16&t.compareDocumentPosition(n)))};var D=/([\0-\x1f\x7f]|^-?\d)|^-$|[^\x80-\uFFFF\w-]/g;function M(t,e){return e?"\0"===t?"�":t.slice(0,-1)+"\\"+t.charCodeAt(t.length-1).toString(16)+" ":"\\"+t}x.escapeSelector=function(t){return(t+"").replace(D,M)};var P=m,L=s;!function(){var e,i,o,s,l,c,h,f,p,g,m=L,v=x.expando,y=0,_=0,b=tt(),w=tt(),T=tt(),D=tt(),M=function(t,e){return t===e&&(l=!0),0},j="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",N="(?:\\\\[\\da-fA-F]{1,6}"+A+"?|\\\\[^\\r\\n\\f]|[\\w-]|[^\0-\\x7f])+",I="\\["+A+"*("+N+")(?:"+A+"*([*^$|!~]?=)"+A+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+N+"))|)"+A+"*\\]",R=":("+N+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+I+")*)|.*)\\)|)",z=new RegExp(A+"+","g"),F=new RegExp("^"+A+"*,"+A+"*"),$=new RegExp("^"+A+"*([>+~]|"+A+")"+A+"*"),H=new RegExp(A+"|>"),q=new RegExp(R),B=new RegExp("^"+N+"$"),W={ID:new RegExp("^#("+N+")"),CLASS:new RegExp("^\\.("+N+")"),TAG:new RegExp("^("+N+"|[*])"),ATTR:new RegExp("^"+I),PSEUDO:new RegExp("^"+R),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+A+"*(even|odd|(([+-]|)(\\d*)n|)"+A+"*(?:([+-]|)"+A+"*(\\d+)|))"+A+"*\\)|)","i"),bool:new RegExp("^(?:"+j+")$","i"),needsContext:new RegExp("^"+A+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+A+"*((?:-\\d)?\\d*)"+A+"*\\)|)(?=[^-]|$)","i")},X=/^(?:input|select|textarea|button)$/i,Y=/^h\d$/i,U=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,V=/[+~]/,Q=new RegExp("\\\\[\\da-fA-F]{1,6}"+A+"?|\\\\([^\\r\\n\\f])","g"),G=function(t,e){var n="0x"+t.slice(1)-65536;return e||(n<0?String.fromCharCode(n+65536):String.fromCharCode(n>>10|55296,1023&n|56320))},K=function(){lt()},J=ft((function(t){return!0===t.disabled&&E(t,"fieldset")}),{dir:"parentNode",next:"legend"});try{m.apply(n=r.call(P.childNodes),P.childNodes),n[P.childNodes.length].nodeType}catch(t){m={apply:function(t,e){L.apply(t,r.call(e))},call:function(t){L.apply(t,r.call(arguments,1))}}}function Z(t,e,n,i){var r,o,s,a,l,u,h,g=e&&e.ownerDocument,y=e?e.nodeType:9;if(n=n||[],"string"!=typeof t||!t||1!==y&&9!==y&&11!==y)return n;if(!i&&(lt(e),e=e||c,f)){if(11!==y&&(l=U.exec(t)))if(r=l[1]){if(9===y){if(!(s=e.getElementById(r)))return n;if(s.id===r)return m.call(n,s),n}else if(g&&(s=g.getElementById(r))&&Z.contains(e,s)&&s.id===r)return m.call(n,s),n}else{if(l[2])return m.apply(n,e.getElementsByTagName(t)),n;if((r=l[3])&&e.getElementsByClassName)return m.apply(n,e.getElementsByClassName(r)),n}if(!(D[t+" "]||p&&p.test(t))){if(h=t,g=e,1===y&&(H.test(t)||$.test(t))){for((g=V.test(t)&&at(e.parentNode)||e)==e&&d.scope||((a=e.getAttribute("id"))?a=x.escapeSelector(a):e.setAttribute("id",a=v)),o=(u=ut(t)).length;o--;)u[o]=(a?"#"+a:":scope")+" "+ht(u[o]);h=u.join(",")}try{return m.apply(n,g.querySelectorAll(h)),n}catch(e){D(t,!0)}finally{a===v&&e.removeAttribute("id")}}}return yt(t.replace(O,"$1"),e,n,i)}function tt(){var t=[];return function e(n,r){return t.push(n+" ")>i.cacheLength&&delete e[t.shift()],e[n+" "]=r}}function et(t){return t[v]=!0,t}function nt(t){var e=c.createElement("fieldset");try{return!!t(e)}catch(t){return!1}finally{e.parentNode&&e.parentNode.removeChild(e),e=null}}function it(t){return function(e){return E(e,"input")&&e.type===t}}function rt(t){return function(e){return(E(e,"input")||E(e,"button"))&&e.type===t}}function ot(t){return function(e){return"form"in e?e.parentNode&&!1===e.disabled?"label"in e?"label"in e.parentNode?e.parentNode.disabled===t:e.disabled===t:e.isDisabled===t||e.isDisabled!==!t&&J(e)===t:e.disabled===t:"label"in e&&e.disabled===t}}function st(t){return et((function(e){return e=+e,et((function(n,i){for(var r,o=t([],n.length,e),s=o.length;s--;)n[r=o[s]]&&(n[r]=!(i[r]=n[r]))}))}))}function at(t){return t&&void 0!==t.getElementsByTagName&&t}function lt(t){var e,n=t?t.ownerDocument||t:P;return n!=c&&9===n.nodeType&&n.documentElement?(h=(c=n).documentElement,f=!x.isXMLDoc(c),g=h.matches||h.webkitMatchesSelector||h.msMatchesSelector,h.msMatchesSelector&&P!=c&&(e=c.defaultView)&&e.top!==e&&e.addEventListener("unload",K),d.getById=nt((function(t){return h.appendChild(t).id=x.expando,!c.getElementsByName||!c.getElementsByName(x.expando).length})),d.disconnectedMatch=nt((function(t){return g.call(t,"*")})),d.scope=nt((function(){return c.querySelectorAll(":scope")})),d.cssHas=nt((function(){try{return c.querySelector(":has(*,:jqfake)"),!1}catch(t){return!0}})),d.getById?(i.filter.ID=function(t){var e=t.replace(Q,G);return function(t){return t.getAttribute("id")===e}},i.find.ID=function(t,e){if(void 0!==e.getElementById&&f){var n=e.getElementById(t);return n?[n]:[]}}):(i.filter.ID=function(t){var e=t.replace(Q,G);return function(t){var n=void 0!==t.getAttributeNode&&t.getAttributeNode("id");return n&&n.value===e}},i.find.ID=function(t,e){if(void 0!==e.getElementById&&f){var n,i,r,o=e.getElementById(t);if(o){if((n=o.getAttributeNode("id"))&&n.value===t)return[o];for(r=e.getElementsByName(t),i=0;o=r[i++];)if((n=o.getAttributeNode("id"))&&n.value===t)return[o]}return[]}}),i.find.TAG=function(t,e){return void 0!==e.getElementsByTagName?e.getElementsByTagName(t):e.querySelectorAll(t)},i.find.CLASS=function(t,e){if(void 0!==e.getElementsByClassName&&f)return e.getElementsByClassName(t)},p=[],nt((function(t){var e;h.appendChild(t).innerHTML="<a id='"+v+"' href='' disabled='disabled'></a><select id='"+v+"-\r\\' disabled='disabled'><option selected=''></option></select>",t.querySelectorAll("[selected]").length||p.push("\\["+A+"*(?:value|"+j+")"),t.querySelectorAll("[id~="+v+"-]").length||p.push("~="),t.querySelectorAll("a#"+v+"+*").length||p.push(".#.+[+~]"),t.querySelectorAll(":checked").length||p.push(":checked"),(e=c.createElement("input")).setAttribute("type","hidden"),t.appendChild(e).setAttribute("name","D"),h.appendChild(t).disabled=!0,2!==t.querySelectorAll(":disabled").length&&p.push(":enabled",":disabled"),(e=c.createElement("input")).setAttribute("name",""),t.appendChild(e),t.querySelectorAll("[name='']").length||p.push("\\["+A+"*name"+A+"*="+A+"*(?:''|\"\")")})),d.cssHas||p.push(":has"),p=p.length&&new RegExp(p.join("|")),M=function(t,e){if(t===e)return l=!0,0;var n=!t.compareDocumentPosition-!e.compareDocumentPosition;return n||(1&(n=(t.ownerDocument||t)==(e.ownerDocument||e)?t.compareDocumentPosition(e):1)||!d.sortDetached&&e.compareDocumentPosition(t)===n?t===c||t.ownerDocument==P&&Z.contains(P,t)?-1:e===c||e.ownerDocument==P&&Z.contains(P,e)?1:s?a.call(s,t)-a.call(s,e):0:4&n?-1:1)},c):c}for(e in Z.matches=function(t,e){return Z(t,null,null,e)},Z.matchesSelector=function(t,e){if(lt(t),f&&!D[e+" "]&&(!p||!p.test(e)))try{var n=g.call(t,e);if(n||d.disconnectedMatch||t.document&&11!==t.document.nodeType)return n}catch(t){D(e,!0)}return Z(e,c,null,[t]).length>0},Z.contains=function(t,e){return(t.ownerDocument||t)!=c&&lt(t),x.contains(t,e)},Z.attr=function(t,e){(t.ownerDocument||t)!=c&&lt(t);var n=i.attrHandle[e.toLowerCase()],r=n&&u.call(i.attrHandle,e.toLowerCase())?n(t,e,!f):void 0;return void 0!==r?r:t.getAttribute(e)},Z.error=function(t){throw new Error("Syntax error, unrecognized expression: "+t)},x.uniqueSort=function(t){var e,n=[],i=0,o=0;if(l=!d.sortStable,s=!d.sortStable&&r.call(t,0),S.call(t,M),l){for(;e=t[o++];)e===t[o]&&(i=n.push(o));for(;i--;)k.call(t,n[i],1)}return s=null,t},x.fn.uniqueSort=function(){return this.pushStack(x.uniqueSort(r.apply(this)))},i=x.expr={cacheLength:50,createPseudo:et,match:W,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(t){return t[1]=t[1].replace(Q,G),t[3]=(t[3]||t[4]||t[5]||"").replace(Q,G),"~="===t[2]&&(t[3]=" "+t[3]+" "),t.slice(0,4)},CHILD:function(t){return t[1]=t[1].toLowerCase(),"nth"===t[1].slice(0,3)?(t[3]||Z.error(t[0]),t[4]=+(t[4]?t[5]+(t[6]||1):2*("even"===t[3]||"odd"===t[3])),t[5]=+(t[7]+t[8]||"odd"===t[3])):t[3]&&Z.error(t[0]),t},PSEUDO:function(t){var e,n=!t[6]&&t[2];return W.CHILD.test(t[0])?null:(t[3]?t[2]=t[4]||t[5]||"":n&&q.test(n)&&(e=ut(n,!0))&&(e=n.indexOf(")",n.length-e)-n.length)&&(t[0]=t[0].slice(0,e),t[2]=n.slice(0,e)),t.slice(0,3))}},filter:{TAG:function(t){var e=t.replace(Q,G).toLowerCase();return"*"===t?function(){return!0}:function(t){return E(t,e)}},CLASS:function(t){var e=b[t+" "];return e||(e=new RegExp("(^|"+A+")"+t+"("+A+"|$)"))&&b(t,(function(t){return e.test("string"==typeof t.className&&t.className||void 0!==t.getAttribute&&t.getAttribute("class")||"")}))},ATTR:function(t,e,n){return function(i){var r=Z.attr(i,t);return null==r?"!="===e:!e||(r+="","="===e?r===n:"!="===e?r!==n:"^="===e?n&&0===r.indexOf(n):"*="===e?n&&r.indexOf(n)>-1:"$="===e?n&&r.slice(-n.length)===n:"~="===e?(" "+r.replace(z," ")+" ").indexOf(n)>-1:"|="===e&&(r===n||r.slice(0,n.length+1)===n+"-"))}},CHILD:function(t,e,n,i,r){var o="nth"!==t.slice(0,3),s="last"!==t.slice(-4),a="of-type"===e;return 1===i&&0===r?function(t){return!!t.parentNode}:function(e,n,l){var c,u,h,f,d,p=o!==s?"nextSibling":"previousSibling",g=e.parentNode,m=a&&e.nodeName.toLowerCase(),_=!l&&!a,b=!1;if(g){if(o){for(;p;){for(h=e;h=h[p];)if(a?E(h,m):1===h.nodeType)return!1;d=p="only"===t&&!d&&"nextSibling"}return!0}if(d=[s?g.firstChild:g.lastChild],s&&_){for(b=(f=(c=(u=g[v]||(g[v]={}))[t]||[])[0]===y&&c[1])&&c[2],h=f&&g.childNodes[f];h=++f&&h&&h[p]||(b=f=0)||d.pop();)if(1===h.nodeType&&++b&&h===e){u[t]=[y,f,b];break}}else if(_&&(b=f=(c=(u=e[v]||(e[v]={}))[t]||[])[0]===y&&c[1]),!1===b)for(;(h=++f&&h&&h[p]||(b=f=0)||d.pop())&&(!(a?E(h,m):1===h.nodeType)||!++b||(_&&((u=h[v]||(h[v]={}))[t]=[y,b]),h!==e)););return(b-=r)===i||b%i==0&&b/i>=0}}},PSEUDO:function(t,e){var n,r=i.pseudos[t]||i.setFilters[t.toLowerCase()]||Z.error("unsupported pseudo: "+t);return r[v]?r(e):r.length>1?(n=[t,t,"",e],i.setFilters.hasOwnProperty(t.toLowerCase())?et((function(t,n){for(var i,o=r(t,e),s=o.length;s--;)t[i=a.call(t,o[s])]=!(n[i]=o[s])})):function(t){return r(t,0,n)}):r}},pseudos:{not:et((function(t){var e=[],n=[],i=vt(t.replace(O,"$1"));return i[v]?et((function(t,e,n,r){for(var o,s=i(t,null,r,[]),a=t.length;a--;)(o=s[a])&&(t[a]=!(e[a]=o))})):function(t,r,o){return e[0]=t,i(e,null,o,n),e[0]=null,!n.pop()}})),has:et((function(t){return function(e){return Z(t,e).length>0}})),contains:et((function(t){return t=t.replace(Q,G),function(e){return(e.textContent||x.text(e)).indexOf(t)>-1}})),lang:et((function(t){return B.test(t||"")||Z.error("unsupported lang: "+t),t=t.replace(Q,G).toLowerCase(),function(e){var n;do{if(n=f?e.lang:e.getAttribute("xml:lang")||e.getAttribute("lang"))return(n=n.toLowerCase())===t||0===n.indexOf(t+"-")}while((e=e.parentNode)&&1===e.nodeType);return!1}})),target:function(e){var n=t.location&&t.location.hash;return n&&n.slice(1)===e.id},root:function(t){return t===h},focus:function(t){return t===function(){try{return c.activeElement}catch(t){}}()&&c.hasFocus()&&!!(t.type||t.href||~t.tabIndex)},enabled:ot(!1),disabled:ot(!0),checked:function(t){return E(t,"input")&&!!t.checked||E(t,"option")&&!!t.selected},selected:function(t){return t.parentNode&&t.parentNode.selectedIndex,!0===t.selected},empty:function(t){for(t=t.firstChild;t;t=t.nextSibling)if(t.nodeType<6)return!1;return!0},parent:function(t){return!i.pseudos.empty(t)},header:function(t){return Y.test(t.nodeName)},input:function(t){return X.test(t.nodeName)},button:function(t){return E(t,"input")&&"button"===t.type||E(t,"button")},text:function(t){var e;return E(t,"input")&&"text"===t.type&&(null==(e=t.getAttribute("type"))||"text"===e.toLowerCase())},first:st((function(){return[0]})),last:st((function(t,e){return[e-1]})),eq:st((function(t,e,n){return[n<0?n+e:n]})),even:st((function(t,e){for(var n=0;n<e;n+=2)t.push(n);return t})),odd:st((function(t,e){for(var n=1;n<e;n+=2)t.push(n);return t})),lt:st((function(t,e,n){var i;for(i=n<0?n+e:n>e?e:n;--i>=0;)t.push(i);return t})),gt:st((function(t,e,n){for(var i=n<0?n+e:n;++i<e;)t.push(i);return t}))}},i.pseudos.nth=i.pseudos.eq,{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})i.pseudos[e]=it(e);for(e in{submit:!0,reset:!0})i.pseudos[e]=rt(e);function ct(){}function ut(t,e){var n,r,o,s,a,l,c,u=w[t+" "];if(u)return e?0:u.slice(0);for(a=t,l=[],c=i.preFilter;a;){for(s in n&&!(r=F.exec(a))||(r&&(a=a.slice(r[0].length)||a),l.push(o=[])),n=!1,(r=$.exec(a))&&(n=r.shift(),o.push({value:n,type:r[0].replace(O," ")}),a=a.slice(n.length)),i.filter)!(r=W[s].exec(a))||c[s]&&!(r=c[s](r))||(n=r.shift(),o.push({value:n,type:s,matches:r}),a=a.slice(n.length));if(!n)break}return e?a.length:a?Z.error(t):w(t,l).slice(0)}function ht(t){for(var e=0,n=t.length,i="";e<n;e++)i+=t[e].value;return i}function ft(t,e,n){var i=e.dir,r=e.next,o=r||i,s=n&&"parentNode"===o,a=_++;return e.first?function(e,n,r){for(;e=e[i];)if(1===e.nodeType||s)return t(e,n,r);return!1}:function(e,n,l){var c,u,h=[y,a];if(l){for(;e=e[i];)if((1===e.nodeType||s)&&t(e,n,l))return!0}else for(;e=e[i];)if(1===e.nodeType||s)if(u=e[v]||(e[v]={}),r&&E(e,r))e=e[i]||e;else{if((c=u[o])&&c[0]===y&&c[1]===a)return h[2]=c[2];if(u[o]=h,h[2]=t(e,n,l))return!0}return!1}}function dt(t){return t.length>1?function(e,n,i){for(var r=t.length;r--;)if(!t[r](e,n,i))return!1;return!0}:t[0]}function pt(t,e,n,i,r){for(var o,s=[],a=0,l=t.length,c=null!=e;a<l;a++)(o=t[a])&&(n&&!n(o,i,r)||(s.push(o),c&&e.push(a)));return s}function gt(t,e,n,i,r,o){return i&&!i[v]&&(i=gt(i)),r&&!r[v]&&(r=gt(r,o)),et((function(o,s,l,c){var u,h,f,d,p=[],g=[],v=s.length,y=o||function(t,e,n){for(var i=0,r=e.length;i<r;i++)Z(t,e[i],n);return n}(e||"*",l.nodeType?[l]:l,[]),_=!t||!o&&e?y:pt(y,p,t,l,c);if(n?n(_,d=r||(o?t:v||i)?[]:s,l,c):d=_,i)for(u=pt(d,g),i(u,[],l,c),h=u.length;h--;)(f=u[h])&&(d[g[h]]=!(_[g[h]]=f));if(o){if(r||t){if(r){for(u=[],h=d.length;h--;)(f=d[h])&&u.push(_[h]=f);r(null,d=[],u,c)}for(h=d.length;h--;)(f=d[h])&&(u=r?a.call(o,f):p[h])>-1&&(o[u]=!(s[u]=f))}}else d=pt(d===s?d.splice(v,d.length):d),r?r(null,s,d,c):m.apply(s,d)}))}function mt(t){for(var e,n,r,s=t.length,l=i.relative[t[0].type],c=l||i.relative[" "],u=l?1:0,h=ft((function(t){return t===e}),c,!0),f=ft((function(t){return a.call(e,t)>-1}),c,!0),d=[function(t,n,i){var r=!l&&(i||n!=o)||((e=n).nodeType?h(t,n,i):f(t,n,i));return e=null,r}];u<s;u++)if(n=i.relative[t[u].type])d=[ft(dt(d),n)];else{if((n=i.filter[t[u].type].apply(null,t[u].matches))[v]){for(r=++u;r<s&&!i.relative[t[r].type];r++);return gt(u>1&&dt(d),u>1&&ht(t.slice(0,u-1).concat({value:" "===t[u-2].type?"*":""})).replace(O,"$1"),n,u<r&&mt(t.slice(u,r)),r<s&&mt(t=t.slice(r)),r<s&&ht(t))}d.push(n)}return dt(d)}function vt(t,e){var n,r=[],s=[],a=T[t+" "];if(!a){for(e||(e=ut(t)),n=e.length;n--;)(a=mt(e[n]))[v]?r.push(a):s.push(a);a=T(t,function(t,e){var n=e.length>0,r=t.length>0,s=function(s,a,l,u,h){var d,p,g,v=0,_="0",b=s&&[],w=[],T=o,E=s||r&&i.find.TAG("*",h),S=y+=null==T?1:Math.random()||.1,k=E.length;for(h&&(o=a==c||a||h);_!==k&&null!=(d=E[_]);_++){if(r&&d){for(p=0,a||d.ownerDocument==c||(lt(d),l=!f);g=t[p++];)if(g(d,a||c,l)){m.call(u,d);break}h&&(y=S)}n&&((d=!g&&d)&&v--,s&&b.push(d))}if(v+=_,n&&_!==v){for(p=0;g=e[p++];)g(b,w,a,l);if(s){if(v>0)for(;_--;)b[_]||w[_]||(w[_]=C.call(u));w=pt(w)}m.apply(u,w),h&&!s&&w.length>0&&v+e.length>1&&x.uniqueSort(u)}return h&&(y=S,o=T),b};return n?et(s):s}(s,r)),a.selector=t}return a}function yt(t,e,n,r){var o,s,a,l,c,u="function"==typeof t&&t,h=!r&&ut(t=u.selector||t);if(n=n||[],1===h.length){if((s=h[0]=h[0].slice(0)).length>2&&"ID"===(a=s[0]).type&&9===e.nodeType&&f&&i.relative[s[1].type]){if(!(e=(i.find.ID(a.matches[0].replace(Q,G),e)||[])[0]))return n;u&&(e=e.parentNode),t=t.slice(s.shift().value.length)}for(o=W.needsContext.test(t)?0:s.length;o--&&(a=s[o],!i.relative[l=a.type]);)if((c=i.find[l])&&(r=c(a.matches[0].replace(Q,G),V.test(s[0].type)&&at(e.parentNode)||e))){if(s.splice(o,1),!(t=r.length&&ht(s)))return m.apply(n,r),n;break}}return(u||vt(t,h))(r,e,!f,n,!e||V.test(t)&&at(e.parentNode)||e),n}ct.prototype=i.filters=i.pseudos,i.setFilters=new ct,d.sortStable=v.split("").sort(M).join("")===v,lt(),d.sortDetached=nt((function(t){return 1&t.compareDocumentPosition(c.createElement("fieldset"))})),x.find=Z,x.expr[":"]=x.expr.pseudos,x.unique=x.uniqueSort,Z.compile=vt,Z.select=yt,Z.setDocument=lt,Z.tokenize=ut,Z.escape=x.escapeSelector,Z.getText=x.text,Z.isXML=x.isXMLDoc,Z.selectors=x.expr,Z.support=x.support,Z.uniqueSort=x.uniqueSort}();var j=function(t,e,n){for(var i=[],r=void 0!==n;(t=t[e])&&9!==t.nodeType;)if(1===t.nodeType){if(r&&x(t).is(n))break;i.push(t)}return i},N=function(t,e){for(var n=[];t;t=t.nextSibling)1===t.nodeType&&t!==e&&n.push(t);return n},I=x.expr.match.needsContext,R=/^<([a-z][^\/\0>:\x20\t\r\n\f]*)[\x20\t\r\n\f]*\/?>(?:<\/\1>|)$/i;function z(t,e,n){return p(e)?x.grep(t,(function(t,i){return!!e.call(t,i,t)!==n})):e.nodeType?x.grep(t,(function(t){return t===e!==n})):"string"!=typeof e?x.grep(t,(function(t){return a.call(e,t)>-1!==n})):x.filter(e,t,n)}x.filter=function(t,e,n){var i=e[0];return n&&(t=":not("+t+")"),1===e.length&&1===i.nodeType?x.find.matchesSelector(i,t)?[i]:[]:x.find.matches(t,x.grep(e,(function(t){return 1===t.nodeType})))},x.fn.extend({find:function(t){var e,n,i=this.length,r=this;if("string"!=typeof t)return this.pushStack(x(t).filter((function(){for(e=0;e<i;e++)if(x.contains(r[e],this))return!0})));for(n=this.pushStack([]),e=0;e<i;e++)x.find(t,r[e],n);return i>1?x.uniqueSort(n):n},filter:function(t){return this.pushStack(z(this,t||[],!1))},not:function(t){return this.pushStack(z(this,t||[],!0))},is:function(t){return!!z(this,"string"==typeof t&&I.test(t)?x(t):t||[],!1).length}});var F,$=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]+))$/;(x.fn.init=function(t,e,n){var i,r;if(!t)return this;if(n=n||F,"string"==typeof t){if(!(i="<"===t[0]&&">"===t[t.length-1]&&t.length>=3?[null,t,null]:$.exec(t))||!i[1]&&e)return!e||e.jquery?(e||n).find(t):this.constructor(e).find(t);if(i[1]){if(e=e instanceof x?e[0]:e,x.merge(this,x.parseHTML(i[1],e&&e.nodeType?e.ownerDocument||e:m,!0)),R.test(i[1])&&x.isPlainObject(e))for(i in e)p(this[i])?this[i](e[i]):this.attr(i,e[i]);return this}return(r=m.getElementById(i[2]))&&(this[0]=r,this.length=1),this}return t.nodeType?(this[0]=t,this.length=1,this):p(t)?void 0!==n.ready?n.ready(t):t(x):x.makeArray(t,this)}).prototype=x.fn,F=x(m);var H=/^(?:parents|prev(?:Until|All))/,q={children:!0,contents:!0,next:!0,prev:!0};function B(t,e){for(;(t=t[e])&&1!==t.nodeType;);return t}x.fn.extend({has:function(t){var e=x(t,this),n=e.length;return this.filter((function(){for(var t=0;t<n;t++)if(x.contains(this,e[t]))return!0}))},closest:function(t,e){var n,i=0,r=this.length,o=[],s="string"!=typeof t&&x(t);if(!I.test(t))for(;i<r;i++)for(n=this[i];n&&n!==e;n=n.parentNode)if(n.nodeType<11&&(s?s.index(n)>-1:1===n.nodeType&&x.find.matchesSelector(n,t))){o.push(n);break}return this.pushStack(o.length>1?x.uniqueSort(o):o)},index:function(t){return t?"string"==typeof t?a.call(x(t),this[0]):a.call(this,t.jquery?t[0]:t):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(t,e){return this.pushStack(x.uniqueSort(x.merge(this.get(),x(t,e))))},addBack:function(t){return this.add(null==t?this.prevObject:this.prevObject.filter(t))}}),x.each({parent:function(t){var e=t.parentNode;return e&&11!==e.nodeType?e:null},parents:function(t){return j(t,"parentNode")},parentsUntil:function(t,e,n){return j(t,"parentNode",n)},next:function(t){return B(t,"nextSibling")},prev:function(t){return B(t,"previousSibling")},nextAll:function(t){return j(t,"nextSibling")},prevAll:function(t){return j(t,"previousSibling")},nextUntil:function(t,e,n){return j(t,"nextSibling",n)},prevUntil:function(t,e,n){return j(t,"previousSibling",n)},siblings:function(t){return N((t.parentNode||{}).firstChild,t)},children:function(t){return N(t.firstChild)},contents:function(t){return null!=t.contentDocument&&i(t.contentDocument)?t.contentDocument:(E(t,"template")&&(t=t.content||t),x.merge([],t.childNodes))}},(function(t,e){x.fn[t]=function(n,i){var r=x.map(this,e,n);return"Until"!==t.slice(-5)&&(i=n),i&&"string"==typeof i&&(r=x.filter(i,r)),this.length>1&&(q[t]||x.uniqueSort(r),H.test(t)&&r.reverse()),this.pushStack(r)}}));var W=/[^\x20\t\r\n\f]+/g;function X(t){return t}function Y(t){throw t}function U(t,e,n,i){var r;try{t&&p(r=t.promise)?r.call(t).done(e).fail(n):t&&p(r=t.then)?r.call(t,e,n):e.apply(void 0,[t].slice(i))}catch(t){n.apply(void 0,[t])}}x.Callbacks=function(t){t="string"==typeof t?function(t){var e={};return x.each(t.match(W)||[],(function(t,n){e[n]=!0})),e}(t):x.extend({},t);var e,n,i,r,o=[],s=[],a=-1,l=function(){for(r=r||t.once,i=e=!0;s.length;a=-1)for(n=s.shift();++a<o.length;)!1===o[a].apply(n[0],n[1])&&t.stopOnFalse&&(a=o.length,n=!1);t.memory||(n=!1),e=!1,r&&(o=n?[]:"")},c={add:function(){return o&&(n&&!e&&(a=o.length-1,s.push(n)),function e(n){x.each(n,(function(n,i){p(i)?t.unique&&c.has(i)||o.push(i):i&&i.length&&"string"!==_(i)&&e(i)}))}(arguments),n&&!e&&l()),this},remove:function(){return x.each(arguments,(function(t,e){for(var n;(n=x.inArray(e,o,n))>-1;)o.splice(n,1),n<=a&&a--})),this},has:function(t){return t?x.inArray(t,o)>-1:o.length>0},empty:function(){return o&&(o=[]),this},disable:function(){return r=s=[],o=n="",this},disabled:function(){return!o},lock:function(){return r=s=[],n||e||(o=n=""),this},locked:function(){return!!r},fireWith:function(t,n){return r||(n=[t,(n=n||[]).slice?n.slice():n],s.push(n),e||l()),this},fire:function(){return c.fireWith(this,arguments),this},fired:function(){return!!i}};return c},x.extend({Deferred:function(e){var n=[["notify","progress",x.Callbacks("memory"),x.Callbacks("memory"),2],["resolve","done",x.Callbacks("once memory"),x.Callbacks("once memory"),0,"resolved"],["reject","fail",x.Callbacks("once memory"),x.Callbacks("once memory"),1,"rejected"]],i="pending",r={state:function(){return i},always:function(){return o.done(arguments).fail(arguments),this},catch:function(t){return r.then(null,t)},pipe:function(){var t=arguments;return x.Deferred((function(e){x.each(n,(function(n,i){var r=p(t[i[4]])&&t[i[4]];o[i[1]]((function(){var t=r&&r.apply(this,arguments);t&&p(t.promise)?t.promise().progress(e.notify).done(e.resolve).fail(e.reject):e[i[0]+"With"](this,r?[t]:arguments)}))})),t=null})).promise()},then:function(e,i,r){var o=0;function s(e,n,i,r){return function(){var a=this,l=arguments,c=function(){var t,c;if(!(e<o)){if((t=i.apply(a,l))===n.promise())throw new TypeError("Thenable self-resolution");c=t&&("object"==typeof t||"function"==typeof t)&&t.then,p(c)?r?c.call(t,s(o,n,X,r),s(o,n,Y,r)):(o++,c.call(t,s(o,n,X,r),s(o,n,Y,r),s(o,n,X,n.notifyWith))):(i!==X&&(a=void 0,l=[t]),(r||n.resolveWith)(a,l))}},u=r?c:function(){try{c()}catch(t){x.Deferred.exceptionHook&&x.Deferred.exceptionHook(t,u.error),e+1>=o&&(i!==Y&&(a=void 0,l=[t]),n.rejectWith(a,l))}};e?u():(x.Deferred.getErrorHook?u.error=x.Deferred.getErrorHook():x.Deferred.getStackHook&&(u.error=x.Deferred.getStackHook()),t.setTimeout(u))}}return x.Deferred((function(t){n[0][3].add(s(0,t,p(r)?r:X,t.notifyWith)),n[1][3].add(s(0,t,p(e)?e:X)),n[2][3].add(s(0,t,p(i)?i:Y))})).promise()},promise:function(t){return null!=t?x.extend(t,r):r}},o={};return x.each(n,(function(t,e){var s=e[2],a=e[5];r[e[1]]=s.add,a&&s.add((function(){i=a}),n[3-t][2].disable,n[3-t][3].disable,n[0][2].lock,n[0][3].lock),s.add(e[3].fire),o[e[0]]=function(){return o[e[0]+"With"](this===o?void 0:this,arguments),this},o[e[0]+"With"]=s.fireWith})),r.promise(o),e&&e.call(o,o),o},when:function(t){var e=arguments.length,n=e,i=Array(n),o=r.call(arguments),s=x.Deferred(),a=function(t){return function(n){i[t]=this,o[t]=arguments.length>1?r.call(arguments):n,--e||s.resolveWith(i,o)}};if(e<=1&&(U(t,s.done(a(n)).resolve,s.reject,!e),"pending"===s.state()||p(o[n]&&o[n].then)))return s.then();for(;n--;)U(o[n],a(n),s.reject);return s.promise()}});var V=/^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/;x.Deferred.exceptionHook=function(e,n){t.console&&t.console.warn&&e&&V.test(e.name)&&t.console.warn("jQuery.Deferred exception: "+e.message,e.stack,n)},x.readyException=function(e){t.setTimeout((function(){throw e}))};var Q=x.Deferred();function G(){m.removeEventListener("DOMContentLoaded",G),t.removeEventListener("load",G),x.ready()}x.fn.ready=function(t){return Q.then(t).catch((function(t){x.readyException(t)})),this},x.extend({isReady:!1,readyWait:1,ready:function(t){(!0===t?--x.readyWait:x.isReady)||(x.isReady=!0,!0!==t&&--x.readyWait>0||Q.resolveWith(m,[x]))}}),x.ready.then=Q.then,"complete"===m.readyState||"loading"!==m.readyState&&!m.documentElement.doScroll?t.setTimeout(x.ready):(m.addEventListener("DOMContentLoaded",G),t.addEventListener("load",G));var K=function(t,e,n,i,r,o,s){var a=0,l=t.length,c=null==n;if("object"===_(n))for(a in r=!0,n)K(t,e,a,n[a],!0,o,s);else if(void 0!==i&&(r=!0,p(i)||(s=!0),c&&(s?(e.call(t,i),e=null):(c=e,e=function(t,e,n){return c.call(x(t),n)})),e))for(;a<l;a++)e(t[a],n,s?i:i.call(t[a],a,e(t[a],n)));return r?t:c?e.call(t):l?e(t[0],n):o},J=/^-ms-/,Z=/-([a-z])/g;function tt(t,e){return e.toUpperCase()}function et(t){return t.replace(J,"ms-").replace(Z,tt)}var nt=function(t){return 1===t.nodeType||9===t.nodeType||!+t.nodeType};function it(){this.expando=x.expando+it.uid++}it.uid=1,it.prototype={cache:function(t){var e=t[this.expando];return e||(e={},nt(t)&&(t.nodeType?t[this.expando]=e:Object.defineProperty(t,this.expando,{value:e,configurable:!0}))),e},set:function(t,e,n){var i,r=this.cache(t);if("string"==typeof e)r[et(e)]=n;else for(i in e)r[et(i)]=e[i];return r},get:function(t,e){return void 0===e?this.cache(t):t[this.expando]&&t[this.expando][et(e)]},access:function(t,e,n){return void 0===e||e&&"string"==typeof e&&void 0===n?this.get(t,e):(this.set(t,e,n),void 0!==n?n:e)},remove:function(t,e){var n,i=t[this.expando];if(void 0!==i){if(void 0!==e){n=(e=Array.isArray(e)?e.map(et):(e=et(e))in i?[e]:e.match(W)||[]).length;for(;n--;)delete i[e[n]]}(void 0===e||x.isEmptyObject(i))&&(t.nodeType?t[this.expando]=void 0:delete t[this.expando])}},hasData:function(t){var e=t[this.expando];return void 0!==e&&!x.isEmptyObject(e)}};var rt=new it,ot=new it,st=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,at=/[A-Z]/g;function lt(t,e,n){var i;if(void 0===n&&1===t.nodeType)if(i="data-"+e.replace(at,"-$&").toLowerCase(),"string"==typeof(n=t.getAttribute(i))){try{n=function(t){return"true"===t||"false"!==t&&("null"===t?null:t===+t+""?+t:st.test(t)?JSON.parse(t):t)}(n)}catch(t){}ot.set(t,e,n)}else n=void 0;return n}x.extend({hasData:function(t){return ot.hasData(t)||rt.hasData(t)},data:function(t,e,n){return ot.access(t,e,n)},removeData:function(t,e){ot.remove(t,e)},_data:function(t,e,n){return rt.access(t,e,n)},_removeData:function(t,e){rt.remove(t,e)}}),x.fn.extend({data:function(t,e){var n,i,r,o=this[0],s=o&&o.attributes;if(void 0===t){if(this.length&&(r=ot.get(o),1===o.nodeType&&!rt.get(o,"hasDataAttrs"))){for(n=s.length;n--;)s[n]&&0===(i=s[n].name).indexOf("data-")&&(i=et(i.slice(5)),lt(o,i,r[i]));rt.set(o,"hasDataAttrs",!0)}return r}return"object"==typeof t?this.each((function(){ot.set(this,t)})):K(this,(function(e){var n;if(o&&void 0===e)return void 0!==(n=ot.get(o,t))||void 0!==(n=lt(o,t))?n:void 0;this.each((function(){ot.set(this,t,e)}))}),null,e,arguments.length>1,null,!0)},removeData:function(t){return this.each((function(){ot.remove(this,t)}))}}),x.extend({queue:function(t,e,n){var i;if(t)return e=(e||"fx")+"queue",i=rt.get(t,e),n&&(!i||Array.isArray(n)?i=rt.access(t,e,x.makeArray(n)):i.push(n)),i||[]},dequeue:function(t,e){e=e||"fx";var n=x.queue(t,e),i=n.length,r=n.shift(),o=x._queueHooks(t,e);"inprogress"===r&&(r=n.shift(),i--),r&&("fx"===e&&n.unshift("inprogress"),delete o.stop,r.call(t,(function(){x.dequeue(t,e)}),o)),!i&&o&&o.empty.fire()},_queueHooks:function(t,e){var n=e+"queueHooks";return rt.get(t,n)||rt.access(t,n,{empty:x.Callbacks("once memory").add((function(){rt.remove(t,[e+"queue",n])}))})}}),x.fn.extend({queue:function(t,e){var n=2;return"string"!=typeof t&&(e=t,t="fx",n--),arguments.length<n?x.queue(this[0],t):void 0===e?this:this.each((function(){var n=x.queue(this,t,e);x._queueHooks(this,t),"fx"===t&&"inprogress"!==n[0]&&x.dequeue(this,t)}))},dequeue:function(t){return this.each((function(){x.dequeue(this,t)}))},clearQueue:function(t){return this.queue(t||"fx",[])},promise:function(t,e){var n,i=1,r=x.Deferred(),o=this,s=this.length,a=function(){--i||r.resolveWith(o,[o])};for("string"!=typeof t&&(e=t,t=void 0),t=t||"fx";s--;)(n=rt.get(o[s],t+"queueHooks"))&&n.empty&&(i++,n.empty.add(a));return a(),r.promise(e)}});var ct=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,ut=new RegExp("^(?:([+-])=|)("+ct+")([a-z%]*)$","i"),ht=["Top","Right","Bottom","Left"],ft=m.documentElement,dt=function(t){return x.contains(t.ownerDocument,t)},pt={composed:!0};ft.getRootNode&&(dt=function(t){return x.contains(t.ownerDocument,t)||t.getRootNode(pt)===t.ownerDocument});var gt=function(t,e){return"none"===(t=e||t).style.display||""===t.style.display&&dt(t)&&"none"===x.css(t,"display")};function mt(t,e,n,i){var r,o,s=20,a=i?function(){return i.cur()}:function(){return x.css(t,e,"")},l=a(),c=n&&n[3]||(x.cssNumber[e]?"":"px"),u=t.nodeType&&(x.cssNumber[e]||"px"!==c&&+l)&&ut.exec(x.css(t,e));if(u&&u[3]!==c){for(l/=2,c=c||u[3],u=+l||1;s--;)x.style(t,e,u+c),(1-o)*(1-(o=a()/l||.5))<=0&&(s=0),u/=o;u*=2,x.style(t,e,u+c),n=n||[]}return n&&(u=+u||+l||0,r=n[1]?u+(n[1]+1)*n[2]:+n[2],i&&(i.unit=c,i.start=u,i.end=r)),r}var vt={};function yt(t){var e,n=t.ownerDocument,i=t.nodeName,r=vt[i];return r||(e=n.body.appendChild(n.createElement(i)),r=x.css(e,"display"),e.parentNode.removeChild(e),"none"===r&&(r="block"),vt[i]=r,r)}function _t(t,e){for(var n,i,r=[],o=0,s=t.length;o<s;o++)(i=t[o]).style&&(n=i.style.display,e?("none"===n&&(r[o]=rt.get(i,"display")||null,r[o]||(i.style.display="")),""===i.style.display&&gt(i)&&(r[o]=yt(i))):"none"!==n&&(r[o]="none",rt.set(i,"display",n)));for(o=0;o<s;o++)null!=r[o]&&(t[o].style.display=r[o]);return t}x.fn.extend({show:function(){return _t(this,!0)},hide:function(){return _t(this)},toggle:function(t){return"boolean"==typeof t?t?this.show():this.hide():this.each((function(){gt(this)?x(this).show():x(this).hide()}))}});var bt,wt,xt=/^(?:checkbox|radio)$/i,Tt=/<([a-z][^\/\0>\x20\t\r\n\f]*)/i,Et=/^$|^module$|\/(?:java|ecma)script/i;bt=m.createDocumentFragment().appendChild(m.createElement("div")),(wt=m.createElement("input")).setAttribute("type","radio"),wt.setAttribute("checked","checked"),wt.setAttribute("name","t"),bt.appendChild(wt),d.checkClone=bt.cloneNode(!0).cloneNode(!0).lastChild.checked,bt.innerHTML="<textarea>x</textarea>",d.noCloneChecked=!!bt.cloneNode(!0).lastChild.defaultValue,bt.innerHTML="<option></option>",d.option=!!bt.lastChild;var Ct={thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]};function St(t,e){var n;return n=void 0!==t.getElementsByTagName?t.getElementsByTagName(e||"*"):void 0!==t.querySelectorAll?t.querySelectorAll(e||"*"):[],void 0===e||e&&E(t,e)?x.merge([t],n):n}function kt(t,e){for(var n=0,i=t.length;n<i;n++)rt.set(t[n],"globalEval",!e||rt.get(e[n],"globalEval"))}Ct.tbody=Ct.tfoot=Ct.colgroup=Ct.caption=Ct.thead,Ct.th=Ct.td,d.option||(Ct.optgroup=Ct.option=[1,"<select multiple='multiple'>","</select>"]);var At=/<|&#?\w+;/;function Ot(t,e,n,i,r){for(var o,s,a,l,c,u,h=e.createDocumentFragment(),f=[],d=0,p=t.length;d<p;d++)if((o=t[d])||0===o)if("object"===_(o))x.merge(f,o.nodeType?[o]:o);else if(At.test(o)){for(s=s||h.appendChild(e.createElement("div")),a=(Tt.exec(o)||["",""])[1].toLowerCase(),l=Ct[a]||Ct._default,s.innerHTML=l[1]+x.htmlPrefilter(o)+l[2],u=l[0];u--;)s=s.lastChild;x.merge(f,s.childNodes),(s=h.firstChild).textContent=""}else f.push(e.createTextNode(o));for(h.textContent="",d=0;o=f[d++];)if(i&&x.inArray(o,i)>-1)r&&r.push(o);else if(c=dt(o),s=St(h.appendChild(o),"script"),c&&kt(s),n)for(u=0;o=s[u++];)Et.test(o.type||"")&&n.push(o);return h}var Dt=/^([^.]*)(?:\.(.+)|)/;function Mt(){return!0}function Pt(){return!1}function Lt(t,e,n,i,r,o){var s,a;if("object"==typeof e){for(a in"string"!=typeof n&&(i=i||n,n=void 0),e)Lt(t,a,n,i,e[a],o);return t}if(null==i&&null==r?(r=n,i=n=void 0):null==r&&("string"==typeof n?(r=i,i=void 0):(r=i,i=n,n=void 0)),!1===r)r=Pt;else if(!r)return t;return 1===o&&(s=r,r=function(t){return x().off(t),s.apply(this,arguments)},r.guid=s.guid||(s.guid=x.guid++)),t.each((function(){x.event.add(this,e,r,i,n)}))}function jt(t,e,n){n?(rt.set(t,e,!1),x.event.add(t,e,{namespace:!1,handler:function(t){var n,i=rt.get(this,e);if(1&t.isTrigger&&this[e]){if(i)(x.event.special[e]||{}).delegateType&&t.stopPropagation();else if(i=r.call(arguments),rt.set(this,e,i),this[e](),n=rt.get(this,e),rt.set(this,e,!1),i!==n)return t.stopImmediatePropagation(),t.preventDefault(),n}else i&&(rt.set(this,e,x.event.trigger(i[0],i.slice(1),this)),t.stopPropagation(),t.isImmediatePropagationStopped=Mt)}})):void 0===rt.get(t,e)&&x.event.add(t,e,Mt)}x.event={global:{},add:function(t,e,n,i,r){var o,s,a,l,c,u,h,f,d,p,g,m=rt.get(t);if(nt(t))for(n.handler&&(n=(o=n).handler,r=o.selector),r&&x.find.matchesSelector(ft,r),n.guid||(n.guid=x.guid++),(l=m.events)||(l=m.events=Object.create(null)),(s=m.handle)||(s=m.handle=function(e){return void 0!==x&&x.event.triggered!==e.type?x.event.dispatch.apply(t,arguments):void 0}),c=(e=(e||"").match(W)||[""]).length;c--;)d=g=(a=Dt.exec(e[c])||[])[1],p=(a[2]||"").split(".").sort(),d&&(h=x.event.special[d]||{},d=(r?h.delegateType:h.bindType)||d,h=x.event.special[d]||{},u=x.extend({type:d,origType:g,data:i,handler:n,guid:n.guid,selector:r,needsContext:r&&x.expr.match.needsContext.test(r),namespace:p.join(".")},o),(f=l[d])||((f=l[d]=[]).delegateCount=0,h.setup&&!1!==h.setup.call(t,i,p,s)||t.addEventListener&&t.addEventListener(d,s)),h.add&&(h.add.call(t,u),u.handler.guid||(u.handler.guid=n.guid)),r?f.splice(f.delegateCount++,0,u):f.push(u),x.event.global[d]=!0)},remove:function(t,e,n,i,r){var o,s,a,l,c,u,h,f,d,p,g,m=rt.hasData(t)&&rt.get(t);if(m&&(l=m.events)){for(c=(e=(e||"").match(W)||[""]).length;c--;)if(d=g=(a=Dt.exec(e[c])||[])[1],p=(a[2]||"").split(".").sort(),d){for(h=x.event.special[d]||{},f=l[d=(i?h.delegateType:h.bindType)||d]||[],a=a[2]&&new RegExp("(^|\\.)"+p.join("\\.(?:.*\\.|)")+"(\\.|$)"),s=o=f.length;o--;)u=f[o],!r&&g!==u.origType||n&&n.guid!==u.guid||a&&!a.test(u.namespace)||i&&i!==u.selector&&("**"!==i||!u.selector)||(f.splice(o,1),u.selector&&f.delegateCount--,h.remove&&h.remove.call(t,u));s&&!f.length&&(h.teardown&&!1!==h.teardown.call(t,p,m.handle)||x.removeEvent(t,d,m.handle),delete l[d])}else for(d in l)x.event.remove(t,d+e[c],n,i,!0);x.isEmptyObject(l)&&rt.remove(t,"handle events")}},dispatch:function(t){var e,n,i,r,o,s,a=new Array(arguments.length),l=x.event.fix(t),c=(rt.get(this,"events")||Object.create(null))[l.type]||[],u=x.event.special[l.type]||{};for(a[0]=l,e=1;e<arguments.length;e++)a[e]=arguments[e];if(l.delegateTarget=this,!u.preDispatch||!1!==u.preDispatch.call(this,l)){for(s=x.event.handlers.call(this,l,c),e=0;(r=s[e++])&&!l.isPropagationStopped();)for(l.currentTarget=r.elem,n=0;(o=r.handlers[n++])&&!l.isImmediatePropagationStopped();)l.rnamespace&&!1!==o.namespace&&!l.rnamespace.test(o.namespace)||(l.handleObj=o,l.data=o.data,void 0!==(i=((x.event.special[o.origType]||{}).handle||o.handler).apply(r.elem,a))&&!1===(l.result=i)&&(l.preventDefault(),l.stopPropagation()));return u.postDispatch&&u.postDispatch.call(this,l),l.result}},handlers:function(t,e){var n,i,r,o,s,a=[],l=e.delegateCount,c=t.target;if(l&&c.nodeType&&!("click"===t.type&&t.button>=1))for(;c!==this;c=c.parentNode||this)if(1===c.nodeType&&("click"!==t.type||!0!==c.disabled)){for(o=[],s={},n=0;n<l;n++)void 0===s[r=(i=e[n]).selector+" "]&&(s[r]=i.needsContext?x(r,this).index(c)>-1:x.find(r,this,null,[c]).length),s[r]&&o.push(i);o.length&&a.push({elem:c,handlers:o})}return c=this,l<e.length&&a.push({elem:c,handlers:e.slice(l)}),a},addProp:function(t,e){Object.defineProperty(x.Event.prototype,t,{enumerable:!0,configurable:!0,get:p(e)?function(){if(this.originalEvent)return e(this.originalEvent)}:function(){if(this.originalEvent)return this.originalEvent[t]},set:function(e){Object.defineProperty(this,t,{enumerable:!0,configurable:!0,writable:!0,value:e})}})},fix:function(t){return t[x.expando]?t:new x.Event(t)},special:{load:{noBubble:!0},click:{setup:function(t){var e=this||t;return xt.test(e.type)&&e.click&&E(e,"input")&&jt(e,"click",!0),!1},trigger:function(t){var e=this||t;return xt.test(e.type)&&e.click&&E(e,"input")&&jt(e,"click"),!0},_default:function(t){var e=t.target;return xt.test(e.type)&&e.click&&E(e,"input")&&rt.get(e,"click")||E(e,"a")}},beforeunload:{postDispatch:function(t){void 0!==t.result&&t.originalEvent&&(t.originalEvent.returnValue=t.result)}}}},x.removeEvent=function(t,e,n){t.removeEventListener&&t.removeEventListener(e,n)},x.Event=function(t,e){if(!(this instanceof x.Event))return new x.Event(t,e);t&&t.type?(this.originalEvent=t,this.type=t.type,this.isDefaultPrevented=t.defaultPrevented||void 0===t.defaultPrevented&&!1===t.returnValue?Mt:Pt,this.target=t.target&&3===t.target.nodeType?t.target.parentNode:t.target,this.currentTarget=t.currentTarget,this.relatedTarget=t.relatedTarget):this.type=t,e&&x.extend(this,e),this.timeStamp=t&&t.timeStamp||Date.now(),this[x.expando]=!0},x.Event.prototype={constructor:x.Event,isDefaultPrevented:Pt,isPropagationStopped:Pt,isImmediatePropagationStopped:Pt,isSimulated:!1,preventDefault:function(){var t=this.originalEvent;this.isDefaultPrevented=Mt,t&&!this.isSimulated&&t.preventDefault()},stopPropagation:function(){var t=this.originalEvent;this.isPropagationStopped=Mt,t&&!this.isSimulated&&t.stopPropagation()},stopImmediatePropagation:function(){var t=this.originalEvent;this.isImmediatePropagationStopped=Mt,t&&!this.isSimulated&&t.stopImmediatePropagation(),this.stopPropagation()}},x.each({altKey:!0,bubbles:!0,cancelable:!0,changedTouches:!0,ctrlKey:!0,detail:!0,eventPhase:!0,metaKey:!0,pageX:!0,pageY:!0,shiftKey:!0,view:!0,char:!0,code:!0,charCode:!0,key:!0,keyCode:!0,button:!0,buttons:!0,clientX:!0,clientY:!0,offsetX:!0,offsetY:!0,pointerId:!0,pointerType:!0,screenX:!0,screenY:!0,targetTouches:!0,toElement:!0,touches:!0,which:!0},x.event.addProp),x.each({focus:"focusin",blur:"focusout"},(function(t,e){function n(t){if(m.documentMode){var n=rt.get(this,"handle"),i=x.event.fix(t);i.type="focusin"===t.type?"focus":"blur",i.isSimulated=!0,n(t),i.target===i.currentTarget&&n(i)}else x.event.simulate(e,t.target,x.event.fix(t))}x.event.special[t]={setup:function(){var i;if(jt(this,t,!0),!m.documentMode)return!1;(i=rt.get(this,e))||this.addEventListener(e,n),rt.set(this,e,(i||0)+1)},trigger:function(){return jt(this,t),!0},teardown:function(){var t;if(!m.documentMode)return!1;(t=rt.get(this,e)-1)?rt.set(this,e,t):(this.removeEventListener(e,n),rt.remove(this,e))},_default:function(e){return rt.get(e.target,t)},delegateType:e},x.event.special[e]={setup:function(){var i=this.ownerDocument||this.document||this,r=m.documentMode?this:i,o=rt.get(r,e);o||(m.documentMode?this.addEventListener(e,n):i.addEventListener(t,n,!0)),rt.set(r,e,(o||0)+1)},teardown:function(){var i=this.ownerDocument||this.document||this,r=m.documentMode?this:i,o=rt.get(r,e)-1;o?rt.set(r,e,o):(m.documentMode?this.removeEventListener(e,n):i.removeEventListener(t,n,!0),rt.remove(r,e))}}})),x.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},(function(t,e){x.event.special[t]={delegateType:e,bindType:e,handle:function(t){var n,i=t.relatedTarget,r=t.handleObj;return i&&(i===this||x.contains(this,i))||(t.type=r.origType,n=r.handler.apply(this,arguments),t.type=e),n}}})),x.fn.extend({on:function(t,e,n,i){return Lt(this,t,e,n,i)},one:function(t,e,n,i){return Lt(this,t,e,n,i,1)},off:function(t,e,n){var i,r;if(t&&t.preventDefault&&t.handleObj)return i=t.handleObj,x(t.delegateTarget).off(i.namespace?i.origType+"."+i.namespace:i.origType,i.selector,i.handler),this;if("object"==typeof t){for(r in t)this.off(r,e,t[r]);return this}return!1!==e&&"function"!=typeof e||(n=e,e=void 0),!1===n&&(n=Pt),this.each((function(){x.event.remove(this,t,n,e)}))}});var Nt=/<script|<style|<link/i,It=/checked\s*(?:[^=]|=\s*.checked.)/i,Rt=/^\s*<!\[CDATA\[|\]\]>\s*$/g;function zt(t,e){return E(t,"table")&&E(11!==e.nodeType?e:e.firstChild,"tr")&&x(t).children("tbody")[0]||t}function Ft(t){return t.type=(null!==t.getAttribute("type"))+"/"+t.type,t}function $t(t){return"true/"===(t.type||"").slice(0,5)?t.type=t.type.slice(5):t.removeAttribute("type"),t}function Ht(t,e){var n,i,r,o,s,a;if(1===e.nodeType){if(rt.hasData(t)&&(a=rt.get(t).events))for(r in rt.remove(e,"handle events"),a)for(n=0,i=a[r].length;n<i;n++)x.event.add(e,r,a[r][n]);ot.hasData(t)&&(o=ot.access(t),s=x.extend({},o),ot.set(e,s))}}function qt(t,e){var n=e.nodeName.toLowerCase();"input"===n&&xt.test(t.type)?e.checked=t.checked:"input"!==n&&"textarea"!==n||(e.defaultValue=t.defaultValue)}function Bt(t,e,n,i){e=o(e);var r,s,a,l,c,u,h=0,f=t.length,g=f-1,m=e[0],v=p(m);if(v||f>1&&"string"==typeof m&&!d.checkClone&&It.test(m))return t.each((function(r){var o=t.eq(r);v&&(e[0]=m.call(this,r,o.html())),Bt(o,e,n,i)}));if(f&&(s=(r=Ot(e,t[0].ownerDocument,!1,t,i)).firstChild,1===r.childNodes.length&&(r=s),s||i)){for(l=(a=x.map(St(r,"script"),Ft)).length;h<f;h++)c=r,h!==g&&(c=x.clone(c,!0,!0),l&&x.merge(a,St(c,"script"))),n.call(t[h],c,h);if(l)for(u=a[a.length-1].ownerDocument,x.map(a,$t),h=0;h<l;h++)c=a[h],Et.test(c.type||"")&&!rt.access(c,"globalEval")&&x.contains(u,c)&&(c.src&&"module"!==(c.type||"").toLowerCase()?x._evalUrl&&!c.noModule&&x._evalUrl(c.src,{nonce:c.nonce||c.getAttribute("nonce")},u):y(c.textContent.replace(Rt,""),c,u))}return t}function Wt(t,e,n){for(var i,r=e?x.filter(e,t):t,o=0;null!=(i=r[o]);o++)n||1!==i.nodeType||x.cleanData(St(i)),i.parentNode&&(n&&dt(i)&&kt(St(i,"script")),i.parentNode.removeChild(i));return t}x.extend({htmlPrefilter:function(t){return t},clone:function(t,e,n){var i,r,o,s,a=t.cloneNode(!0),l=dt(t);if(!(d.noCloneChecked||1!==t.nodeType&&11!==t.nodeType||x.isXMLDoc(t)))for(s=St(a),i=0,r=(o=St(t)).length;i<r;i++)qt(o[i],s[i]);if(e)if(n)for(o=o||St(t),s=s||St(a),i=0,r=o.length;i<r;i++)Ht(o[i],s[i]);else Ht(t,a);return(s=St(a,"script")).length>0&&kt(s,!l&&St(t,"script")),a},cleanData:function(t){for(var e,n,i,r=x.event.special,o=0;void 0!==(n=t[o]);o++)if(nt(n)){if(e=n[rt.expando]){if(e.events)for(i in e.events)r[i]?x.event.remove(n,i):x.removeEvent(n,i,e.handle);n[rt.expando]=void 0}n[ot.expando]&&(n[ot.expando]=void 0)}}}),x.fn.extend({detach:function(t){return Wt(this,t,!0)},remove:function(t){return Wt(this,t)},text:function(t){return K(this,(function(t){return void 0===t?x.text(this):this.empty().each((function(){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(this.textContent=t)}))}),null,t,arguments.length)},append:function(){return Bt(this,arguments,(function(t){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||zt(this,t).appendChild(t)}))},prepend:function(){return Bt(this,arguments,(function(t){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var e=zt(this,t);e.insertBefore(t,e.firstChild)}}))},before:function(){return Bt(this,arguments,(function(t){this.parentNode&&this.parentNode.insertBefore(t,this)}))},after:function(){return Bt(this,arguments,(function(t){this.parentNode&&this.parentNode.insertBefore(t,this.nextSibling)}))},empty:function(){for(var t,e=0;null!=(t=this[e]);e++)1===t.nodeType&&(x.cleanData(St(t,!1)),t.textContent="");return this},clone:function(t,e){return t=null!=t&&t,e=null==e?t:e,this.map((function(){return x.clone(this,t,e)}))},html:function(t){return K(this,(function(t){var e=this[0]||{},n=0,i=this.length;if(void 0===t&&1===e.nodeType)return e.innerHTML;if("string"==typeof t&&!Nt.test(t)&&!Ct[(Tt.exec(t)||["",""])[1].toLowerCase()]){t=x.htmlPrefilter(t);try{for(;n<i;n++)1===(e=this[n]||{}).nodeType&&(x.cleanData(St(e,!1)),e.innerHTML=t);e=0}catch(t){}}e&&this.empty().append(t)}),null,t,arguments.length)},replaceWith:function(){var t=[];return Bt(this,arguments,(function(e){var n=this.parentNode;x.inArray(this,t)<0&&(x.cleanData(St(this)),n&&n.replaceChild(e,this))}),t)}}),x.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},(function(t,e){x.fn[t]=function(t){for(var n,i=[],r=x(t),o=r.length-1,a=0;a<=o;a++)n=a===o?this:this.clone(!0),x(r[a])[e](n),s.apply(i,n.get());return this.pushStack(i)}}));var Xt=new RegExp("^("+ct+")(?!px)[a-z%]+$","i"),Yt=/^--/,Ut=function(e){var n=e.ownerDocument.defaultView;return n&&n.opener||(n=t),n.getComputedStyle(e)},Vt=function(t,e,n){var i,r,o={};for(r in e)o[r]=t.style[r],t.style[r]=e[r];for(r in i=n.call(t),e)t.style[r]=o[r];return i},Qt=new RegExp(ht.join("|"),"i");function Gt(t,e,n){var i,r,o,s,a=Yt.test(e),l=t.style;return(n=n||Ut(t))&&(s=n.getPropertyValue(e)||n[e],a&&s&&(s=s.replace(O,"$1")||void 0),""!==s||dt(t)||(s=x.style(t,e)),!d.pixelBoxStyles()&&Xt.test(s)&&Qt.test(e)&&(i=l.width,r=l.minWidth,o=l.maxWidth,l.minWidth=l.maxWidth=l.width=s,s=n.width,l.width=i,l.minWidth=r,l.maxWidth=o)),void 0!==s?s+"":s}function Kt(t,e){return{get:function(){if(!t())return(this.get=e).apply(this,arguments);delete this.get}}}!function(){function e(){if(u){c.style.cssText="position:absolute;left:-11111px;width:60px;margin-top:1px;padding:0;border:0",u.style.cssText="position:relative;display:block;box-sizing:border-box;overflow:scroll;margin:auto;border:1px;padding:1px;width:60%;top:1%",ft.appendChild(c).appendChild(u);var e=t.getComputedStyle(u);i="1%"!==e.top,l=12===n(e.marginLeft),u.style.right="60%",s=36===n(e.right),r=36===n(e.width),u.style.position="absolute",o=12===n(u.offsetWidth/3),ft.removeChild(c),u=null}}function n(t){return Math.round(parseFloat(t))}var i,r,o,s,a,l,c=m.createElement("div"),u=m.createElement("div");u.style&&(u.style.backgroundClip="content-box",u.cloneNode(!0).style.backgroundClip="",d.clearCloneStyle="content-box"===u.style.backgroundClip,x.extend(d,{boxSizingReliable:function(){return e(),r},pixelBoxStyles:function(){return e(),s},pixelPosition:function(){return e(),i},reliableMarginLeft:function(){return e(),l},scrollboxSize:function(){return e(),o},reliableTrDimensions:function(){var e,n,i,r;return null==a&&(e=m.createElement("table"),n=m.createElement("tr"),i=m.createElement("div"),e.style.cssText="position:absolute;left:-11111px;border-collapse:separate",n.style.cssText="box-sizing:content-box;border:1px solid",n.style.height="1px",i.style.height="9px",i.style.display="block",ft.appendChild(e).appendChild(n).appendChild(i),r=t.getComputedStyle(n),a=parseInt(r.height,10)+parseInt(r.borderTopWidth,10)+parseInt(r.borderBottomWidth,10)===n.offsetHeight,ft.removeChild(e)),a}}))}();var Jt=["Webkit","Moz","ms"],Zt=m.createElement("div").style,te={};function ee(t){return x.cssProps[t]||te[t]||(t in Zt?t:te[t]=function(t){for(var e=t[0].toUpperCase()+t.slice(1),n=Jt.length;n--;)if((t=Jt[n]+e)in Zt)return t}(t)||t)}var ne=/^(none|table(?!-c[ea]).+)/,ie={position:"absolute",visibility:"hidden",display:"block"},re={letterSpacing:"0",fontWeight:"400"};function oe(t,e,n){var i=ut.exec(e);return i?Math.max(0,i[2]-(n||0))+(i[3]||"px"):e}function se(t,e,n,i,r,o){var s="width"===e?1:0,a=0,l=0,c=0;if(n===(i?"border":"content"))return 0;for(;s<4;s+=2)"margin"===n&&(c+=x.css(t,n+ht[s],!0,r)),i?("content"===n&&(l-=x.css(t,"padding"+ht[s],!0,r)),"margin"!==n&&(l-=x.css(t,"border"+ht[s]+"Width",!0,r))):(l+=x.css(t,"padding"+ht[s],!0,r),"padding"!==n?l+=x.css(t,"border"+ht[s]+"Width",!0,r):a+=x.css(t,"border"+ht[s]+"Width",!0,r));return!i&&o>=0&&(l+=Math.max(0,Math.ceil(t["offset"+e[0].toUpperCase()+e.slice(1)]-o-l-a-.5))||0),l+c}function ae(t,e,n){var i=Ut(t),r=(!d.boxSizingReliable()||n)&&"border-box"===x.css(t,"boxSizing",!1,i),o=r,s=Gt(t,e,i),a="offset"+e[0].toUpperCase()+e.slice(1);if(Xt.test(s)){if(!n)return s;s="auto"}return(!d.boxSizingReliable()&&r||!d.reliableTrDimensions()&&E(t,"tr")||"auto"===s||!parseFloat(s)&&"inline"===x.css(t,"display",!1,i))&&t.getClientRects().length&&(r="border-box"===x.css(t,"boxSizing",!1,i),(o=a in t)&&(s=t[a])),(s=parseFloat(s)||0)+se(t,e,n||(r?"border":"content"),o,i,s)+"px"}function le(t,e,n,i,r){return new le.prototype.init(t,e,n,i,r)}x.extend({cssHooks:{opacity:{get:function(t,e){if(e){var n=Gt(t,"opacity");return""===n?"1":n}}}},cssNumber:{animationIterationCount:!0,aspectRatio:!0,borderImageSlice:!0,columnCount:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,gridArea:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnStart:!0,gridRow:!0,gridRowEnd:!0,gridRowStart:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,scale:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeMiterlimit:!0,strokeOpacity:!0},cssProps:{},style:function(t,e,n,i){if(t&&3!==t.nodeType&&8!==t.nodeType&&t.style){var r,o,s,a=et(e),l=Yt.test(e),c=t.style;if(l||(e=ee(a)),s=x.cssHooks[e]||x.cssHooks[a],void 0===n)return s&&"get"in s&&void 0!==(r=s.get(t,!1,i))?r:c[e];"string"==(o=typeof n)&&(r=ut.exec(n))&&r[1]&&(n=mt(t,e,r),o="number"),null!=n&&n==n&&("number"!==o||l||(n+=r&&r[3]||(x.cssNumber[a]?"":"px")),d.clearCloneStyle||""!==n||0!==e.indexOf("background")||(c[e]="inherit"),s&&"set"in s&&void 0===(n=s.set(t,n,i))||(l?c.setProperty(e,n):c[e]=n))}},css:function(t,e,n,i){var r,o,s,a=et(e);return Yt.test(e)||(e=ee(a)),(s=x.cssHooks[e]||x.cssHooks[a])&&"get"in s&&(r=s.get(t,!0,n)),void 0===r&&(r=Gt(t,e,i)),"normal"===r&&e in re&&(r=re[e]),""===n||n?(o=parseFloat(r),!0===n||isFinite(o)?o||0:r):r}}),x.each(["height","width"],(function(t,e){x.cssHooks[e]={get:function(t,n,i){if(n)return!ne.test(x.css(t,"display"))||t.getClientRects().length&&t.getBoundingClientRect().width?ae(t,e,i):Vt(t,ie,(function(){return ae(t,e,i)}))},set:function(t,n,i){var r,o=Ut(t),s=!d.scrollboxSize()&&"absolute"===o.position,a=(s||i)&&"border-box"===x.css(t,"boxSizing",!1,o),l=i?se(t,e,i,a,o):0;return a&&s&&(l-=Math.ceil(t["offset"+e[0].toUpperCase()+e.slice(1)]-parseFloat(o[e])-se(t,e,"border",!1,o)-.5)),l&&(r=ut.exec(n))&&"px"!==(r[3]||"px")&&(t.style[e]=n,n=x.css(t,e)),oe(0,n,l)}}})),x.cssHooks.marginLeft=Kt(d.reliableMarginLeft,(function(t,e){if(e)return(parseFloat(Gt(t,"marginLeft"))||t.getBoundingClientRect().left-Vt(t,{marginLeft:0},(function(){return t.getBoundingClientRect().left})))+"px"})),x.each({margin:"",padding:"",border:"Width"},(function(t,e){x.cssHooks[t+e]={expand:function(n){for(var i=0,r={},o="string"==typeof n?n.split(" "):[n];i<4;i++)r[t+ht[i]+e]=o[i]||o[i-2]||o[0];return r}},"margin"!==t&&(x.cssHooks[t+e].set=oe)})),x.fn.extend({css:function(t,e){return K(this,(function(t,e,n){var i,r,o={},s=0;if(Array.isArray(e)){for(i=Ut(t),r=e.length;s<r;s++)o[e[s]]=x.css(t,e[s],!1,i);return o}return void 0!==n?x.style(t,e,n):x.css(t,e)}),t,e,arguments.length>1)}}),x.Tween=le,le.prototype={constructor:le,init:function(t,e,n,i,r,o){this.elem=t,this.prop=n,this.easing=r||x.easing._default,this.options=e,this.start=this.now=this.cur(),this.end=i,this.unit=o||(x.cssNumber[n]?"":"px")},cur:function(){var t=le.propHooks[this.prop];return t&&t.get?t.get(this):le.propHooks._default.get(this)},run:function(t){var e,n=le.propHooks[this.prop];return this.options.duration?this.pos=e=x.easing[this.easing](t,this.options.duration*t,0,1,this.options.duration):this.pos=e=t,this.now=(this.end-this.start)*e+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),n&&n.set?n.set(this):le.propHooks._default.set(this),this}},le.prototype.init.prototype=le.prototype,le.propHooks={_default:{get:function(t){var e;return 1!==t.elem.nodeType||null!=t.elem[t.prop]&&null==t.elem.style[t.prop]?t.elem[t.prop]:(e=x.css(t.elem,t.prop,""))&&"auto"!==e?e:0},set:function(t){x.fx.step[t.prop]?x.fx.step[t.prop](t):1!==t.elem.nodeType||!x.cssHooks[t.prop]&&null==t.elem.style[ee(t.prop)]?t.elem[t.prop]=t.now:x.style(t.elem,t.prop,t.now+t.unit)}}},le.propHooks.scrollTop=le.propHooks.scrollLeft={set:function(t){t.elem.nodeType&&t.elem.parentNode&&(t.elem[t.prop]=t.now)}},x.easing={linear:function(t){return t},swing:function(t){return.5-Math.cos(t*Math.PI)/2},_default:"swing"},x.fx=le.prototype.init,x.fx.step={};var ce,ue,he=/^(?:toggle|show|hide)$/,fe=/queueHooks$/;function de(){ue&&(!1===m.hidden&&t.requestAnimationFrame?t.requestAnimationFrame(de):t.setTimeout(de,x.fx.interval),x.fx.tick())}function pe(){return t.setTimeout((function(){ce=void 0})),ce=Date.now()}function ge(t,e){var n,i=0,r={height:t};for(e=e?1:0;i<4;i+=2-e)r["margin"+(n=ht[i])]=r["padding"+n]=t;return e&&(r.opacity=r.width=t),r}function me(t,e,n){for(var i,r=(ve.tweeners[e]||[]).concat(ve.tweeners["*"]),o=0,s=r.length;o<s;o++)if(i=r[o].call(n,e,t))return i}function ve(t,e,n){var i,r,o=0,s=ve.prefilters.length,a=x.Deferred().always((function(){delete l.elem})),l=function(){if(r)return!1;for(var e=ce||pe(),n=Math.max(0,c.startTime+c.duration-e),i=1-(n/c.duration||0),o=0,s=c.tweens.length;o<s;o++)c.tweens[o].run(i);return a.notifyWith(t,[c,i,n]),i<1&&s?n:(s||a.notifyWith(t,[c,1,0]),a.resolveWith(t,[c]),!1)},c=a.promise({elem:t,props:x.extend({},e),opts:x.extend(!0,{specialEasing:{},easing:x.easing._default},n),originalProperties:e,originalOptions:n,startTime:ce||pe(),duration:n.duration,tweens:[],createTween:function(e,n){var i=x.Tween(t,c.opts,e,n,c.opts.specialEasing[e]||c.opts.easing);return c.tweens.push(i),i},stop:function(e){var n=0,i=e?c.tweens.length:0;if(r)return this;for(r=!0;n<i;n++)c.tweens[n].run(1);return e?(a.notifyWith(t,[c,1,0]),a.resolveWith(t,[c,e])):a.rejectWith(t,[c,e]),this}}),u=c.props;for(function(t,e){var n,i,r,o,s;for(n in t)if(r=e[i=et(n)],o=t[n],Array.isArray(o)&&(r=o[1],o=t[n]=o[0]),n!==i&&(t[i]=o,delete t[n]),(s=x.cssHooks[i])&&"expand"in s)for(n in o=s.expand(o),delete t[i],o)n in t||(t[n]=o[n],e[n]=r);else e[i]=r}(u,c.opts.specialEasing);o<s;o++)if(i=ve.prefilters[o].call(c,t,u,c.opts))return p(i.stop)&&(x._queueHooks(c.elem,c.opts.queue).stop=i.stop.bind(i)),i;return x.map(u,me,c),p(c.opts.start)&&c.opts.start.call(t,c),c.progress(c.opts.progress).done(c.opts.done,c.opts.complete).fail(c.opts.fail).always(c.opts.always),x.fx.timer(x.extend(l,{elem:t,anim:c,queue:c.opts.queue})),c}x.Animation=x.extend(ve,{tweeners:{"*":[function(t,e){var n=this.createTween(t,e);return mt(n.elem,t,ut.exec(e),n),n}]},tweener:function(t,e){p(t)?(e=t,t=["*"]):t=t.match(W);for(var n,i=0,r=t.length;i<r;i++)n=t[i],ve.tweeners[n]=ve.tweeners[n]||[],ve.tweeners[n].unshift(e)},prefilters:[function(t,e,n){var i,r,o,s,a,l,c,u,h="width"in e||"height"in e,f=this,d={},p=t.style,g=t.nodeType&&gt(t),m=rt.get(t,"fxshow");for(i in n.queue||(null==(s=x._queueHooks(t,"fx")).unqueued&&(s.unqueued=0,a=s.empty.fire,s.empty.fire=function(){s.unqueued||a()}),s.unqueued++,f.always((function(){f.always((function(){s.unqueued--,x.queue(t,"fx").length||s.empty.fire()}))}))),e)if(r=e[i],he.test(r)){if(delete e[i],o=o||"toggle"===r,r===(g?"hide":"show")){if("show"!==r||!m||void 0===m[i])continue;g=!0}d[i]=m&&m[i]||x.style(t,i)}if((l=!x.isEmptyObject(e))||!x.isEmptyObject(d))for(i in h&&1===t.nodeType&&(n.overflow=[p.overflow,p.overflowX,p.overflowY],null==(c=m&&m.display)&&(c=rt.get(t,"display")),"none"===(u=x.css(t,"display"))&&(c?u=c:(_t([t],!0),c=t.style.display||c,u=x.css(t,"display"),_t([t]))),("inline"===u||"inline-block"===u&&null!=c)&&"none"===x.css(t,"float")&&(l||(f.done((function(){p.display=c})),null==c&&(u=p.display,c="none"===u?"":u)),p.display="inline-block")),n.overflow&&(p.overflow="hidden",f.always((function(){p.overflow=n.overflow[0],p.overflowX=n.overflow[1],p.overflowY=n.overflow[2]}))),l=!1,d)l||(m?"hidden"in m&&(g=m.hidden):m=rt.access(t,"fxshow",{display:c}),o&&(m.hidden=!g),g&&_t([t],!0),f.done((function(){for(i in g||_t([t]),rt.remove(t,"fxshow"),d)x.style(t,i,d[i])}))),l=me(g?m[i]:0,i,f),i in m||(m[i]=l.start,g&&(l.end=l.start,l.start=0))}],prefilter:function(t,e){e?ve.prefilters.unshift(t):ve.prefilters.push(t)}}),x.speed=function(t,e,n){var i=t&&"object"==typeof t?x.extend({},t):{complete:n||!n&&e||p(t)&&t,duration:t,easing:n&&e||e&&!p(e)&&e};return x.fx.off?i.duration=0:"number"!=typeof i.duration&&(i.duration in x.fx.speeds?i.duration=x.fx.speeds[i.duration]:i.duration=x.fx.speeds._default),null!=i.queue&&!0!==i.queue||(i.queue="fx"),i.old=i.complete,i.complete=function(){p(i.old)&&i.old.call(this),i.queue&&x.dequeue(this,i.queue)},i},x.fn.extend({fadeTo:function(t,e,n,i){return this.filter(gt).css("opacity",0).show().end().animate({opacity:e},t,n,i)},animate:function(t,e,n,i){var r=x.isEmptyObject(t),o=x.speed(e,n,i),s=function(){var e=ve(this,x.extend({},t),o);(r||rt.get(this,"finish"))&&e.stop(!0)};return s.finish=s,r||!1===o.queue?this.each(s):this.queue(o.queue,s)},stop:function(t,e,n){var i=function(t){var e=t.stop;delete t.stop,e(n)};return"string"!=typeof t&&(n=e,e=t,t=void 0),e&&this.queue(t||"fx",[]),this.each((function(){var e=!0,r=null!=t&&t+"queueHooks",o=x.timers,s=rt.get(this);if(r)s[r]&&s[r].stop&&i(s[r]);else for(r in s)s[r]&&s[r].stop&&fe.test(r)&&i(s[r]);for(r=o.length;r--;)o[r].elem!==this||null!=t&&o[r].queue!==t||(o[r].anim.stop(n),e=!1,o.splice(r,1));!e&&n||x.dequeue(this,t)}))},finish:function(t){return!1!==t&&(t=t||"fx"),this.each((function(){var e,n=rt.get(this),i=n[t+"queue"],r=n[t+"queueHooks"],o=x.timers,s=i?i.length:0;for(n.finish=!0,x.queue(this,t,[]),r&&r.stop&&r.stop.call(this,!0),e=o.length;e--;)o[e].elem===this&&o[e].queue===t&&(o[e].anim.stop(!0),o.splice(e,1));for(e=0;e<s;e++)i[e]&&i[e].finish&&i[e].finish.call(this);delete n.finish}))}}),x.each(["toggle","show","hide"],(function(t,e){var n=x.fn[e];x.fn[e]=function(t,i,r){return null==t||"boolean"==typeof t?n.apply(this,arguments):this.animate(ge(e,!0),t,i,r)}})),x.each({slideDown:ge("show"),slideUp:ge("hide"),slideToggle:ge("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},(function(t,e){x.fn[t]=function(t,n,i){return this.animate(e,t,n,i)}})),x.timers=[],x.fx.tick=function(){var t,e=0,n=x.timers;for(ce=Date.now();e<n.length;e++)(t=n[e])()||n[e]!==t||n.splice(e--,1);n.length||x.fx.stop(),ce=void 0},x.fx.timer=function(t){x.timers.push(t),x.fx.start()},x.fx.interval=13,x.fx.start=function(){ue||(ue=!0,de())},x.fx.stop=function(){ue=null},x.fx.speeds={slow:600,fast:200,_default:400},x.fn.delay=function(e,n){return e=x.fx&&x.fx.speeds[e]||e,n=n||"fx",this.queue(n,(function(n,i){var r=t.setTimeout(n,e);i.stop=function(){t.clearTimeout(r)}}))},function(){var t=m.createElement("input"),e=m.createElement("select").appendChild(m.createElement("option"));t.type="checkbox",d.checkOn=""!==t.value,d.optSelected=e.selected,(t=m.createElement("input")).value="t",t.type="radio",d.radioValue="t"===t.value}();var ye,_e=x.expr.attrHandle;x.fn.extend({attr:function(t,e){return K(this,x.attr,t,e,arguments.length>1)},removeAttr:function(t){return this.each((function(){x.removeAttr(this,t)}))}}),x.extend({attr:function(t,e,n){var i,r,o=t.nodeType;if(3!==o&&8!==o&&2!==o)return void 0===t.getAttribute?x.prop(t,e,n):(1===o&&x.isXMLDoc(t)||(r=x.attrHooks[e.toLowerCase()]||(x.expr.match.bool.test(e)?ye:void 0)),void 0!==n?null===n?void x.removeAttr(t,e):r&&"set"in r&&void 0!==(i=r.set(t,n,e))?i:(t.setAttribute(e,n+""),n):r&&"get"in r&&null!==(i=r.get(t,e))?i:null==(i=x.find.attr(t,e))?void 0:i)},attrHooks:{type:{set:function(t,e){if(!d.radioValue&&"radio"===e&&E(t,"input")){var n=t.value;return t.setAttribute("type",e),n&&(t.value=n),e}}}},removeAttr:function(t,e){var n,i=0,r=e&&e.match(W);if(r&&1===t.nodeType)for(;n=r[i++];)t.removeAttribute(n)}}),ye={set:function(t,e,n){return!1===e?x.removeAttr(t,n):t.setAttribute(n,n),n}},x.each(x.expr.match.bool.source.match(/\w+/g),(function(t,e){var n=_e[e]||x.find.attr;_e[e]=function(t,e,i){var r,o,s=e.toLowerCase();return i||(o=_e[s],_e[s]=r,r=null!=n(t,e,i)?s:null,_e[s]=o),r}}));var be=/^(?:input|select|textarea|button)$/i,we=/^(?:a|area)$/i;function xe(t){return(t.match(W)||[]).join(" ")}function Te(t){return t.getAttribute&&t.getAttribute("class")||""}function Ee(t){return Array.isArray(t)?t:"string"==typeof t&&t.match(W)||[]}x.fn.extend({prop:function(t,e){return K(this,x.prop,t,e,arguments.length>1)},removeProp:function(t){return this.each((function(){delete this[x.propFix[t]||t]}))}}),x.extend({prop:function(t,e,n){var i,r,o=t.nodeType;if(3!==o&&8!==o&&2!==o)return 1===o&&x.isXMLDoc(t)||(e=x.propFix[e]||e,r=x.propHooks[e]),void 0!==n?r&&"set"in r&&void 0!==(i=r.set(t,n,e))?i:t[e]=n:r&&"get"in r&&null!==(i=r.get(t,e))?i:t[e]},propHooks:{tabIndex:{get:function(t){var e=x.find.attr(t,"tabindex");return e?parseInt(e,10):be.test(t.nodeName)||we.test(t.nodeName)&&t.href?0:-1}}},propFix:{for:"htmlFor",class:"className"}}),d.optSelected||(x.propHooks.selected={get:function(t){var e=t.parentNode;return e&&e.parentNode&&e.parentNode.selectedIndex,null},set:function(t){var e=t.parentNode;e&&(e.selectedIndex,e.parentNode&&e.parentNode.selectedIndex)}}),x.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],(function(){x.propFix[this.toLowerCase()]=this})),x.fn.extend({addClass:function(t){var e,n,i,r,o,s;return p(t)?this.each((function(e){x(this).addClass(t.call(this,e,Te(this)))})):(e=Ee(t)).length?this.each((function(){if(i=Te(this),n=1===this.nodeType&&" "+xe(i)+" "){for(o=0;o<e.length;o++)r=e[o],n.indexOf(" "+r+" ")<0&&(n+=r+" ");s=xe(n),i!==s&&this.setAttribute("class",s)}})):this},removeClass:function(t){var e,n,i,r,o,s;return p(t)?this.each((function(e){x(this).removeClass(t.call(this,e,Te(this)))})):arguments.length?(e=Ee(t)).length?this.each((function(){if(i=Te(this),n=1===this.nodeType&&" "+xe(i)+" "){for(o=0;o<e.length;o++)for(r=e[o];n.indexOf(" "+r+" ")>-1;)n=n.replace(" "+r+" "," ");s=xe(n),i!==s&&this.setAttribute("class",s)}})):this:this.attr("class","")},toggleClass:function(t,e){var n,i,r,o,s=typeof t,a="string"===s||Array.isArray(t);return p(t)?this.each((function(n){x(this).toggleClass(t.call(this,n,Te(this),e),e)})):"boolean"==typeof e&&a?e?this.addClass(t):this.removeClass(t):(n=Ee(t),this.each((function(){if(a)for(o=x(this),r=0;r<n.length;r++)i=n[r],o.hasClass(i)?o.removeClass(i):o.addClass(i);else void 0!==t&&"boolean"!==s||((i=Te(this))&&rt.set(this,"__className__",i),this.setAttribute&&this.setAttribute("class",i||!1===t?"":rt.get(this,"__className__")||""))})))},hasClass:function(t){var e,n,i=0;for(e=" "+t+" ";n=this[i++];)if(1===n.nodeType&&(" "+xe(Te(n))+" ").indexOf(e)>-1)return!0;return!1}});var Ce=/\r/g;x.fn.extend({val:function(t){var e,n,i,r=this[0];return arguments.length?(i=p(t),this.each((function(n){var r;1===this.nodeType&&(null==(r=i?t.call(this,n,x(this).val()):t)?r="":"number"==typeof r?r+="":Array.isArray(r)&&(r=x.map(r,(function(t){return null==t?"":t+""}))),(e=x.valHooks[this.type]||x.valHooks[this.nodeName.toLowerCase()])&&"set"in e&&void 0!==e.set(this,r,"value")||(this.value=r))}))):r?(e=x.valHooks[r.type]||x.valHooks[r.nodeName.toLowerCase()])&&"get"in e&&void 0!==(n=e.get(r,"value"))?n:"string"==typeof(n=r.value)?n.replace(Ce,""):null==n?"":n:void 0}}),x.extend({valHooks:{option:{get:function(t){var e=x.find.attr(t,"value");return null!=e?e:xe(x.text(t))}},select:{get:function(t){var e,n,i,r=t.options,o=t.selectedIndex,s="select-one"===t.type,a=s?null:[],l=s?o+1:r.length;for(i=o<0?l:s?o:0;i<l;i++)if(((n=r[i]).selected||i===o)&&!n.disabled&&(!n.parentNode.disabled||!E(n.parentNode,"optgroup"))){if(e=x(n).val(),s)return e;a.push(e)}return a},set:function(t,e){for(var n,i,r=t.options,o=x.makeArray(e),s=r.length;s--;)((i=r[s]).selected=x.inArray(x.valHooks.option.get(i),o)>-1)&&(n=!0);return n||(t.selectedIndex=-1),o}}}}),x.each(["radio","checkbox"],(function(){x.valHooks[this]={set:function(t,e){if(Array.isArray(e))return t.checked=x.inArray(x(t).val(),e)>-1}},d.checkOn||(x.valHooks[this].get=function(t){return null===t.getAttribute("value")?"on":t.value})}));var Se=t.location,ke={guid:Date.now()},Ae=/\?/;x.parseXML=function(e){var n,i;if(!e||"string"!=typeof e)return null;try{n=(new t.DOMParser).parseFromString(e,"text/xml")}catch(t){}return i=n&&n.getElementsByTagName("parsererror")[0],n&&!i||x.error("Invalid XML: "+(i?x.map(i.childNodes,(function(t){return t.textContent})).join("\n"):e)),n};var Oe=/^(?:focusinfocus|focusoutblur)$/,De=function(t){t.stopPropagation()};x.extend(x.event,{trigger:function(e,n,i,r){var o,s,a,l,c,h,f,d,v=[i||m],y=u.call(e,"type")?e.type:e,_=u.call(e,"namespace")?e.namespace.split("."):[];if(s=d=a=i=i||m,3!==i.nodeType&&8!==i.nodeType&&!Oe.test(y+x.event.triggered)&&(y.indexOf(".")>-1&&(_=y.split("."),y=_.shift(),_.sort()),c=y.indexOf(":")<0&&"on"+y,(e=e[x.expando]?e:new x.Event(y,"object"==typeof e&&e)).isTrigger=r?2:3,e.namespace=_.join("."),e.rnamespace=e.namespace?new RegExp("(^|\\.)"+_.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,e.result=void 0,e.target||(e.target=i),n=null==n?[e]:x.makeArray(n,[e]),f=x.event.special[y]||{},r||!f.trigger||!1!==f.trigger.apply(i,n))){if(!r&&!f.noBubble&&!g(i)){for(l=f.delegateType||y,Oe.test(l+y)||(s=s.parentNode);s;s=s.parentNode)v.push(s),a=s;a===(i.ownerDocument||m)&&v.push(a.defaultView||a.parentWindow||t)}for(o=0;(s=v[o++])&&!e.isPropagationStopped();)d=s,e.type=o>1?l:f.bindType||y,(h=(rt.get(s,"events")||Object.create(null))[e.type]&&rt.get(s,"handle"))&&h.apply(s,n),(h=c&&s[c])&&h.apply&&nt(s)&&(e.result=h.apply(s,n),!1===e.result&&e.preventDefault());return e.type=y,r||e.isDefaultPrevented()||f._default&&!1!==f._default.apply(v.pop(),n)||!nt(i)||c&&p(i[y])&&!g(i)&&((a=i[c])&&(i[c]=null),x.event.triggered=y,e.isPropagationStopped()&&d.addEventListener(y,De),i[y](),e.isPropagationStopped()&&d.removeEventListener(y,De),x.event.triggered=void 0,a&&(i[c]=a)),e.result}},simulate:function(t,e,n){var i=x.extend(new x.Event,n,{type:t,isSimulated:!0});x.event.trigger(i,null,e)}}),x.fn.extend({trigger:function(t,e){return this.each((function(){x.event.trigger(t,e,this)}))},triggerHandler:function(t,e){var n=this[0];if(n)return x.event.trigger(t,e,n,!0)}});var Me=/\[\]$/,Pe=/\r?\n/g,Le=/^(?:submit|button|image|reset|file)$/i,je=/^(?:input|select|textarea|keygen)/i;function Ne(t,e,n,i){var r;if(Array.isArray(e))x.each(e,(function(e,r){n||Me.test(t)?i(t,r):Ne(t+"["+("object"==typeof r&&null!=r?e:"")+"]",r,n,i)}));else if(n||"object"!==_(e))i(t,e);else for(r in e)Ne(t+"["+r+"]",e[r],n,i)}x.param=function(t,e){var n,i=[],r=function(t,e){var n=p(e)?e():e;i[i.length]=encodeURIComponent(t)+"="+encodeURIComponent(null==n?"":n)};if(null==t)return"";if(Array.isArray(t)||t.jquery&&!x.isPlainObject(t))x.each(t,(function(){r(this.name,this.value)}));else for(n in t)Ne(n,t[n],e,r);return i.join("&")},x.fn.extend({serialize:function(){return x.param(this.serializeArray())},serializeArray:function(){return this.map((function(){var t=x.prop(this,"elements");return t?x.makeArray(t):this})).filter((function(){var t=this.type;return this.name&&!x(this).is(":disabled")&&je.test(this.nodeName)&&!Le.test(t)&&(this.checked||!xt.test(t))})).map((function(t,e){var n=x(this).val();return null==n?null:Array.isArray(n)?x.map(n,(function(t){return{name:e.name,value:t.replace(Pe,"\r\n")}})):{name:e.name,value:n.replace(Pe,"\r\n")}})).get()}});var Ie=/%20/g,Re=/#.*$/,ze=/([?&])_=[^&]*/,Fe=/^(.*?):[ \t]*([^\r\n]*)$/gm,$e=/^(?:GET|HEAD)$/,He=/^\/\//,qe={},Be={},We="*/".concat("*"),Xe=m.createElement("a");function Ye(t){return function(e,n){"string"!=typeof e&&(n=e,e="*");var i,r=0,o=e.toLowerCase().match(W)||[];if(p(n))for(;i=o[r++];)"+"===i[0]?(i=i.slice(1)||"*",(t[i]=t[i]||[]).unshift(n)):(t[i]=t[i]||[]).push(n)}}function Ue(t,e,n,i){var r={},o=t===Be;function s(a){var l;return r[a]=!0,x.each(t[a]||[],(function(t,a){var c=a(e,n,i);return"string"!=typeof c||o||r[c]?o?!(l=c):void 0:(e.dataTypes.unshift(c),s(c),!1)})),l}return s(e.dataTypes[0])||!r["*"]&&s("*")}function Ve(t,e){var n,i,r=x.ajaxSettings.flatOptions||{};for(n in e)void 0!==e[n]&&((r[n]?t:i||(i={}))[n]=e[n]);return i&&x.extend(!0,t,i),t}Xe.href=Se.href,x.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:Se.href,type:"GET",isLocal:/^(?:about|app|app-storage|.+-extension|file|res|widget):$/.test(Se.protocol),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":We,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":JSON.parse,"text xml":x.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(t,e){return e?Ve(Ve(t,x.ajaxSettings),e):Ve(x.ajaxSettings,t)},ajaxPrefilter:Ye(qe),ajaxTransport:Ye(Be),ajax:function(e,n){"object"==typeof e&&(n=e,e=void 0),n=n||{};var i,r,o,s,a,l,c,u,h,f,d=x.ajaxSetup({},n),p=d.context||d,g=d.context&&(p.nodeType||p.jquery)?x(p):x.event,v=x.Deferred(),y=x.Callbacks("once memory"),_=d.statusCode||{},b={},w={},T="canceled",E={readyState:0,getResponseHeader:function(t){var e;if(c){if(!s)for(s={};e=Fe.exec(o);)s[e[1].toLowerCase()+" "]=(s[e[1].toLowerCase()+" "]||[]).concat(e[2]);e=s[t.toLowerCase()+" "]}return null==e?null:e.join(", ")},getAllResponseHeaders:function(){return c?o:null},setRequestHeader:function(t,e){return null==c&&(t=w[t.toLowerCase()]=w[t.toLowerCase()]||t,b[t]=e),this},overrideMimeType:function(t){return null==c&&(d.mimeType=t),this},statusCode:function(t){var e;if(t)if(c)E.always(t[E.status]);else for(e in t)_[e]=[_[e],t[e]];return this},abort:function(t){var e=t||T;return i&&i.abort(e),C(0,e),this}};if(v.promise(E),d.url=((e||d.url||Se.href)+"").replace(He,Se.protocol+"//"),d.type=n.method||n.type||d.method||d.type,d.dataTypes=(d.dataType||"*").toLowerCase().match(W)||[""],null==d.crossDomain){l=m.createElement("a");try{l.href=d.url,l.href=l.href,d.crossDomain=Xe.protocol+"//"+Xe.host!=l.protocol+"//"+l.host}catch(t){d.crossDomain=!0}}if(d.data&&d.processData&&"string"!=typeof d.data&&(d.data=x.param(d.data,d.traditional)),Ue(qe,d,n,E),c)return E;for(h in(u=x.event&&d.global)&&0==x.active++&&x.event.trigger("ajaxStart"),d.type=d.type.toUpperCase(),d.hasContent=!$e.test(d.type),r=d.url.replace(Re,""),d.hasContent?d.data&&d.processData&&0===(d.contentType||"").indexOf("application/x-www-form-urlencoded")&&(d.data=d.data.replace(Ie,"+")):(f=d.url.slice(r.length),d.data&&(d.processData||"string"==typeof d.data)&&(r+=(Ae.test(r)?"&":"?")+d.data,delete d.data),!1===d.cache&&(r=r.replace(ze,"$1"),f=(Ae.test(r)?"&":"?")+"_="+ke.guid+++f),d.url=r+f),d.ifModified&&(x.lastModified[r]&&E.setRequestHeader("If-Modified-Since",x.lastModified[r]),x.etag[r]&&E.setRequestHeader("If-None-Match",x.etag[r])),(d.data&&d.hasContent&&!1!==d.contentType||n.contentType)&&E.setRequestHeader("Content-Type",d.contentType),E.setRequestHeader("Accept",d.dataTypes[0]&&d.accepts[d.dataTypes[0]]?d.accepts[d.dataTypes[0]]+("*"!==d.dataTypes[0]?", "+We+"; q=0.01":""):d.accepts["*"]),d.headers)E.setRequestHeader(h,d.headers[h]);if(d.beforeSend&&(!1===d.beforeSend.call(p,E,d)||c))return E.abort();if(T="abort",y.add(d.complete),E.done(d.success),E.fail(d.error),i=Ue(Be,d,n,E)){if(E.readyState=1,u&&g.trigger("ajaxSend",[E,d]),c)return E;d.async&&d.timeout>0&&(a=t.setTimeout((function(){E.abort("timeout")}),d.timeout));try{c=!1,i.send(b,C)}catch(t){if(c)throw t;C(-1,t)}}else C(-1,"No Transport");function C(e,n,s,l){var h,f,m,b,w,T=n;c||(c=!0,a&&t.clearTimeout(a),i=void 0,o=l||"",E.readyState=e>0?4:0,h=e>=200&&e<300||304===e,s&&(b=function(t,e,n){for(var i,r,o,s,a=t.contents,l=t.dataTypes;"*"===l[0];)l.shift(),void 0===i&&(i=t.mimeType||e.getResponseHeader("Content-Type"));if(i)for(r in a)if(a[r]&&a[r].test(i)){l.unshift(r);break}if(l[0]in n)o=l[0];else{for(r in n){if(!l[0]||t.converters[r+" "+l[0]]){o=r;break}s||(s=r)}o=o||s}if(o)return o!==l[0]&&l.unshift(o),n[o]}(d,E,s)),!h&&x.inArray("script",d.dataTypes)>-1&&x.inArray("json",d.dataTypes)<0&&(d.converters["text script"]=function(){}),b=function(t,e,n,i){var r,o,s,a,l,c={},u=t.dataTypes.slice();if(u[1])for(s in t.converters)c[s.toLowerCase()]=t.converters[s];for(o=u.shift();o;)if(t.responseFields[o]&&(n[t.responseFields[o]]=e),!l&&i&&t.dataFilter&&(e=t.dataFilter(e,t.dataType)),l=o,o=u.shift())if("*"===o)o=l;else if("*"!==l&&l!==o){if(!(s=c[l+" "+o]||c["* "+o]))for(r in c)if((a=r.split(" "))[1]===o&&(s=c[l+" "+a[0]]||c["* "+a[0]])){!0===s?s=c[r]:!0!==c[r]&&(o=a[0],u.unshift(a[1]));break}if(!0!==s)if(s&&t.throws)e=s(e);else try{e=s(e)}catch(t){return{state:"parsererror",error:s?t:"No conversion from "+l+" to "+o}}}return{state:"success",data:e}}(d,b,E,h),h?(d.ifModified&&((w=E.getResponseHeader("Last-Modified"))&&(x.lastModified[r]=w),(w=E.getResponseHeader("etag"))&&(x.etag[r]=w)),204===e||"HEAD"===d.type?T="nocontent":304===e?T="notmodified":(T=b.state,f=b.data,h=!(m=b.error))):(m=T,!e&&T||(T="error",e<0&&(e=0))),E.status=e,E.statusText=(n||T)+"",h?v.resolveWith(p,[f,T,E]):v.rejectWith(p,[E,T,m]),E.statusCode(_),_=void 0,u&&g.trigger(h?"ajaxSuccess":"ajaxError",[E,d,h?f:m]),y.fireWith(p,[E,T]),u&&(g.trigger("ajaxComplete",[E,d]),--x.active||x.event.trigger("ajaxStop")))}return E},getJSON:function(t,e,n){return x.get(t,e,n,"json")},getScript:function(t,e){return x.get(t,void 0,e,"script")}}),x.each(["get","post"],(function(t,e){x[e]=function(t,n,i,r){return p(n)&&(r=r||i,i=n,n=void 0),x.ajax(x.extend({url:t,type:e,dataType:r,data:n,success:i},x.isPlainObject(t)&&t))}})),x.ajaxPrefilter((function(t){var e;for(e in t.headers)"content-type"===e.toLowerCase()&&(t.contentType=t.headers[e]||"")})),x._evalUrl=function(t,e,n){return x.ajax({url:t,type:"GET",dataType:"script",cache:!0,async:!1,global:!1,converters:{"text script":function(){}},dataFilter:function(t){x.globalEval(t,e,n)}})},x.fn.extend({wrapAll:function(t){var e;return this[0]&&(p(t)&&(t=t.call(this[0])),e=x(t,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&e.insertBefore(this[0]),e.map((function(){for(var t=this;t.firstElementChild;)t=t.firstElementChild;return t})).append(this)),this},wrapInner:function(t){return p(t)?this.each((function(e){x(this).wrapInner(t.call(this,e))})):this.each((function(){var e=x(this),n=e.contents();n.length?n.wrapAll(t):e.append(t)}))},wrap:function(t){var e=p(t);return this.each((function(n){x(this).wrapAll(e?t.call(this,n):t)}))},unwrap:function(t){return this.parent(t).not("body").each((function(){x(this).replaceWith(this.childNodes)})),this}}),x.expr.pseudos.hidden=function(t){return!x.expr.pseudos.visible(t)},x.expr.pseudos.visible=function(t){return!!(t.offsetWidth||t.offsetHeight||t.getClientRects().length)},x.ajaxSettings.xhr=function(){try{return new t.XMLHttpRequest}catch(t){}};var Qe={0:200,1223:204},Ge=x.ajaxSettings.xhr();d.cors=!!Ge&&"withCredentials"in Ge,d.ajax=Ge=!!Ge,x.ajaxTransport((function(e){var n,i;if(d.cors||Ge&&!e.crossDomain)return{send:function(r,o){var s,a=e.xhr();if(a.open(e.type,e.url,e.async,e.username,e.password),e.xhrFields)for(s in e.xhrFields)a[s]=e.xhrFields[s];for(s in e.mimeType&&a.overrideMimeType&&a.overrideMimeType(e.mimeType),e.crossDomain||r["X-Requested-With"]||(r["X-Requested-With"]="XMLHttpRequest"),r)a.setRequestHeader(s,r[s]);n=function(t){return function(){n&&(n=i=a.onload=a.onerror=a.onabort=a.ontimeout=a.onreadystatechange=null,"abort"===t?a.abort():"error"===t?"number"!=typeof a.status?o(0,"error"):o(a.status,a.statusText):o(Qe[a.status]||a.status,a.statusText,"text"!==(a.responseType||"text")||"string"!=typeof a.responseText?{binary:a.response}:{text:a.responseText},a.getAllResponseHeaders()))}},a.onload=n(),i=a.onerror=a.ontimeout=n("error"),void 0!==a.onabort?a.onabort=i:a.onreadystatechange=function(){4===a.readyState&&t.setTimeout((function(){n&&i()}))},n=n("abort");try{a.send(e.hasContent&&e.data||null)}catch(t){if(n)throw t}},abort:function(){n&&n()}}})),x.ajaxPrefilter((function(t){t.crossDomain&&(t.contents.script=!1)})),x.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(t){return x.globalEval(t),t}}}),x.ajaxPrefilter("script",(function(t){void 0===t.cache&&(t.cache=!1),t.crossDomain&&(t.type="GET")})),x.ajaxTransport("script",(function(t){var e,n;if(t.crossDomain||t.scriptAttrs)return{send:function(i,r){e=x("<script>").attr(t.scriptAttrs||{}).prop({charset:t.scriptCharset,src:t.url}).on("load error",n=function(t){e.remove(),n=null,t&&r("error"===t.type?404:200,t.type)}),m.head.appendChild(e[0])},abort:function(){n&&n()}}}));var Ke,Je=[],Ze=/(=)\?(?=&|$)|\?\?/;x.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var t=Je.pop()||x.expando+"_"+ke.guid++;return this[t]=!0,t}}),x.ajaxPrefilter("json jsonp",(function(e,n,i){var r,o,s,a=!1!==e.jsonp&&(Ze.test(e.url)?"url":"string"==typeof e.data&&0===(e.contentType||"").indexOf("application/x-www-form-urlencoded")&&Ze.test(e.data)&&"data");if(a||"jsonp"===e.dataTypes[0])return r=e.jsonpCallback=p(e.jsonpCallback)?e.jsonpCallback():e.jsonpCallback,a?e[a]=e[a].replace(Ze,"$1"+r):!1!==e.jsonp&&(e.url+=(Ae.test(e.url)?"&":"?")+e.jsonp+"="+r),e.converters["script json"]=function(){return s||x.error(r+" was not called"),s[0]},e.dataTypes[0]="json",o=t[r],t[r]=function(){s=arguments},i.always((function(){void 0===o?x(t).removeProp(r):t[r]=o,e[r]&&(e.jsonpCallback=n.jsonpCallback,Je.push(r)),s&&p(o)&&o(s[0]),s=o=void 0})),"script"})),d.createHTMLDocument=((Ke=m.implementation.createHTMLDocument("").body).innerHTML="<form></form><form></form>",2===Ke.childNodes.length),x.parseHTML=function(t,e,n){return"string"!=typeof t?[]:("boolean"==typeof e&&(n=e,e=!1),e||(d.createHTMLDocument?((i=(e=m.implementation.createHTMLDocument("")).createElement("base")).href=m.location.href,e.head.appendChild(i)):e=m),o=!n&&[],(r=R.exec(t))?[e.createElement(r[1])]:(r=Ot([t],e,o),o&&o.length&&x(o).remove(),x.merge([],r.childNodes)));var i,r,o},x.fn.load=function(t,e,n){var i,r,o,s=this,a=t.indexOf(" ");return a>-1&&(i=xe(t.slice(a)),t=t.slice(0,a)),p(e)?(n=e,e=void 0):e&&"object"==typeof e&&(r="POST"),s.length>0&&x.ajax({url:t,type:r||"GET",dataType:"html",data:e}).done((function(t){o=arguments,s.html(i?x("<div>").append(x.parseHTML(t)).find(i):t)})).always(n&&function(t,e){s.each((function(){n.apply(this,o||[t.responseText,e,t])}))}),this},x.expr.pseudos.animated=function(t){return x.grep(x.timers,(function(e){return t===e.elem})).length},x.offset={setOffset:function(t,e,n){var i,r,o,s,a,l,c=x.css(t,"position"),u=x(t),h={};"static"===c&&(t.style.position="relative"),a=u.offset(),o=x.css(t,"top"),l=x.css(t,"left"),("absolute"===c||"fixed"===c)&&(o+l).indexOf("auto")>-1?(s=(i=u.position()).top,r=i.left):(s=parseFloat(o)||0,r=parseFloat(l)||0),p(e)&&(e=e.call(t,n,x.extend({},a))),null!=e.top&&(h.top=e.top-a.top+s),null!=e.left&&(h.left=e.left-a.left+r),"using"in e?e.using.call(t,h):u.css(h)}},x.fn.extend({offset:function(t){if(arguments.length)return void 0===t?this:this.each((function(e){x.offset.setOffset(this,t,e)}));var e,n,i=this[0];return i?i.getClientRects().length?(e=i.getBoundingClientRect(),n=i.ownerDocument.defaultView,{top:e.top+n.pageYOffset,left:e.left+n.pageXOffset}):{top:0,left:0}:void 0},position:function(){if(this[0]){var t,e,n,i=this[0],r={top:0,left:0};if("fixed"===x.css(i,"position"))e=i.getBoundingClientRect();else{for(e=this.offset(),n=i.ownerDocument,t=i.offsetParent||n.documentElement;t&&(t===n.body||t===n.documentElement)&&"static"===x.css(t,"position");)t=t.parentNode;t&&t!==i&&1===t.nodeType&&((r=x(t).offset()).top+=x.css(t,"borderTopWidth",!0),r.left+=x.css(t,"borderLeftWidth",!0))}return{top:e.top-r.top-x.css(i,"marginTop",!0),left:e.left-r.left-x.css(i,"marginLeft",!0)}}},offsetParent:function(){return this.map((function(){for(var t=this.offsetParent;t&&"static"===x.css(t,"position");)t=t.offsetParent;return t||ft}))}}),x.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},(function(t,e){var n="pageYOffset"===e;x.fn[t]=function(i){return K(this,(function(t,i,r){var o;if(g(t)?o=t:9===t.nodeType&&(o=t.defaultView),void 0===r)return o?o[e]:t[i];o?o.scrollTo(n?o.pageXOffset:r,n?r:o.pageYOffset):t[i]=r}),t,i,arguments.length)}})),x.each(["top","left"],(function(t,e){x.cssHooks[e]=Kt(d.pixelPosition,(function(t,n){if(n)return n=Gt(t,e),Xt.test(n)?x(t).position()[e]+"px":n}))})),x.each({Height:"height",Width:"width"},(function(t,e){x.each({padding:"inner"+t,content:e,"":"outer"+t},(function(n,i){x.fn[i]=function(r,o){var s=arguments.length&&(n||"boolean"!=typeof r),a=n||(!0===r||!0===o?"margin":"border");return K(this,(function(e,n,r){var o;return g(e)?0===i.indexOf("outer")?e["inner"+t]:e.document.documentElement["client"+t]:9===e.nodeType?(o=e.documentElement,Math.max(e.body["scroll"+t],o["scroll"+t],e.body["offset"+t],o["offset"+t],o["client"+t])):void 0===r?x.css(e,n,a):x.style(e,n,r,a)}),e,s?r:void 0,s)}}))})),x.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],(function(t,e){x.fn[e]=function(t){return this.on(e,t)}})),x.fn.extend({bind:function(t,e,n){return this.on(t,null,e,n)},unbind:function(t,e){return this.off(t,null,e)},delegate:function(t,e,n,i){return this.on(e,t,n,i)},undelegate:function(t,e,n){return 1===arguments.length?this.off(t,"**"):this.off(e,t||"**",n)},hover:function(t,e){return this.on("mouseenter",t).on("mouseleave",e||t)}}),x.each("blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu".split(" "),(function(t,e){x.fn[e]=function(t,n){return arguments.length>0?this.on(e,null,t,n):this.trigger(e)}}));var tn=/^[\s\uFEFF\xA0]+|([^\s\uFEFF\xA0])[\s\uFEFF\xA0]+$/g;x.proxy=function(t,e){var n,i,o;if("string"==typeof e&&(n=t[e],e=t,t=n),p(t))return i=r.call(arguments,2),o=function(){return t.apply(e||this,i.concat(r.call(arguments)))},o.guid=t.guid=t.guid||x.guid++,o},x.holdReady=function(t){t?x.readyWait++:x.ready(!0)},x.isArray=Array.isArray,x.parseJSON=JSON.parse,x.nodeName=E,x.isFunction=p,x.isWindow=g,x.camelCase=et,x.type=_,x.now=Date.now,x.isNumeric=function(t){var e=x.type(t);return("number"===e||"string"===e)&&!isNaN(t-parseFloat(t))},x.trim=function(t){return null==t?"":(t+"").replace(tn,"$1")},"function"==typeof define&&define.amd&&define("jquery",[],(function(){return x}));var en=t.jQuery,nn=t.$;return x.noConflict=function(e){return t.$===x&&(t.$=nn),e&&t.jQuery===x&&(t.jQuery=en),x},void 0===e&&(t.jQuery=t.$=x),x})),
/*!
 * Modernizr v2.8.3
 * www.modernizr.com
 *
 * Copyright (c) Faruk Ates, Paul Irish, Alex Sexton
 * Available under the BSD and MIT licenses: www.modernizr.com/license/
 */
window.Modernizr=function(t,e,n){function i(t){g.cssText=t}function r(t,e){return typeof t===e}function o(t,e){return!!~(""+t).indexOf(e)}function s(t,e){for(var i in t){var r=t[i];if(!o(r,"-")&&g[r]!==n)return"pfx"!=e||r}return!1}function a(t,e,i){for(var o in t){var s=e[t[o]];if(s!==n)return!1===i?t[o]:r(s,"function")?s.bind(i||e):s}return!1}function l(t,e,n){var i=t.charAt(0).toUpperCase()+t.slice(1),o=(t+" "+w.join(i+" ")+i).split(" ");return r(e,"string")||r(e,"undefined")?s(o,e):a(o=(t+" "+x.join(i+" ")+i).split(" "),e,n)}var c,u,h={},f=e.documentElement,d="modernizr",p=e.createElement(d),g=p.style,m=e.createElement("input"),v=":)",y={}.toString,_=" -webkit- -moz- -o- -ms- ".split(" "),b="Webkit Moz O ms",w=b.split(" "),x=b.toLowerCase().split(" "),T="http://www.w3.org/2000/svg",E={},C={},S={},k=[],A=k.slice,O=function(t,n,i,r){var o,s,a,l,c=e.createElement("div"),u=e.body,h=u||e.createElement("body");if(parseInt(i,10))for(;i--;)(a=e.createElement("div")).id=r?r[i]:d+(i+1),c.appendChild(a);return o=["&#173;",'<style id="s',d,'">',t,"</style>"].join(""),c.id=d,(u?c:h).innerHTML+=o,h.appendChild(c),u||(h.style.background="",h.style.overflow="hidden",l=f.style.overflow,f.style.overflow="hidden",f.appendChild(h)),s=n(c,t),u?c.parentNode.removeChild(c):(h.parentNode.removeChild(h),f.style.overflow=l),!!s},D=function(){var t={select:"input",change:"input",submit:"form",reset:"form",error:"img",load:"img",abort:"img"};return function(i,o){o=o||e.createElement(t[i]||"div");var s=(i="on"+i)in o;return s||(o.setAttribute||(o=e.createElement("div")),o.setAttribute&&o.removeAttribute&&(o.setAttribute(i,""),s=r(o[i],"function"),r(o[i],"undefined")||(o[i]=n),o.removeAttribute(i))),o=null,s}}(),M={}.hasOwnProperty;for(var P in u=r(M,"undefined")||r(M.call,"undefined")?function(t,e){return e in t&&r(t.constructor.prototype[e],"undefined")}:function(t,e){return M.call(t,e)},Function.prototype.bind||(Function.prototype.bind=function(t){var e=this;if("function"!=typeof e)throw new TypeError;var n=A.call(arguments,1),i=function(){if(this instanceof i){var r=function(){};r.prototype=e.prototype;var o=new r,s=e.apply(o,n.concat(A.call(arguments)));return Object(s)===s?s:o}return e.apply(t,n.concat(A.call(arguments)))};return i}),E.flexbox=function(){return l("flexWrap")},E.flexboxlegacy=function(){return l("boxDirection")},E.canvas=function(){var t=e.createElement("canvas");return!(!t.getContext||!t.getContext("2d"))},E.canvastext=function(){return!(!h.canvas||!r(e.createElement("canvas").getContext("2d").fillText,"function"))},E.webgl=function(){return!!t.WebGLRenderingContext},E.touch=function(){var n;return"ontouchstart"in t||t.DocumentTouch&&e instanceof DocumentTouch?n=!0:O(["@media (",_.join("touch-enabled),("),d,")","{#modernizr{top:9px;position:absolute}}"].join(""),(function(t){n=9===t.offsetTop})),n},E.geolocation=function(){return"geolocation"in navigator},E.postmessage=function(){return!!t.postMessage},E.websqldatabase=function(){return!!t.openDatabase},E.indexedDB=function(){return!!l("indexedDB",t)},E.hashchange=function(){return D("hashchange",t)&&(e.documentMode===n||e.documentMode>7)},E.history=function(){return!(!t.history||!history.pushState)},E.draganddrop=function(){var t=e.createElement("div");return"draggable"in t||"ondragstart"in t&&"ondrop"in t},E.websockets=function(){return"WebSocket"in t||"MozWebSocket"in t},E.rgba=function(){return i("background-color:rgba(150,255,150,.5)"),o(g.backgroundColor,"rgba")},E.hsla=function(){return i("background-color:hsla(120,40%,100%,.5)"),o(g.backgroundColor,"rgba")||o(g.backgroundColor,"hsla")},E.multiplebgs=function(){return i("background:url(https://),url(https://),red url(https://)"),/(url\s*\(.*?){3}/.test(g.background)},E.backgroundsize=function(){return l("backgroundSize")},E.borderimage=function(){return l("borderImage")},E.borderradius=function(){return l("borderRadius")},E.boxshadow=function(){return l("boxShadow")},E.textshadow=function(){return""===e.createElement("div").style.textShadow},E.opacity=function(){return i(_.join("opacity:.55;")+""),/^0.55$/.test(g.opacity)},E.cssanimations=function(){return l("animationName")},E.csscolumns=function(){return l("columnCount")},E.cssgradients=function(){var t="background-image:";return i((t+"-webkit- ".split(" ").join("gradient(linear,left top,right bottom,from(#9f9),to(white));"+t)+_.join("linear-gradient(left top,#9f9, white);"+t)).slice(0,-17)),o(g.backgroundImage,"gradient")},E.cssreflections=function(){return l("boxReflect")},E.csstransforms=function(){return!!l("transform")},E.csstransforms3d=function(){var t=!!l("perspective");return t&&"webkitPerspective"in f.style&&O("@media (transform-3d),(-webkit-transform-3d){#modernizr{left:9px;position:absolute;height:3px;}}",(function(e){t=9===e.offsetLeft&&3===e.offsetHeight})),t},E.csstransitions=function(){return l("transition")},E.fontface=function(){var t;return O('@font-face {font-family:"font";src:url("https://")}',(function(n,i){var r=e.getElementById("smodernizr"),o=r.sheet||r.styleSheet,s=o?o.cssRules&&o.cssRules[0]?o.cssRules[0].cssText:o.cssText||"":"";t=/src/i.test(s)&&0===s.indexOf(i.split(" ")[0])})),t},E.generatedcontent=function(){var t;return O(["#",d,"{font:0/0 a}#",d,':after{content:"',v,'";visibility:hidden;font:3px/1 a}'].join(""),(function(e){t=e.offsetHeight>=3})),t},E.video=function(){var t=e.createElement("video"),n=!1;try{(n=!!t.canPlayType)&&((n=new Boolean(n)).ogg=t.canPlayType('video/ogg; codecs="theora"').replace(/^no$/,""),n.h264=t.canPlayType('video/mp4; codecs="avc1.42E01E"').replace(/^no$/,""),n.webm=t.canPlayType('video/webm; codecs="vp8, vorbis"').replace(/^no$/,""))}catch(t){}return n},E.audio=function(){var t=e.createElement("audio"),n=!1;try{(n=!!t.canPlayType)&&((n=new Boolean(n)).ogg=t.canPlayType('audio/ogg; codecs="vorbis"').replace(/^no$/,""),n.mp3=t.canPlayType("audio/mpeg;").replace(/^no$/,""),n.wav=t.canPlayType('audio/wav; codecs="1"').replace(/^no$/,""),n.m4a=(t.canPlayType("audio/x-m4a;")||t.canPlayType("audio/aac;")).replace(/^no$/,""))}catch(t){}return n},E.localstorage=function(){try{return localStorage.setItem(d,d),localStorage.removeItem(d),!0}catch(t){return!1}},E.sessionstorage=function(){try{return sessionStorage.setItem(d,d),sessionStorage.removeItem(d),!0}catch(t){return!1}},E.webworkers=function(){return!!t.Worker},E.applicationcache=function(){return!!t.applicationCache},E.svg=function(){return!!e.createElementNS&&!!e.createElementNS(T,"svg").createSVGRect},E.inlinesvg=function(){var t=e.createElement("div");return t.innerHTML="<svg/>",(t.firstChild&&t.firstChild.namespaceURI)==T},E.smil=function(){return!!e.createElementNS&&/SVGAnimate/.test(y.call(e.createElementNS(T,"animate")))},E.svgclippaths=function(){return!!e.createElementNS&&/SVGClipPath/.test(y.call(e.createElementNS(T,"clipPath")))},E)u(E,P)&&(c=P.toLowerCase(),h[c]=E[P](),k.push((h[c]?"":"no-")+c));return h.input||(h.input=function(n){for(var i=0,r=n.length;r>i;i++)S[n[i]]=!!(n[i]in m);return S.list&&(S.list=!(!e.createElement("datalist")||!t.HTMLDataListElement)),S}("autocomplete autofocus list placeholder max min multiple pattern required step".split(" ")),h.inputtypes=function(t){for(var i,r,o,s=0,a=t.length;a>s;s++)m.setAttribute("type",r=t[s]),(i="text"!==m.type)&&(m.value=v,m.style.cssText="position:absolute;visibility:hidden;",/^range$/.test(r)&&m.style.WebkitAppearance!==n?(f.appendChild(m),i=(o=e.defaultView).getComputedStyle&&"textfield"!==o.getComputedStyle(m,null).WebkitAppearance&&0!==m.offsetHeight,f.removeChild(m)):/^(search|tel)$/.test(r)||(i=/^(url|email)$/.test(r)?m.checkValidity&&!1===m.checkValidity():m.value!=v)),C[t[s]]=!!i;return C}("search tel url email datetime date month week time datetime-local number range color".split(" "))),h.addTest=function(t,e){if("object"==typeof t)for(var i in t)u(t,i)&&h.addTest(i,t[i]);else{if(t=t.toLowerCase(),h[t]!==n)return h;e="function"==typeof e?e():e,f.className+=" "+(e?"":"no-")+t,h[t]=e}return h},i(""),p=m=null,function(t,e){function n(){var t=g.elements;return"string"==typeof t?t.split(" "):t}function i(t){var e=p[t[f]];return e||(e={},d++,t[f]=d,p[d]=e),e}function r(t,n,r){return n||(n=e),l?n.createElement(t):(r||(r=i(n)),!(o=r.cache[t]?r.cache[t].cloneNode():h.test(t)?(r.cache[t]=r.createElem(t)).cloneNode():r.createElem(t)).canHaveChildren||u.test(t)||o.tagUrn?o:r.frag.appendChild(o));var o}function o(t,e){e.cache||(e.cache={},e.createElem=t.createElement,e.createFrag=t.createDocumentFragment,e.frag=e.createFrag()),t.createElement=function(n){return g.shivMethods?r(n,t,e):e.createElem(n)},t.createDocumentFragment=Function("h,f","return function(){var n=f.cloneNode(),c=n.createElement;h.shivMethods&&("+n().join().replace(/[\w\-]+/g,(function(t){return e.createElem(t),e.frag.createElement(t),'c("'+t+'")'}))+");return n}")(g,e.frag)}function s(t){t||(t=e);var n=i(t);return!g.shivCSS||a||n.hasCSS||(n.hasCSS=!!function(t,e){var n=t.createElement("p"),i=t.getElementsByTagName("head")[0]||t.documentElement;return n.innerHTML="x<style>article,aside,dialog,figcaption,figure,footer,header,hgroup,main,nav,section{display:block}mark{background:#FF0;color:#000}template{display:none}</style>",i.insertBefore(n.lastChild,i.firstChild)}(t)),l||o(t,n),t}var a,l,c=t.html5||{},u=/^<|^(?:button|map|select|textarea|object|iframe|option|optgroup)$/i,h=/^(?:a|b|code|div|fieldset|h1|h2|h3|h4|h5|h6|i|label|li|ol|p|q|span|strong|style|table|tbody|td|th|tr|ul)$/i,f="_html5shiv",d=0,p={};!function(){try{var t=e.createElement("a");t.innerHTML="<xyz></xyz>",a="hidden"in t,l=1==t.childNodes.length||function(){e.createElement("a");var t=e.createDocumentFragment();return void 0===t.cloneNode||void 0===t.createDocumentFragment||void 0===t.createElement}()}catch(t){a=!0,l=!0}}();var g={elements:c.elements||"abbr article aside audio bdi canvas data datalist details dialog figcaption figure footer header hgroup main mark meter nav output progress section summary template time video",version:"3.7.0",shivCSS:!1!==c.shivCSS,supportsUnknownElements:l,shivMethods:!1!==c.shivMethods,type:"default",shivDocument:s,createElement:r,createDocumentFragment:function(t,r){if(t||(t=e),l)return t.createDocumentFragment();for(var o=(r=r||i(t)).frag.cloneNode(),s=0,a=n(),c=a.length;c>s;s++)o.createElement(a[s]);return o}};t.html5=g,s(e)}(this,e),h._version="2.8.3",h._prefixes=_,h._domPrefixes=x,h._cssomPrefixes=w,h.mq=function(e){var n,i=t.matchMedia||t.msMatchMedia;return i?i(e)&&i(e).matches||!1:(O("@media "+e+" { #"+d+" { position: absolute; } }",(function(e){n="absolute"==(t.getComputedStyle?getComputedStyle(e,null):e.currentStyle).position})),n)},h.hasEvent=D,h.testProp=function(t){return s([t])},h.testAllProps=l,h.testStyles=O,h.prefixed=function(t,e,n){return e?l(t,e,n):l(t,"pfx")},f.className=f.className.replace(/(^|\s)no-js(\s|$)/,"$1$2")+" js "+k.join(" "),h}(this,this.document),function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t=t||self).window=t.window||{})}(this,(function(t){"use strict";function e(t,e){t.prototype=Object.create(e.prototype),t.prototype.constructor=t,t.__proto__=e}function n(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}
/*!
   * GSAP 3.12.5
   * https://gsap.com
   *
   * @license Copyright 2008-2024, GreenSock. All rights reserved.
   * Subject to the terms at https://gsap.com/standard-license or for
   * Club GSAP members, the agreement issued with that membership.
   * @author: Jack Doyle, <EMAIL>
  */var i,r,o,s,a,l,c,u,h,f,d,p,g,m,v,y={autoSleep:120,force3D:"auto",nullTargetWarn:1,units:{lineHeight:""}},_={duration:.5,overwrite:!1,delay:0},b=1e8,w=1e-8,x=2*Math.PI,T=x/4,E=0,C=Math.sqrt,S=Math.cos,k=Math.sin,A=function(t){return"string"==typeof t},O=function(t){return"function"==typeof t},D=function(t){return"number"==typeof t},M=function(t){return void 0===t},P=function(t){return"object"==typeof t},L=function(t){return!1!==t},j=function(){return"undefined"!=typeof window},N=function(t){return O(t)||A(t)},I="function"==typeof ArrayBuffer&&ArrayBuffer.isView||function(){},R=Array.isArray,z=/(?:-?\.?\d|\.)+/gi,F=/[-+=.]*\d+[.e\-+]*\d*[e\-+]*\d*/g,$=/[-+=.]*\d+[.e-]*\d*[a-z%]*/g,H=/[-+=.]*\d+\.?\d*(?:e-|e\+)?\d*/gi,q=/[+-]=-?[.\d]+/,B=/[^,'"\[\]\s]+/gi,W=/^[+\-=e\s\d]*\d+[.\d]*([a-z]*|%)\s*$/i,X={},Y={},U=function(t){return(Y=xt(t,X))&&Tn},V=function(t,e){return console.warn("Invalid property",t,"set to",e,"Missing plugin? gsap.registerPlugin()")},Q=function(t,e){return!e&&console.warn(t)},G=function(t,e){return t&&(X[t]=e)&&Y&&(Y[t]=e)||X},K=function(){return 0},J={suppressEvents:!0,isStart:!0,kill:!1},Z={suppressEvents:!0,kill:!1},tt={suppressEvents:!0},et={},nt=[],it={},rt={},ot={},st=30,at=[],lt="",ct=function(t){var e,n,i=t[0];if(P(i)||O(i)||(t=[t]),!(e=(i._gsap||{}).harness)){for(n=at.length;n--&&!at[n].targetTest(i););e=at[n]}for(n=t.length;n--;)t[n]&&(t[n]._gsap||(t[n]._gsap=new Ie(t[n],e)))||t.splice(n,1);return t},ut=function(t){return t._gsap||ct(Zt(t))[0]._gsap},ht=function(t,e,n){return(n=t[e])&&O(n)?t[e]():M(n)&&t.getAttribute&&t.getAttribute(e)||n},ft=function(t,e){return(t=t.split(",")).forEach(e)||t},dt=function(t){return Math.round(1e5*t)/1e5||0},pt=function(t){return Math.round(1e7*t)/1e7||0},gt=function(t,e){var n=e.charAt(0),i=parseFloat(e.substr(2));return t=parseFloat(t),"+"===n?t+i:"-"===n?t-i:"*"===n?t*i:t/i},mt=function(t,e){for(var n=e.length,i=0;t.indexOf(e[i])<0&&++i<n;);return i<n},vt=function(){var t,e,n=nt.length,i=nt.slice(0);for(it={},nt.length=0,t=0;t<n;t++)(e=i[t])&&e._lazy&&(e.render(e._lazy[0],e._lazy[1],!0)._lazy=0)},yt=function(t,e,n,i){nt.length&&!r&&vt(),t.render(e,n,i||r&&e<0&&(t._initted||t._startAt)),nt.length&&!r&&vt()},_t=function(t){var e=parseFloat(t);return(e||0===e)&&(t+"").match(B).length<2?e:A(t)?t.trim():t},bt=function(t){return t},wt=function(t,e){for(var n in e)n in t||(t[n]=e[n]);return t},xt=function(t,e){for(var n in e)t[n]=e[n];return t},Tt=function t(e,n){for(var i in n)"__proto__"!==i&&"constructor"!==i&&"prototype"!==i&&(e[i]=P(n[i])?t(e[i]||(e[i]={}),n[i]):n[i]);return e},Et=function(t,e){var n,i={};for(n in t)n in e||(i[n]=t[n]);return i},Ct=function(t){var e,n=t.parent||s,i=t.keyframes?(e=R(t.keyframes),function(t,n){for(var i in n)i in t||"duration"===i&&e||"ease"===i||(t[i]=n[i])}):wt;if(L(t.inherit))for(;n;)i(t,n.vars.defaults),n=n.parent||n._dp;return t},St=function(t,e,n,i,r){void 0===n&&(n="_first"),void 0===i&&(i="_last");var o,s=t[i];if(r)for(o=e[r];s&&s[r]>o;)s=s._prev;return s?(e._next=s._next,s._next=e):(e._next=t[n],t[n]=e),e._next?e._next._prev=e:t[i]=e,e._prev=s,e.parent=e._dp=t,e},kt=function(t,e,n,i){void 0===n&&(n="_first"),void 0===i&&(i="_last");var r=e._prev,o=e._next;r?r._next=o:t[n]===e&&(t[n]=o),o?o._prev=r:t[i]===e&&(t[i]=r),e._next=e._prev=e.parent=null},At=function(t,e){t.parent&&(!e||t.parent.autoRemoveChildren)&&t.parent.remove&&t.parent.remove(t),t._act=0},Ot=function(t,e){if(t&&(!e||e._end>t._dur||e._start<0))for(var n=t;n;)n._dirty=1,n=n.parent;return t},Dt=function(t,e,n,i){return t._startAt&&(r?t._startAt.revert(Z):t.vars.immediateRender&&!t.vars.autoRevert||t._startAt.render(e,!0,i))},Mt=function t(e){return!e||e._ts&&t(e.parent)},Pt=function(t){return t._repeat?Lt(t._tTime,t=t.duration()+t._rDelay)*t:0},Lt=function(t,e){var n=Math.floor(t/=e);return t&&n===t?n-1:n},jt=function(t,e){return(t-e._start)*e._ts+(e._ts>=0?0:e._dirty?e.totalDuration():e._tDur)},Nt=function(t){return t._end=pt(t._start+(t._tDur/Math.abs(t._ts||t._rts||w)||0))},It=function(t,e){var n=t._dp;return n&&n.smoothChildTiming&&t._ts&&(t._start=pt(n._time-(t._ts>0?e/t._ts:((t._dirty?t.totalDuration():t._tDur)-e)/-t._ts)),Nt(t),n._dirty||Ot(n,t)),t},Rt=function(t,e){var n;if((e._time||!e._dur&&e._initted||e._start<t._time&&(e._dur||!e.add))&&(n=jt(t.rawTime(),e),(!e._dur||Qt(0,e.totalDuration(),n)-e._tTime>w)&&e.render(n,!0)),Ot(t,e)._dp&&t._initted&&t._time>=t._dur&&t._ts){if(t._dur<t.duration())for(n=t;n._dp;)n.rawTime()>=0&&n.totalTime(n._tTime),n=n._dp;t._zTime=-1e-8}},zt=function(t,e,n,i){return e.parent&&At(e),e._start=pt((D(n)?n:n||t!==s?Yt(t,n,e):t._time)+e._delay),e._end=pt(e._start+(e.totalDuration()/Math.abs(e.timeScale())||0)),St(t,e,"_first","_last",t._sort?"_start":0),qt(e)||(t._recent=e),i||Rt(t,e),t._ts<0&&It(t,t._tTime),t},Ft=function(t,e){return(X.ScrollTrigger||V("scrollTrigger",e))&&X.ScrollTrigger.create(e,t)},$t=function(t,e,n,i,o){return We(t,e,o),t._initted?!n&&t._pt&&!r&&(t._dur&&!1!==t.vars.lazy||!t._dur&&t.vars.lazy)&&h!==Te.frame?(nt.push(t),t._lazy=[o,i],1):void 0:1},Ht=function t(e){var n=e.parent;return n&&n._ts&&n._initted&&!n._lock&&(n.rawTime()<0||t(n))},qt=function(t){var e=t.data;return"isFromStart"===e||"isStart"===e},Bt=function(t,e,n,i){var r=t._repeat,o=pt(e)||0,s=t._tTime/t._tDur;return s&&!i&&(t._time*=o/t._dur),t._dur=o,t._tDur=r?r<0?1e10:pt(o*(r+1)+t._rDelay*r):o,s>0&&!i&&It(t,t._tTime=t._tDur*s),t.parent&&Nt(t),n||Ot(t.parent,t),t},Wt=function(t){return t instanceof ze?Ot(t):Bt(t,t._dur)},Xt={_start:0,endTime:K,totalDuration:K},Yt=function t(e,n,i){var r,o,s,a=e.labels,l=e._recent||Xt,c=e.duration()>=b?l.endTime(!1):e._dur;return A(n)&&(isNaN(n)||n in a)?(o=n.charAt(0),s="%"===n.substr(-1),r=n.indexOf("="),"<"===o||">"===o?(r>=0&&(n=n.replace(/=/,"")),("<"===o?l._start:l.endTime(l._repeat>=0))+(parseFloat(n.substr(1))||0)*(s?(r<0?l:i).totalDuration()/100:1)):r<0?(n in a||(a[n]=c),a[n]):(o=parseFloat(n.charAt(r-1)+n.substr(r+1)),s&&i&&(o=o/100*(R(i)?i[0]:i).totalDuration()),r>1?t(e,n.substr(0,r-1),i)+o:c+o)):null==n?c:+n},Ut=function(t,e,n){var i,r,o=D(e[1]),s=(o?2:1)+(t<2?0:1),a=e[s];if(o&&(a.duration=e[1]),a.parent=n,t){for(i=a,r=n;r&&!("immediateRender"in i);)i=r.vars.defaults||{},r=L(r.vars.inherit)&&r.parent;a.immediateRender=L(i.immediateRender),t<2?a.runBackwards=1:a.startAt=e[s-1]}return new Qe(e[0],a,e[s+1])},Vt=function(t,e){return t||0===t?e(t):e},Qt=function(t,e,n){return n<t?t:n>e?e:n},Gt=function(t,e){return A(t)&&(e=W.exec(t))?e[1]:""},Kt=[].slice,Jt=function(t,e){return t&&P(t)&&"length"in t&&(!e&&!t.length||t.length-1 in t&&P(t[0]))&&!t.nodeType&&t!==a},Zt=function(t,e,n){return o&&!e&&o.selector?o.selector(t):!A(t)||n||!l&&Ee()?R(t)?function(t,e,n){return void 0===n&&(n=[]),t.forEach((function(t){var i;return A(t)&&!e||Jt(t,1)?(i=n).push.apply(i,Zt(t)):n.push(t)}))||n}(t,n):Jt(t)?Kt.call(t,0):t?[t]:[]:Kt.call((e||c).querySelectorAll(t),0)},te=function(t){return t=Zt(t)[0]||Q("Invalid scope")||{},function(e){var n=t.current||t.nativeElement||t;return Zt(e,n.querySelectorAll?n:n===t?Q("Invalid scope")||c.createElement("div"):t)}},ee=function(t){return t.sort((function(){return.5-Math.random()}))},ne=function(t){if(O(t))return t;var e=P(t)?t:{each:t},n=Me(e.ease),i=e.from||0,r=parseFloat(e.base)||0,o={},s=i>0&&i<1,a=isNaN(i)||s,l=e.axis,c=i,u=i;return A(i)?c=u={center:.5,edges:.5,end:1}[i]||0:!s&&a&&(c=i[0],u=i[1]),function(t,s,h){var f,d,p,g,m,v,y,_,w,x=(h||e).length,T=o[x];if(!T){if(!(w="auto"===e.grid?0:(e.grid||[1,b])[1])){for(y=-b;y<(y=h[w++].getBoundingClientRect().left)&&w<x;);w<x&&w--}for(T=o[x]=[],f=a?Math.min(w,x)*c-.5:i%w,d=w===b?0:a?x*u/w-.5:i/w|0,y=0,_=b,v=0;v<x;v++)p=v%w-f,g=d-(v/w|0),T[v]=m=l?Math.abs("y"===l?g:p):C(p*p+g*g),m>y&&(y=m),m<_&&(_=m);"random"===i&&ee(T),T.max=y-_,T.min=_,T.v=x=(parseFloat(e.amount)||parseFloat(e.each)*(w>x?x-1:l?"y"===l?x/w:w:Math.max(w,x/w))||0)*("edges"===i?-1:1),T.b=x<0?r-x:r,T.u=Gt(e.amount||e.each)||0,n=n&&x<0?Oe(n):n}return x=(T[t]-T.min)/T.max||0,pt(T.b+(n?n(x):x)*T.v)+T.u}},ie=function(t){var e=Math.pow(10,((t+"").split(".")[1]||"").length);return function(n){var i=pt(Math.round(parseFloat(n)/t)*t*e);return(i-i%1)/e+(D(n)?0:Gt(n))}},re=function(t,e){var n,i,r=R(t);return!r&&P(t)&&(n=r=t.radius||b,t.values?(t=Zt(t.values),(i=!D(t[0]))&&(n*=n)):t=ie(t.increment)),Vt(e,r?O(t)?function(e){return i=t(e),Math.abs(i-e)<=n?i:e}:function(e){for(var r,o,s=parseFloat(i?e.x:e),a=parseFloat(i?e.y:0),l=b,c=0,u=t.length;u--;)(r=i?(r=t[u].x-s)*r+(o=t[u].y-a)*o:Math.abs(t[u]-s))<l&&(l=r,c=u);return c=!n||l<=n?t[c]:e,i||c===e||D(e)?c:c+Gt(e)}:ie(t))},oe=function(t,e,n,i){return Vt(R(t)?!e:!0===n?!!(n=0):!i,(function(){return R(t)?t[~~(Math.random()*t.length)]:(n=n||1e-5)&&(i=n<1?Math.pow(10,(n+"").length-2):1)&&Math.floor(Math.round((t-n/2+Math.random()*(e-t+.99*n))/n)*n*i)/i}))},se=function(t,e,n){return Vt(n,(function(n){return t[~~e(n)]}))},ae=function(t){for(var e,n,i,r,o=0,s="";~(e=t.indexOf("random(",o));)i=t.indexOf(")",e),r="["===t.charAt(e+7),n=t.substr(e+7,i-e-7).match(r?B:z),s+=t.substr(o,e-o)+oe(r?n:+n[0],r?0:+n[1],+n[2]||1e-5),o=i+1;return s+t.substr(o,t.length-o)},le=function(t,e,n,i,r){var o=e-t,s=i-n;return Vt(r,(function(e){return n+((e-t)/o*s||0)}))},ce=function(t,e,n){var i,r,o,s=t.labels,a=b;for(i in s)(r=s[i]-e)<0==!!n&&r&&a>(r=Math.abs(r))&&(o=i,a=r);return o},ue=function(t,e,n){var i,r,s,a=t.vars,l=a[e],c=o,u=t._ctx;if(l)return i=a[e+"Params"],r=a.callbackScope||t,n&&nt.length&&vt(),u&&(o=u),s=i?l.apply(r,i):l.call(r),o=c,s},he=function(t){return At(t),t.scrollTrigger&&t.scrollTrigger.kill(!!r),t.progress()<1&&ue(t,"onInterrupt"),t},fe=[],de=function(t){if(t)if(t=!t.name&&t.default||t,j()||t.headless){var e=t.name,n=O(t),i=e&&!n&&t.init?function(){this._props=[]}:t,r={init:K,render:on,add:qe,kill:an,modifier:sn,rawVars:0},o={targetTest:0,get:0,getSetter:tn,aliases:{},register:0};if(Ee(),t!==i){if(rt[e])return;wt(i,wt(Et(t,r),o)),xt(i.prototype,xt(r,Et(t,o))),rt[i.prop=e]=i,t.targetTest&&(at.push(i),et[e]=1),e=("css"===e?"CSS":e.charAt(0).toUpperCase()+e.substr(1))+"Plugin"}G(e,i),t.register&&t.register(Tn,i,un)}else fe.push(t)},pe=255,ge={aqua:[0,pe,pe],lime:[0,pe,0],silver:[192,192,192],black:[0,0,0],maroon:[128,0,0],teal:[0,128,128],blue:[0,0,pe],navy:[0,0,128],white:[pe,pe,pe],olive:[128,128,0],yellow:[pe,pe,0],orange:[pe,165,0],gray:[128,128,128],purple:[128,0,128],green:[0,128,0],red:[pe,0,0],pink:[pe,192,203],cyan:[0,pe,pe],transparent:[pe,pe,pe,0]},me=function(t,e,n){return(6*(t+=t<0?1:t>1?-1:0)<1?e+(n-e)*t*6:t<.5?n:3*t<2?e+(n-e)*(2/3-t)*6:e)*pe+.5|0},ve=function(t,e,n){var i,r,o,s,a,l,c,u,h,f,d=t?D(t)?[t>>16,t>>8&pe,t&pe]:0:ge.black;if(!d){if(","===t.substr(-1)&&(t=t.substr(0,t.length-1)),ge[t])d=ge[t];else if("#"===t.charAt(0)){if(t.length<6&&(i=t.charAt(1),r=t.charAt(2),o=t.charAt(3),t="#"+i+i+r+r+o+o+(5===t.length?t.charAt(4)+t.charAt(4):"")),9===t.length)return[(d=parseInt(t.substr(1,6),16))>>16,d>>8&pe,d&pe,parseInt(t.substr(7),16)/255];d=[(t=parseInt(t.substr(1),16))>>16,t>>8&pe,t&pe]}else if("hsl"===t.substr(0,3))if(d=f=t.match(z),e){if(~t.indexOf("="))return d=t.match(F),n&&d.length<4&&(d[3]=1),d}else s=+d[0]%360/360,a=+d[1]/100,i=2*(l=+d[2]/100)-(r=l<=.5?l*(a+1):l+a-l*a),d.length>3&&(d[3]*=1),d[0]=me(s+1/3,i,r),d[1]=me(s,i,r),d[2]=me(s-1/3,i,r);else d=t.match(z)||ge.transparent;d=d.map(Number)}return e&&!f&&(i=d[0]/pe,r=d[1]/pe,o=d[2]/pe,l=((c=Math.max(i,r,o))+(u=Math.min(i,r,o)))/2,c===u?s=a=0:(h=c-u,a=l>.5?h/(2-c-u):h/(c+u),s=c===i?(r-o)/h+(r<o?6:0):c===r?(o-i)/h+2:(i-r)/h+4,s*=60),d[0]=~~(s+.5),d[1]=~~(100*a+.5),d[2]=~~(100*l+.5)),n&&d.length<4&&(d[3]=1),d},ye=function(t){var e=[],n=[],i=-1;return t.split(be).forEach((function(t){var r=t.match($)||[];e.push.apply(e,r),n.push(i+=r.length+1)})),e.c=n,e},_e=function(t,e,n){var i,r,o,s,a="",l=(t+a).match(be),c=e?"hsla(":"rgba(",u=0;if(!l)return t;if(l=l.map((function(t){return(t=ve(t,e,1))&&c+(e?t[0]+","+t[1]+"%,"+t[2]+"%,"+t[3]:t.join(","))+")"})),n&&(o=ye(t),(i=n.c).join(a)!==o.c.join(a)))for(s=(r=t.replace(be,"1").split($)).length-1;u<s;u++)a+=r[u]+(~i.indexOf(u)?l.shift()||c+"0,0,0,0)":(o.length?o:l.length?l:n).shift());if(!r)for(s=(r=t.split(be)).length-1;u<s;u++)a+=r[u]+l[u];return a+r[s]},be=function(){var t,e="(?:\\b(?:(?:rgb|rgba|hsl|hsla)\\(.+?\\))|\\B#(?:[0-9a-f]{3,4}){1,2}\\b";for(t in ge)e+="|"+t+"\\b";return new RegExp(e+")","gi")}(),we=/hsl[a]?\(/,xe=function(t){var e,n=t.join(" ");if(be.lastIndex=0,be.test(n))return e=we.test(n),t[1]=_e(t[1],e),t[0]=_e(t[0],e,ye(t[1])),!0},Te=function(){var t,e,n,i,r,o,s=Date.now,h=500,f=33,p=s(),g=p,m=1e3/240,v=m,y=[],_=function n(a){var l,c,u,d,_=s()-g,b=!0===a;if((_>h||_<0)&&(p+=_-f),((l=(u=(g+=_)-p)-v)>0||b)&&(d=++i.frame,r=u-1e3*i.time,i.time=u/=1e3,v+=l+(l>=m?4:m-l),c=1),b||(t=e(n)),c)for(o=0;o<y.length;o++)y[o](u,r,d,a)};return i={time:0,frame:0,tick:function(){_(!0)},deltaRatio:function(t){return r/(1e3/(t||60))},wake:function(){u&&(!l&&j()&&(a=l=window,c=a.document||{},X.gsap=Tn,(a.gsapVersions||(a.gsapVersions=[])).push(Tn.version),U(Y||a.GreenSockGlobals||!a.gsap&&a||{}),fe.forEach(de)),n="undefined"!=typeof requestAnimationFrame&&requestAnimationFrame,t&&i.sleep(),e=n||function(t){return setTimeout(t,v-1e3*i.time+1|0)},d=1,_(2))},sleep:function(){(n?cancelAnimationFrame:clearTimeout)(t),d=0,e=K},lagSmoothing:function(t,e){h=t||1/0,f=Math.min(e||33,h)},fps:function(t){m=1e3/(t||240),v=1e3*i.time+m},add:function(t,e,n){var r=e?function(e,n,o,s){t(e,n,o,s),i.remove(r)}:t;return i.remove(t),y[n?"unshift":"push"](r),Ee(),r},remove:function(t,e){~(e=y.indexOf(t))&&y.splice(e,1)&&o>=e&&o--},_listeners:y}}(),Ee=function(){return!d&&Te.wake()},Ce={},Se=/^[\d.\-M][\d.\-,\s]/,ke=/["']/g,Ae=function(t){for(var e,n,i,r={},o=t.substr(1,t.length-3).split(":"),s=o[0],a=1,l=o.length;a<l;a++)n=o[a],e=a!==l-1?n.lastIndexOf(","):n.length,i=n.substr(0,e),r[s]=isNaN(i)?i.replace(ke,"").trim():+i,s=n.substr(e+1).trim();return r},Oe=function(t){return function(e){return 1-t(1-e)}},De=function t(e,n){for(var i,r=e._first;r;)r instanceof ze?t(r,n):!r.vars.yoyoEase||r._yoyo&&r._repeat||r._yoyo===n||(r.timeline?t(r.timeline,n):(i=r._ease,r._ease=r._yEase,r._yEase=i,r._yoyo=n)),r=r._next},Me=function(t,e){return t&&(O(t)?t:Ce[t]||function(t){var e,n,i,r,o=(t+"").split("("),s=Ce[o[0]];return s&&o.length>1&&s.config?s.config.apply(null,~t.indexOf("{")?[Ae(o[1])]:(e=t,n=e.indexOf("(")+1,i=e.indexOf(")"),r=e.indexOf("(",n),e.substring(n,~r&&r<i?e.indexOf(")",i+1):i)).split(",").map(_t)):Ce._CE&&Se.test(t)?Ce._CE("",t):s}(t))||e},Pe=function(t,e,n,i){void 0===n&&(n=function(t){return 1-e(1-t)}),void 0===i&&(i=function(t){return t<.5?e(2*t)/2:1-e(2*(1-t))/2});var r,o={easeIn:e,easeOut:n,easeInOut:i};return ft(t,(function(t){for(var e in Ce[t]=X[t]=o,Ce[r=t.toLowerCase()]=n,o)Ce[r+("easeIn"===e?".in":"easeOut"===e?".out":".inOut")]=Ce[t+"."+e]=o[e]})),o},Le=function(t){return function(e){return e<.5?(1-t(1-2*e))/2:.5+t(2*(e-.5))/2}},je=function t(e,n,i){var r=n>=1?n:1,o=(i||(e?.3:.45))/(n<1?n:1),s=o/x*(Math.asin(1/r)||0),a=function(t){return 1===t?1:r*Math.pow(2,-10*t)*k((t-s)*o)+1},l="out"===e?a:"in"===e?function(t){return 1-a(1-t)}:Le(a);return o=x/o,l.config=function(n,i){return t(e,n,i)},l},Ne=function t(e,n){void 0===n&&(n=1.70158);var i=function(t){return t?--t*t*((n+1)*t+n)+1:0},r="out"===e?i:"in"===e?function(t){return 1-i(1-t)}:Le(i);return r.config=function(n){return t(e,n)},r};ft("Linear,Quad,Cubic,Quart,Quint,Strong",(function(t,e){var n=e<5?e+1:e;Pe(t+",Power"+(n-1),e?function(t){return Math.pow(t,n)}:function(t){return t},(function(t){return 1-Math.pow(1-t,n)}),(function(t){return t<.5?Math.pow(2*t,n)/2:1-Math.pow(2*(1-t),n)/2}))})),Ce.Linear.easeNone=Ce.none=Ce.Linear.easeIn,Pe("Elastic",je("in"),je("out"),je()),p=7.5625,m=1/(g=2.75),Pe("Bounce",(function(t){return 1-v(1-t)}),v=function(t){return t<m?p*t*t:t<.7272727272727273?p*Math.pow(t-1.5/g,2)+.75:t<.9090909090909092?p*(t-=2.25/g)*t+.9375:p*Math.pow(t-2.625/g,2)+.984375}),Pe("Expo",(function(t){return t?Math.pow(2,10*(t-1)):0})),Pe("Circ",(function(t){return-(C(1-t*t)-1)})),Pe("Sine",(function(t){return 1===t?1:1-S(t*T)})),Pe("Back",Ne("in"),Ne("out"),Ne()),Ce.SteppedEase=Ce.steps=X.SteppedEase={config:function(t,e){void 0===t&&(t=1);var n=1/t,i=t+(e?0:1),r=e?1:0;return function(t){return((i*Qt(0,.99999999,t)|0)+r)*n}}},_.ease=Ce["quad.out"],ft("onComplete,onUpdate,onStart,onRepeat,onReverseComplete,onInterrupt",(function(t){return lt+=t+","+t+"Params,"}));var Ie=function(t,e){this.id=E++,t._gsap=this,this.target=t,this.harness=e,this.get=e?e.get:ht,this.set=e?e.getSetter:tn},Re=function(){function t(t){this.vars=t,this._delay=+t.delay||0,(this._repeat=t.repeat===1/0?-2:t.repeat||0)&&(this._rDelay=t.repeatDelay||0,this._yoyo=!!t.yoyo||!!t.yoyoEase),this._ts=1,Bt(this,+t.duration,1,1),this.data=t.data,o&&(this._ctx=o,o.data.push(this)),d||Te.wake()}var e=t.prototype;return e.delay=function(t){return t||0===t?(this.parent&&this.parent.smoothChildTiming&&this.startTime(this._start+t-this._delay),this._delay=t,this):this._delay},e.duration=function(t){return arguments.length?this.totalDuration(this._repeat>0?t+(t+this._rDelay)*this._repeat:t):this.totalDuration()&&this._dur},e.totalDuration=function(t){return arguments.length?(this._dirty=0,Bt(this,this._repeat<0?t:(t-this._repeat*this._rDelay)/(this._repeat+1))):this._tDur},e.totalTime=function(t,e){if(Ee(),!arguments.length)return this._tTime;var n=this._dp;if(n&&n.smoothChildTiming&&this._ts){for(It(this,t),!n._dp||n.parent||Rt(n,this);n&&n.parent;)n.parent._time!==n._start+(n._ts>=0?n._tTime/n._ts:(n.totalDuration()-n._tTime)/-n._ts)&&n.totalTime(n._tTime,!0),n=n.parent;!this.parent&&this._dp.autoRemoveChildren&&(this._ts>0&&t<this._tDur||this._ts<0&&t>0||!this._tDur&&!t)&&zt(this._dp,this,this._start-this._delay)}return(this._tTime!==t||!this._dur&&!e||this._initted&&Math.abs(this._zTime)===w||!t&&!this._initted&&(this.add||this._ptLookup))&&(this._ts||(this._pTime=t),yt(this,t,e)),this},e.time=function(t,e){return arguments.length?this.totalTime(Math.min(this.totalDuration(),t+Pt(this))%(this._dur+this._rDelay)||(t?this._dur:0),e):this._time},e.totalProgress=function(t,e){return arguments.length?this.totalTime(this.totalDuration()*t,e):this.totalDuration()?Math.min(1,this._tTime/this._tDur):this.rawTime()>0?1:0},e.progress=function(t,e){return arguments.length?this.totalTime(this.duration()*(!this._yoyo||1&this.iteration()?t:1-t)+Pt(this),e):this.duration()?Math.min(1,this._time/this._dur):this.rawTime()>0?1:0},e.iteration=function(t,e){var n=this.duration()+this._rDelay;return arguments.length?this.totalTime(this._time+(t-1)*n,e):this._repeat?Lt(this._tTime,n)+1:1},e.timeScale=function(t,e){if(!arguments.length)return-1e-8===this._rts?0:this._rts;if(this._rts===t)return this;var n=this.parent&&this._ts?jt(this.parent._time,this):this._tTime;return this._rts=+t||0,this._ts=this._ps||-1e-8===t?0:this._rts,this.totalTime(Qt(-Math.abs(this._delay),this._tDur,n),!1!==e),Nt(this),function(t){for(var e=t.parent;e&&e.parent;)e._dirty=1,e.totalDuration(),e=e.parent;return t}(this)},e.paused=function(t){return arguments.length?(this._ps!==t&&(this._ps=t,t?(this._pTime=this._tTime||Math.max(-this._delay,this.rawTime()),this._ts=this._act=0):(Ee(),this._ts=this._rts,this.totalTime(this.parent&&!this.parent.smoothChildTiming?this.rawTime():this._tTime||this._pTime,1===this.progress()&&Math.abs(this._zTime)!==w&&(this._tTime-=w)))),this):this._ps},e.startTime=function(t){if(arguments.length){this._start=t;var e=this.parent||this._dp;return e&&(e._sort||!this.parent)&&zt(e,this,t-this._delay),this}return this._start},e.endTime=function(t){return this._start+(L(t)?this.totalDuration():this.duration())/Math.abs(this._ts||1)},e.rawTime=function(t){var e=this.parent||this._dp;return e?t&&(!this._ts||this._repeat&&this._time&&this.totalProgress()<1)?this._tTime%(this._dur+this._rDelay):this._ts?jt(e.rawTime(t),this):this._tTime:this._tTime},e.revert=function(t){void 0===t&&(t=tt);var e=r;return r=t,(this._initted||this._startAt)&&(this.timeline&&this.timeline.revert(t),this.totalTime(-.01,t.suppressEvents)),"nested"!==this.data&&!1!==t.kill&&this.kill(),r=e,this},e.globalTime=function(t){for(var e=this,n=arguments.length?t:e.rawTime();e;)n=e._start+n/(Math.abs(e._ts)||1),e=e._dp;return!this.parent&&this._sat?this._sat.globalTime(t):n},e.repeat=function(t){return arguments.length?(this._repeat=t===1/0?-2:t,Wt(this)):-2===this._repeat?1/0:this._repeat},e.repeatDelay=function(t){if(arguments.length){var e=this._time;return this._rDelay=t,Wt(this),e?this.time(e):this}return this._rDelay},e.yoyo=function(t){return arguments.length?(this._yoyo=t,this):this._yoyo},e.seek=function(t,e){return this.totalTime(Yt(this,t),L(e))},e.restart=function(t,e){return this.play().totalTime(t?-this._delay:0,L(e))},e.play=function(t,e){return null!=t&&this.seek(t,e),this.reversed(!1).paused(!1)},e.reverse=function(t,e){return null!=t&&this.seek(t||this.totalDuration(),e),this.reversed(!0).paused(!1)},e.pause=function(t,e){return null!=t&&this.seek(t,e),this.paused(!0)},e.resume=function(){return this.paused(!1)},e.reversed=function(t){return arguments.length?(!!t!==this.reversed()&&this.timeScale(-this._rts||(t?-1e-8:0)),this):this._rts<0},e.invalidate=function(){return this._initted=this._act=0,this._zTime=-1e-8,this},e.isActive=function(){var t,e=this.parent||this._dp,n=this._start;return!(e&&!(this._ts&&this._initted&&e.isActive()&&(t=e.rawTime(!0))>=n&&t<this.endTime(!0)-w))},e.eventCallback=function(t,e,n){var i=this.vars;return arguments.length>1?(e?(i[t]=e,n&&(i[t+"Params"]=n),"onUpdate"===t&&(this._onUpdate=e)):delete i[t],this):i[t]},e.then=function(t){var e=this;return new Promise((function(n){var i=O(t)?t:bt,r=function(){var t=e.then;e.then=null,O(i)&&(i=i(e))&&(i.then||i===e)&&(e.then=t),n(i),e.then=t};e._initted&&1===e.totalProgress()&&e._ts>=0||!e._tTime&&e._ts<0?r():e._prom=r}))},e.kill=function(){he(this)},t}();wt(Re.prototype,{_time:0,_start:0,_end:0,_tTime:0,_tDur:0,_dirty:0,_repeat:0,_yoyo:!1,parent:null,_initted:!1,_rDelay:0,_ts:1,_dp:0,ratio:0,_zTime:-1e-8,_prom:0,_ps:!1,_rts:1});var ze=function(t){function i(e,i){var r;return void 0===e&&(e={}),(r=t.call(this,e)||this).labels={},r.smoothChildTiming=!!e.smoothChildTiming,r.autoRemoveChildren=!!e.autoRemoveChildren,r._sort=L(e.sortChildren),s&&zt(e.parent||s,n(r),i),e.reversed&&r.reverse(),e.paused&&r.paused(!0),e.scrollTrigger&&Ft(n(r),e.scrollTrigger),r}e(i,t);var o=i.prototype;return o.to=function(t,e,n){return Ut(0,arguments,this),this},o.from=function(t,e,n){return Ut(1,arguments,this),this},o.fromTo=function(t,e,n,i){return Ut(2,arguments,this),this},o.set=function(t,e,n){return e.duration=0,e.parent=this,Ct(e).repeatDelay||(e.repeat=0),e.immediateRender=!!e.immediateRender,new Qe(t,e,Yt(this,n),1),this},o.call=function(t,e,n){return zt(this,Qe.delayedCall(0,t,e),n)},o.staggerTo=function(t,e,n,i,r,o,s){return n.duration=e,n.stagger=n.stagger||i,n.onComplete=o,n.onCompleteParams=s,n.parent=this,new Qe(t,n,Yt(this,r)),this},o.staggerFrom=function(t,e,n,i,r,o,s){return n.runBackwards=1,Ct(n).immediateRender=L(n.immediateRender),this.staggerTo(t,e,n,i,r,o,s)},o.staggerFromTo=function(t,e,n,i,r,o,s,a){return i.startAt=n,Ct(i).immediateRender=L(i.immediateRender),this.staggerTo(t,e,i,r,o,s,a)},o.render=function(t,e,n){var i,o,a,l,c,u,h,f,d,p,g,m,v=this._time,y=this._dirty?this.totalDuration():this._tDur,_=this._dur,b=t<=0?0:pt(t),x=this._zTime<0!=t<0&&(this._initted||!_);if(this!==s&&b>y&&t>=0&&(b=y),b!==this._tTime||n||x){if(v!==this._time&&_&&(b+=this._time-v,t+=this._time-v),i=b,d=this._start,u=!(f=this._ts),x&&(_||(v=this._zTime),(t||!e)&&(this._zTime=t)),this._repeat){if(g=this._yoyo,c=_+this._rDelay,this._repeat<-1&&t<0)return this.totalTime(100*c+t,e,n);if(i=pt(b%c),b===y?(l=this._repeat,i=_):((l=~~(b/c))&&l===b/c&&(i=_,l--),i>_&&(i=_)),p=Lt(this._tTime,c),!v&&this._tTime&&p!==l&&this._tTime-p*c-this._dur<=0&&(p=l),g&&1&l&&(i=_-i,m=1),l!==p&&!this._lock){var T=g&&1&p,E=T===(g&&1&l);if(l<p&&(T=!T),v=T?0:b%_?_:b,this._lock=1,this.render(v||(m?0:pt(l*c)),e,!_)._lock=0,this._tTime=b,!e&&this.parent&&ue(this,"onRepeat"),this.vars.repeatRefresh&&!m&&(this.invalidate()._lock=1),v&&v!==this._time||u!==!this._ts||this.vars.onRepeat&&!this.parent&&!this._act)return this;if(_=this._dur,y=this._tDur,E&&(this._lock=2,v=T?_:-1e-4,this.render(v,!0),this.vars.repeatRefresh&&!m&&this.invalidate()),this._lock=0,!this._ts&&!u)return this;De(this,m)}}if(this._hasPause&&!this._forcing&&this._lock<2&&(h=function(t,e,n){var i;if(n>e)for(i=t._first;i&&i._start<=n;){if("isPause"===i.data&&i._start>e)return i;i=i._next}else for(i=t._last;i&&i._start>=n;){if("isPause"===i.data&&i._start<e)return i;i=i._prev}}(this,pt(v),pt(i)),h&&(b-=i-(i=h._start))),this._tTime=b,this._time=i,this._act=!f,this._initted||(this._onUpdate=this.vars.onUpdate,this._initted=1,this._zTime=t,v=0),!v&&i&&!e&&!l&&(ue(this,"onStart"),this._tTime!==b))return this;if(i>=v&&t>=0)for(o=this._first;o;){if(a=o._next,(o._act||i>=o._start)&&o._ts&&h!==o){if(o.parent!==this)return this.render(t,e,n);if(o.render(o._ts>0?(i-o._start)*o._ts:(o._dirty?o.totalDuration():o._tDur)+(i-o._start)*o._ts,e,n),i!==this._time||!this._ts&&!u){h=0,a&&(b+=this._zTime=-1e-8);break}}o=a}else{o=this._last;for(var C=t<0?t:i;o;){if(a=o._prev,(o._act||C<=o._end)&&o._ts&&h!==o){if(o.parent!==this)return this.render(t,e,n);if(o.render(o._ts>0?(C-o._start)*o._ts:(o._dirty?o.totalDuration():o._tDur)+(C-o._start)*o._ts,e,n||r&&(o._initted||o._startAt)),i!==this._time||!this._ts&&!u){h=0,a&&(b+=this._zTime=C?-1e-8:w);break}}o=a}}if(h&&!e&&(this.pause(),h.render(i>=v?0:-1e-8)._zTime=i>=v?1:-1,this._ts))return this._start=d,Nt(this),this.render(t,e,n);this._onUpdate&&!e&&ue(this,"onUpdate",!0),(b===y&&this._tTime>=this.totalDuration()||!b&&v)&&(d!==this._start&&Math.abs(f)===Math.abs(this._ts)||this._lock||((t||!_)&&(b===y&&this._ts>0||!b&&this._ts<0)&&At(this,1),e||t<0&&!v||!b&&!v&&y||(ue(this,b===y&&t>=0?"onComplete":"onReverseComplete",!0),this._prom&&!(b<y&&this.timeScale()>0)&&this._prom())))}return this},o.add=function(t,e){var n=this;if(D(e)||(e=Yt(this,e,t)),!(t instanceof Re)){if(R(t))return t.forEach((function(t){return n.add(t,e)})),this;if(A(t))return this.addLabel(t,e);if(!O(t))return this;t=Qe.delayedCall(0,t)}return this!==t?zt(this,t,e):this},o.getChildren=function(t,e,n,i){void 0===t&&(t=!0),void 0===e&&(e=!0),void 0===n&&(n=!0),void 0===i&&(i=-b);for(var r=[],o=this._first;o;)o._start>=i&&(o instanceof Qe?e&&r.push(o):(n&&r.push(o),t&&r.push.apply(r,o.getChildren(!0,e,n)))),o=o._next;return r},o.getById=function(t){for(var e=this.getChildren(1,1,1),n=e.length;n--;)if(e[n].vars.id===t)return e[n]},o.remove=function(t){return A(t)?this.removeLabel(t):O(t)?this.killTweensOf(t):(kt(this,t),t===this._recent&&(this._recent=this._last),Ot(this))},o.totalTime=function(e,n){return arguments.length?(this._forcing=1,!this._dp&&this._ts&&(this._start=pt(Te.time-(this._ts>0?e/this._ts:(this.totalDuration()-e)/-this._ts))),t.prototype.totalTime.call(this,e,n),this._forcing=0,this):this._tTime},o.addLabel=function(t,e){return this.labels[t]=Yt(this,e),this},o.removeLabel=function(t){return delete this.labels[t],this},o.addPause=function(t,e,n){var i=Qe.delayedCall(0,e||K,n);return i.data="isPause",this._hasPause=1,zt(this,i,Yt(this,t))},o.removePause=function(t){var e=this._first;for(t=Yt(this,t);e;)e._start===t&&"isPause"===e.data&&At(e),e=e._next},o.killTweensOf=function(t,e,n){for(var i=this.getTweensOf(t,n),r=i.length;r--;)Fe!==i[r]&&i[r].kill(t,e);return this},o.getTweensOf=function(t,e){for(var n,i=[],r=Zt(t),o=this._first,s=D(e);o;)o instanceof Qe?mt(o._targets,r)&&(s?(!Fe||o._initted&&o._ts)&&o.globalTime(0)<=e&&o.globalTime(o.totalDuration())>e:!e||o.isActive())&&i.push(o):(n=o.getTweensOf(r,e)).length&&i.push.apply(i,n),o=o._next;return i},o.tweenTo=function(t,e){e=e||{};var n,i=this,r=Yt(i,t),o=e,s=o.startAt,a=o.onStart,l=o.onStartParams,c=o.immediateRender,u=Qe.to(i,wt({ease:e.ease||"none",lazy:!1,immediateRender:!1,time:r,overwrite:"auto",duration:e.duration||Math.abs((r-(s&&"time"in s?s.time:i._time))/i.timeScale())||w,onStart:function(){if(i.pause(),!n){var t=e.duration||Math.abs((r-(s&&"time"in s?s.time:i._time))/i.timeScale());u._dur!==t&&Bt(u,t,0,1).render(u._time,!0,!0),n=1}a&&a.apply(u,l||[])}},e));return c?u.render(0):u},o.tweenFromTo=function(t,e,n){return this.tweenTo(e,wt({startAt:{time:Yt(this,t)}},n))},o.recent=function(){return this._recent},o.nextLabel=function(t){return void 0===t&&(t=this._time),ce(this,Yt(this,t))},o.previousLabel=function(t){return void 0===t&&(t=this._time),ce(this,Yt(this,t),1)},o.currentLabel=function(t){return arguments.length?this.seek(t,!0):this.previousLabel(this._time+w)},o.shiftChildren=function(t,e,n){void 0===n&&(n=0);for(var i,r=this._first,o=this.labels;r;)r._start>=n&&(r._start+=t,r._end+=t),r=r._next;if(e)for(i in o)o[i]>=n&&(o[i]+=t);return Ot(this)},o.invalidate=function(e){var n=this._first;for(this._lock=0;n;)n.invalidate(e),n=n._next;return t.prototype.invalidate.call(this,e)},o.clear=function(t){void 0===t&&(t=!0);for(var e,n=this._first;n;)e=n._next,this.remove(n),n=e;return this._dp&&(this._time=this._tTime=this._pTime=0),t&&(this.labels={}),Ot(this)},o.totalDuration=function(t){var e,n,i,r=0,o=this,a=o._last,l=b;if(arguments.length)return o.timeScale((o._repeat<0?o.duration():o.totalDuration())/(o.reversed()?-t:t));if(o._dirty){for(i=o.parent;a;)e=a._prev,a._dirty&&a.totalDuration(),(n=a._start)>l&&o._sort&&a._ts&&!o._lock?(o._lock=1,zt(o,a,n-a._delay,1)._lock=0):l=n,n<0&&a._ts&&(r-=n,(!i&&!o._dp||i&&i.smoothChildTiming)&&(o._start+=n/o._ts,o._time-=n,o._tTime-=n),o.shiftChildren(-n,!1,-1/0),l=0),a._end>r&&a._ts&&(r=a._end),a=e;Bt(o,o===s&&o._time>r?o._time:r,1,1),o._dirty=0}return o._tDur},i.updateRoot=function(t){if(s._ts&&(yt(s,jt(t,s)),h=Te.frame),Te.frame>=st){st+=y.autoSleep||120;var e=s._first;if((!e||!e._ts)&&y.autoSleep&&Te._listeners.length<2){for(;e&&!e._ts;)e=e._next;e||Te.sleep()}}},i}(Re);wt(ze.prototype,{_lock:0,_hasPause:0,_forcing:0});var Fe,$e,He=function(t,e,n,i,r,o,s){var a,l,c,u,h,f,d,p,g=new un(this._pt,t,e,0,1,rn,null,r),m=0,v=0;for(g.b=n,g.e=i,n+="",(d=~(i+="").indexOf("random("))&&(i=ae(i)),o&&(o(p=[n,i],t,e),n=p[0],i=p[1]),l=n.match(H)||[];a=H.exec(i);)u=a[0],h=i.substring(m,a.index),c?c=(c+1)%5:"rgba("===h.substr(-5)&&(c=1),u!==l[v++]&&(f=parseFloat(l[v-1])||0,g._pt={_next:g._pt,p:h||1===v?h:",",s:f,c:"="===u.charAt(1)?gt(f,u)-f:parseFloat(u)-f,m:c&&c<4?Math.round:0},m=H.lastIndex);return g.c=m<i.length?i.substring(m,i.length):"",g.fp=s,(q.test(i)||d)&&(g.e=0),this._pt=g,g},qe=function(t,e,n,i,r,o,s,a,l,c){O(i)&&(i=i(r||0,t,o));var u,h=t[e],f="get"!==n?n:O(h)?l?t[e.indexOf("set")||!O(t["get"+e.substr(3)])?e:"get"+e.substr(3)](l):t[e]():h,d=O(h)?l?Je:Ke:Ge;if(A(i)&&(~i.indexOf("random(")&&(i=ae(i)),"="===i.charAt(1)&&((u=gt(f,i)+(Gt(f)||0))||0===u)&&(i=u)),!c||f!==i||$e)return isNaN(f*i)||""===i?(!h&&!(e in t)&&V(e,i),He.call(this,t,e,f,i,d,a||y.stringFilter,l)):(u=new un(this._pt,t,e,+f||0,i-(f||0),"boolean"==typeof h?nn:en,0,d),l&&(u.fp=l),s&&u.modifier(s,this,t),this._pt=u)},Be=function(t,e,n,i,r,o){var s,a,l,c;if(rt[t]&&!1!==(s=new rt[t]).init(r,s.rawVars?e[t]:function(t,e,n,i,r){if(O(t)&&(t=Ye(t,r,e,n,i)),!P(t)||t.style&&t.nodeType||R(t)||I(t))return A(t)?Ye(t,r,e,n,i):t;var o,s={};for(o in t)s[o]=Ye(t[o],r,e,n,i);return s}(e[t],i,r,o,n),n,i,o)&&(n._pt=a=new un(n._pt,r,t,0,1,s.render,s,0,s.priority),n!==f))for(l=n._ptLookup[n._targets.indexOf(r)],c=s._props.length;c--;)l[s._props[c]]=a;return s},We=function t(e,n,o){var a,l,c,u,h,f,d,p,g,m,v,y,x,T=e.vars,E=T.ease,C=T.startAt,S=T.immediateRender,k=T.lazy,A=T.onUpdate,O=T.runBackwards,D=T.yoyoEase,M=T.keyframes,P=T.autoRevert,j=e._dur,N=e._startAt,I=e._targets,R=e.parent,z=R&&"nested"===R.data?R.vars.targets:I,F="auto"===e._overwrite&&!i,$=e.timeline;if($&&(!M||!E)&&(E="none"),e._ease=Me(E,_.ease),e._yEase=D?Oe(Me(!0===D?E:D,_.ease)):0,D&&e._yoyo&&!e._repeat&&(D=e._yEase,e._yEase=e._ease,e._ease=D),e._from=!$&&!!T.runBackwards,!$||M&&!T.stagger){if(y=(p=I[0]?ut(I[0]).harness:0)&&T[p.prop],a=Et(T,et),N&&(N._zTime<0&&N.progress(1),n<0&&O&&S&&!P?N.render(-1,!0):N.revert(O&&j?Z:J),N._lazy=0),C){if(At(e._startAt=Qe.set(I,wt({data:"isStart",overwrite:!1,parent:R,immediateRender:!0,lazy:!N&&L(k),startAt:null,delay:0,onUpdate:A&&function(){return ue(e,"onUpdate")},stagger:0},C))),e._startAt._dp=0,e._startAt._sat=e,n<0&&(r||!S&&!P)&&e._startAt.revert(Z),S&&j&&n<=0&&o<=0)return void(n&&(e._zTime=n))}else if(O&&j&&!N)if(n&&(S=!1),c=wt({overwrite:!1,data:"isFromStart",lazy:S&&!N&&L(k),immediateRender:S,stagger:0,parent:R},a),y&&(c[p.prop]=y),At(e._startAt=Qe.set(I,c)),e._startAt._dp=0,e._startAt._sat=e,n<0&&(r?e._startAt.revert(Z):e._startAt.render(-1,!0)),e._zTime=n,S){if(!n)return}else t(e._startAt,w,w);for(e._pt=e._ptCache=0,k=j&&L(k)||k&&!j,l=0;l<I.length;l++){if(d=(h=I[l])._gsap||ct(I)[l]._gsap,e._ptLookup[l]=m={},it[d.id]&&nt.length&&vt(),v=z===I?l:z.indexOf(h),p&&!1!==(g=new p).init(h,y||a,e,v,z)&&(e._pt=u=new un(e._pt,h,g.name,0,1,g.render,g,0,g.priority),g._props.forEach((function(t){m[t]=u})),g.priority&&(f=1)),!p||y)for(c in a)rt[c]&&(g=Be(c,a,e,v,h,z))?g.priority&&(f=1):m[c]=u=qe.call(e,h,c,"get",a[c],v,z,0,T.stringFilter);e._op&&e._op[l]&&e.kill(h,e._op[l]),F&&e._pt&&(Fe=e,s.killTweensOf(h,m,e.globalTime(n)),x=!e.parent,Fe=0),e._pt&&k&&(it[d.id]=1)}f&&cn(e),e._onInit&&e._onInit(e)}e._onUpdate=A,e._initted=(!e._op||e._pt)&&!x,M&&n<=0&&$.render(b,!0,!0)},Xe=function(t,e,n,i){var r,o,s=e.ease||i||"power1.inOut";if(R(e))o=n[t]||(n[t]=[]),e.forEach((function(t,n){return o.push({t:n/(e.length-1)*100,v:t,e:s})}));else for(r in e)o=n[r]||(n[r]=[]),"ease"===r||o.push({t:parseFloat(t),v:e[r],e:s})},Ye=function(t,e,n,i,r){return O(t)?t.call(e,n,i,r):A(t)&&~t.indexOf("random(")?ae(t):t},Ue=lt+"repeat,repeatDelay,yoyo,repeatRefresh,yoyoEase,autoRevert",Ve={};ft(Ue+",id,stagger,delay,duration,paused,scrollTrigger",(function(t){return Ve[t]=1}));var Qe=function(t){function o(e,r,o,a){var l;"number"==typeof r&&(o.duration=r,r=o,o=null);var c,u,h,f,d,p,g,m,v=(l=t.call(this,a?r:Ct(r))||this).vars,_=v.duration,b=v.delay,w=v.immediateRender,x=v.stagger,T=v.overwrite,E=v.keyframes,C=v.defaults,S=v.scrollTrigger,k=v.yoyoEase,A=r.parent||s,O=(R(e)||I(e)?D(e[0]):"length"in r)?[e]:Zt(e);if(l._targets=O.length?ct(O):Q("GSAP target "+e+" not found. https://gsap.com",!y.nullTargetWarn)||[],l._ptLookup=[],l._overwrite=T,E||x||N(_)||N(b)){if(r=l.vars,(c=l.timeline=new ze({data:"nested",defaults:C||{},targets:A&&"nested"===A.data?A.vars.targets:O})).kill(),c.parent=c._dp=n(l),c._start=0,x||N(_)||N(b)){if(f=O.length,g=x&&ne(x),P(x))for(d in x)~Ue.indexOf(d)&&(m||(m={}),m[d]=x[d]);for(u=0;u<f;u++)(h=Et(r,Ve)).stagger=0,k&&(h.yoyoEase=k),m&&xt(h,m),p=O[u],h.duration=+Ye(_,n(l),u,p,O),h.delay=(+Ye(b,n(l),u,p,O)||0)-l._delay,!x&&1===f&&h.delay&&(l._delay=b=h.delay,l._start+=b,h.delay=0),c.to(p,h,g?g(u,p,O):0),c._ease=Ce.none;c.duration()?_=b=0:l.timeline=0}else if(E){Ct(wt(c.vars.defaults,{ease:"none"})),c._ease=Me(E.ease||r.ease||"none");var M,j,z,F=0;if(R(E))E.forEach((function(t){return c.to(O,t,">")})),c.duration();else{for(d in h={},E)"ease"===d||"easeEach"===d||Xe(d,E[d],h,E.easeEach);for(d in h)for(M=h[d].sort((function(t,e){return t.t-e.t})),F=0,u=0;u<M.length;u++)(z={ease:(j=M[u]).e,duration:(j.t-(u?M[u-1].t:0))/100*_})[d]=j.v,c.to(O,z,F),F+=z.duration;c.duration()<_&&c.to({},{duration:_-c.duration()})}}_||l.duration(_=c.duration())}else l.timeline=0;return!0!==T||i||(Fe=n(l),s.killTweensOf(O),Fe=0),zt(A,n(l),o),r.reversed&&l.reverse(),r.paused&&l.paused(!0),(w||!_&&!E&&l._start===pt(A._time)&&L(w)&&Mt(n(l))&&"nested"!==A.data)&&(l._tTime=-1e-8,l.render(Math.max(0,-b)||0)),S&&Ft(n(l),S),l}e(o,t);var a=o.prototype;return a.render=function(t,e,n){var i,o,s,a,l,c,u,h,f,d=this._time,p=this._tDur,g=this._dur,m=t<0,v=t>p-w&&!m?p:t<w?0:t;if(g){if(v!==this._tTime||!t||n||!this._initted&&this._tTime||this._startAt&&this._zTime<0!==m){if(i=v,h=this.timeline,this._repeat){if(a=g+this._rDelay,this._repeat<-1&&m)return this.totalTime(100*a+t,e,n);if(i=pt(v%a),v===p?(s=this._repeat,i=g):((s=~~(v/a))&&s===pt(v/a)&&(i=g,s--),i>g&&(i=g)),(c=this._yoyo&&1&s)&&(f=this._yEase,i=g-i),l=Lt(this._tTime,a),i===d&&!n&&this._initted&&s===l)return this._tTime=v,this;s!==l&&(h&&this._yEase&&De(h,c),this.vars.repeatRefresh&&!c&&!this._lock&&this._time!==a&&this._initted&&(this._lock=n=1,this.render(pt(a*s),!0).invalidate()._lock=0))}if(!this._initted){if($t(this,m?t:i,n,e,v))return this._tTime=0,this;if(!(d===this._time||n&&this.vars.repeatRefresh&&s!==l))return this;if(g!==this._dur)return this.render(t,e,n)}if(this._tTime=v,this._time=i,!this._act&&this._ts&&(this._act=1,this._lazy=0),this.ratio=u=(f||this._ease)(i/g),this._from&&(this.ratio=u=1-u),i&&!d&&!e&&!s&&(ue(this,"onStart"),this._tTime!==v))return this;for(o=this._pt;o;)o.r(u,o.d),o=o._next;h&&h.render(t<0?t:h._dur*h._ease(i/this._dur),e,n)||this._startAt&&(this._zTime=t),this._onUpdate&&!e&&(m&&Dt(this,t,0,n),ue(this,"onUpdate")),this._repeat&&s!==l&&this.vars.onRepeat&&!e&&this.parent&&ue(this,"onRepeat"),v!==this._tDur&&v||this._tTime!==v||(m&&!this._onUpdate&&Dt(this,t,0,!0),(t||!g)&&(v===this._tDur&&this._ts>0||!v&&this._ts<0)&&At(this,1),e||m&&!d||!(v||d||c)||(ue(this,v===p?"onComplete":"onReverseComplete",!0),this._prom&&!(v<p&&this.timeScale()>0)&&this._prom()))}}else!function(t,e,n,i){var o,s,a,l=t.ratio,c=e<0||!e&&(!t._start&&Ht(t)&&(t._initted||!qt(t))||(t._ts<0||t._dp._ts<0)&&!qt(t))?0:1,u=t._rDelay,h=0;if(u&&t._repeat&&(h=Qt(0,t._tDur,e),s=Lt(h,u),t._yoyo&&1&s&&(c=1-c),s!==Lt(t._tTime,u)&&(l=1-c,t.vars.repeatRefresh&&t._initted&&t.invalidate())),c!==l||r||i||t._zTime===w||!e&&t._zTime){if(!t._initted&&$t(t,e,i,n,h))return;for(a=t._zTime,t._zTime=e||(n?w:0),n||(n=e&&!a),t.ratio=c,t._from&&(c=1-c),t._time=0,t._tTime=h,o=t._pt;o;)o.r(c,o.d),o=o._next;e<0&&Dt(t,e,0,!0),t._onUpdate&&!n&&ue(t,"onUpdate"),h&&t._repeat&&!n&&t.parent&&ue(t,"onRepeat"),(e>=t._tDur||e<0)&&t.ratio===c&&(c&&At(t,1),n||r||(ue(t,c?"onComplete":"onReverseComplete",!0),t._prom&&t._prom()))}else t._zTime||(t._zTime=e)}(this,t,e,n);return this},a.targets=function(){return this._targets},a.invalidate=function(e){return(!e||!this.vars.runBackwards)&&(this._startAt=0),this._pt=this._op=this._onUpdate=this._lazy=this.ratio=0,this._ptLookup=[],this.timeline&&this.timeline.invalidate(e),t.prototype.invalidate.call(this,e)},a.resetTo=function(t,e,n,i,r){d||Te.wake(),this._ts||this.play();var o=Math.min(this._dur,(this._dp._time-this._start)*this._ts);return this._initted||We(this,o),function(t,e,n,i,r,o,s,a){var l,c,u,h,f=(t._pt&&t._ptCache||(t._ptCache={}))[e];if(!f)for(f=t._ptCache[e]=[],u=t._ptLookup,h=t._targets.length;h--;){if((l=u[h][e])&&l.d&&l.d._pt)for(l=l.d._pt;l&&l.p!==e&&l.fp!==e;)l=l._next;if(!l)return $e=1,t.vars[e]="+=0",We(t,s),$e=0,a?Q(e+" not eligible for reset"):1;f.push(l)}for(h=f.length;h--;)(l=(c=f[h])._pt||c).s=!i&&0!==i||r?l.s+(i||0)+o*l.c:i,l.c=n-l.s,c.e&&(c.e=dt(n)+Gt(c.e)),c.b&&(c.b=l.s+Gt(c.b))}(this,t,e,n,i,this._ease(o/this._dur),o,r)?this.resetTo(t,e,n,i,1):(It(this,0),this.parent||St(this._dp,this,"_first","_last",this._dp._sort?"_start":0),this.render(0))},a.kill=function(t,e){if(void 0===e&&(e="all"),!(t||e&&"all"!==e))return this._lazy=this._pt=0,this.parent?he(this):this;if(this.timeline){var n=this.timeline.totalDuration();return this.timeline.killTweensOf(t,e,Fe&&!0!==Fe.vars.overwrite)._first||he(this),this.parent&&n!==this.timeline.totalDuration()&&Bt(this,this._dur*this.timeline._tDur/n,0,1),this}var i,r,o,s,a,l,c,u=this._targets,h=t?Zt(t):u,f=this._ptLookup,d=this._pt;if((!e||"all"===e)&&function(t,e){for(var n=t.length,i=n===e.length;i&&n--&&t[n]===e[n];);return n<0}(u,h))return"all"===e&&(this._pt=0),he(this);for(i=this._op=this._op||[],"all"!==e&&(A(e)&&(a={},ft(e,(function(t){return a[t]=1})),e=a),e=function(t,e){var n,i,r,o,s=t[0]?ut(t[0]).harness:0,a=s&&s.aliases;if(!a)return e;for(i in n=xt({},e),a)if(i in n)for(r=(o=a[i].split(",")).length;r--;)n[o[r]]=n[i];return n}(u,e)),c=u.length;c--;)if(~h.indexOf(u[c]))for(a in r=f[c],"all"===e?(i[c]=e,s=r,o={}):(o=i[c]=i[c]||{},s=e),s)(l=r&&r[a])&&("kill"in l.d&&!0!==l.d.kill(a)||kt(this,l,"_pt"),delete r[a]),"all"!==o&&(o[a]=1);return this._initted&&!this._pt&&d&&he(this),this},o.to=function(t,e){return new o(t,e,arguments[2])},o.from=function(t,e){return Ut(1,arguments)},o.delayedCall=function(t,e,n,i){return new o(e,0,{immediateRender:!1,lazy:!1,overwrite:!1,delay:t,onComplete:e,onReverseComplete:e,onCompleteParams:n,onReverseCompleteParams:n,callbackScope:i})},o.fromTo=function(t,e,n){return Ut(2,arguments)},o.set=function(t,e){return e.duration=0,e.repeatDelay||(e.repeat=0),new o(t,e)},o.killTweensOf=function(t,e,n){return s.killTweensOf(t,e,n)},o}(Re);wt(Qe.prototype,{_targets:[],_lazy:0,_startAt:0,_op:0,_onInit:0}),ft("staggerTo,staggerFrom,staggerFromTo",(function(t){Qe[t]=function(){var e=new ze,n=Kt.call(arguments,0);return n.splice("staggerFromTo"===t?5:4,0,0),e[t].apply(e,n)}}));var Ge=function(t,e,n){return t[e]=n},Ke=function(t,e,n){return t[e](n)},Je=function(t,e,n,i){return t[e](i.fp,n)},Ze=function(t,e,n){return t.setAttribute(e,n)},tn=function(t,e){return O(t[e])?Ke:M(t[e])&&t.setAttribute?Ze:Ge},en=function(t,e){return e.set(e.t,e.p,Math.round(1e6*(e.s+e.c*t))/1e6,e)},nn=function(t,e){return e.set(e.t,e.p,!!(e.s+e.c*t),e)},rn=function(t,e){var n=e._pt,i="";if(!t&&e.b)i=e.b;else if(1===t&&e.e)i=e.e;else{for(;n;)i=n.p+(n.m?n.m(n.s+n.c*t):Math.round(1e4*(n.s+n.c*t))/1e4)+i,n=n._next;i+=e.c}e.set(e.t,e.p,i,e)},on=function(t,e){for(var n=e._pt;n;)n.r(t,n.d),n=n._next},sn=function(t,e,n,i){for(var r,o=this._pt;o;)r=o._next,o.p===i&&o.modifier(t,e,n),o=r},an=function(t){for(var e,n,i=this._pt;i;)n=i._next,i.p===t&&!i.op||i.op===t?kt(this,i,"_pt"):i.dep||(e=1),i=n;return!e},ln=function(t,e,n,i){i.mSet(t,e,i.m.call(i.tween,n,i.mt),i)},cn=function(t){for(var e,n,i,r,o=t._pt;o;){for(e=o._next,n=i;n&&n.pr>o.pr;)n=n._next;(o._prev=n?n._prev:r)?o._prev._next=o:i=o,(o._next=n)?n._prev=o:r=o,o=e}t._pt=i},un=function(){function t(t,e,n,i,r,o,s,a,l){this.t=e,this.s=i,this.c=r,this.p=n,this.r=o||en,this.d=s||this,this.set=a||Ge,this.pr=l||0,this._next=t,t&&(t._prev=this)}return t.prototype.modifier=function(t,e,n){this.mSet=this.mSet||this.set,this.set=ln,this.m=t,this.mt=n,this.tween=e},t}();ft(lt+"parent,duration,ease,delay,overwrite,runBackwards,startAt,yoyo,immediateRender,repeat,repeatDelay,data,paused,reversed,lazy,callbackScope,stringFilter,id,yoyoEase,stagger,inherit,repeatRefresh,keyframes,autoRevert,scrollTrigger",(function(t){return et[t]=1})),X.TweenMax=X.TweenLite=Qe,X.TimelineLite=X.TimelineMax=ze,s=new ze({sortChildren:!1,defaults:_,autoRemoveChildren:!0,id:"root",smoothChildTiming:!0}),y.stringFilter=xe;var hn=[],fn={},dn=[],pn=0,gn=0,mn=function(t){return(fn[t]||dn).map((function(t){return t()}))},vn=function(){var t=Date.now(),e=[];t-pn>2&&(mn("matchMediaInit"),hn.forEach((function(t){var n,i,r,o,s=t.queries,l=t.conditions;for(i in s)(n=a.matchMedia(s[i]).matches)&&(r=1),n!==l[i]&&(l[i]=n,o=1);o&&(t.revert(),r&&e.push(t))})),mn("matchMediaRevert"),e.forEach((function(t){return t.onMatch(t,(function(e){return t.add(null,e)}))})),pn=t,mn("matchMedia"))},yn=function(){function t(t,e){this.selector=e&&te(e),this.data=[],this._r=[],this.isReverted=!1,this.id=gn++,t&&this.add(t)}var e=t.prototype;return e.add=function(t,e,n){O(t)&&(n=e,e=t,t=O);var i=this,r=function(){var t,r=o,s=i.selector;return r&&r!==i&&r.data.push(i),n&&(i.selector=te(n)),o=i,t=e.apply(i,arguments),O(t)&&i._r.push(t),o=r,i.selector=s,i.isReverted=!1,t};return i.last=r,t===O?r(i,(function(t){return i.add(null,t)})):t?i[t]=r:r},e.ignore=function(t){var e=o;o=null,t(this),o=e},e.getTweens=function(){var e=[];return this.data.forEach((function(n){return n instanceof t?e.push.apply(e,n.getTweens()):n instanceof Qe&&!(n.parent&&"nested"===n.parent.data)&&e.push(n)})),e},e.clear=function(){this._r.length=this.data.length=0},e.kill=function(t,e){var n=this;if(t?function(){for(var e,i=n.getTweens(),r=n.data.length;r--;)"isFlip"===(e=n.data[r]).data&&(e.revert(),e.getChildren(!0,!0,!1).forEach((function(t){return i.splice(i.indexOf(t),1)})));for(i.map((function(t){return{g:t._dur||t._delay||t._sat&&!t._sat.vars.immediateRender?t.globalTime(0):-1/0,t:t}})).sort((function(t,e){return e.g-t.g||-1/0})).forEach((function(e){return e.t.revert(t)})),r=n.data.length;r--;)(e=n.data[r])instanceof ze?"nested"!==e.data&&(e.scrollTrigger&&e.scrollTrigger.revert(),e.kill()):!(e instanceof Qe)&&e.revert&&e.revert(t);n._r.forEach((function(e){return e(t,n)})),n.isReverted=!0}():this.data.forEach((function(t){return t.kill&&t.kill()})),this.clear(),e)for(var i=hn.length;i--;)hn[i].id===this.id&&hn.splice(i,1)},e.revert=function(t){this.kill(t||{})},t}(),_n=function(){function t(t){this.contexts=[],this.scope=t,o&&o.data.push(this)}var e=t.prototype;return e.add=function(t,e,n){P(t)||(t={matches:t});var i,r,s,l=new yn(0,n||this.scope),c=l.conditions={};for(r in o&&!l.selector&&(l.selector=o.selector),this.contexts.push(l),e=l.add("onMatch",e),l.queries=t,t)"all"===r?s=1:(i=a.matchMedia(t[r]))&&(hn.indexOf(l)<0&&hn.push(l),(c[r]=i.matches)&&(s=1),i.addListener?i.addListener(vn):i.addEventListener("change",vn));return s&&e(l,(function(t){return l.add(null,t)})),this},e.revert=function(t){this.kill(t||{})},e.kill=function(t){this.contexts.forEach((function(e){return e.kill(t,!0)}))},t}(),bn={registerPlugin:function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];e.forEach((function(t){return de(t)}))},timeline:function(t){return new ze(t)},getTweensOf:function(t,e){return s.getTweensOf(t,e)},getProperty:function(t,e,n,i){A(t)&&(t=Zt(t)[0]);var r=ut(t||{}).get,o=n?bt:_t;return"native"===n&&(n=""),t?e?o((rt[e]&&rt[e].get||r)(t,e,n,i)):function(e,n,i){return o((rt[e]&&rt[e].get||r)(t,e,n,i))}:t},quickSetter:function(t,e,n){if((t=Zt(t)).length>1){var i=t.map((function(t){return Tn.quickSetter(t,e,n)})),r=i.length;return function(t){for(var e=r;e--;)i[e](t)}}t=t[0]||{};var o=rt[e],s=ut(t),a=s.harness&&(s.harness.aliases||{})[e]||e,l=o?function(e){var i=new o;f._pt=0,i.init(t,n?e+n:e,f,0,[t]),i.render(1,i),f._pt&&on(1,f)}:s.set(t,a);return o?l:function(e){return l(t,a,n?e+n:e,s,1)}},quickTo:function(t,e,n){var i,r=Tn.to(t,xt(((i={})[e]="+=0.1",i.paused=!0,i),n||{})),o=function(t,n,i){return r.resetTo(e,t,n,i)};return o.tween=r,o},isTweening:function(t){return s.getTweensOf(t,!0).length>0},defaults:function(t){return t&&t.ease&&(t.ease=Me(t.ease,_.ease)),Tt(_,t||{})},config:function(t){return Tt(y,t||{})},registerEffect:function(t){var e=t.name,n=t.effect,i=t.plugins,r=t.defaults,o=t.extendTimeline;(i||"").split(",").forEach((function(t){return t&&!rt[t]&&!X[t]&&Q(e+" effect requires "+t+" plugin.")})),ot[e]=function(t,e,i){return n(Zt(t),wt(e||{},r),i)},o&&(ze.prototype[e]=function(t,n,i){return this.add(ot[e](t,P(n)?n:(i=n)&&{},this),i)})},registerEase:function(t,e){Ce[t]=Me(e)},parseEase:function(t,e){return arguments.length?Me(t,e):Ce},getById:function(t){return s.getById(t)},exportRoot:function(t,e){void 0===t&&(t={});var n,i,r=new ze(t);for(r.smoothChildTiming=L(t.smoothChildTiming),s.remove(r),r._dp=0,r._time=r._tTime=s._time,n=s._first;n;)i=n._next,!e&&!n._dur&&n instanceof Qe&&n.vars.onComplete===n._targets[0]||zt(r,n,n._start-n._delay),n=i;return zt(s,r,0),r},context:function(t,e){return t?new yn(t,e):o},matchMedia:function(t){return new _n(t)},matchMediaRefresh:function(){return hn.forEach((function(t){var e,n,i=t.conditions;for(n in i)i[n]&&(i[n]=!1,e=1);e&&t.revert()}))||vn()},addEventListener:function(t,e){var n=fn[t]||(fn[t]=[]);~n.indexOf(e)||n.push(e)},removeEventListener:function(t,e){var n=fn[t],i=n&&n.indexOf(e);i>=0&&n.splice(i,1)},utils:{wrap:function t(e,n,i){var r=n-e;return R(e)?se(e,t(0,e.length),n):Vt(i,(function(t){return(r+(t-e)%r)%r+e}))},wrapYoyo:function t(e,n,i){var r=n-e,o=2*r;return R(e)?se(e,t(0,e.length-1),n):Vt(i,(function(t){return e+((t=(o+(t-e)%o)%o||0)>r?o-t:t)}))},distribute:ne,random:oe,snap:re,normalize:function(t,e,n){return le(t,e,0,1,n)},getUnit:Gt,clamp:function(t,e,n){return Vt(n,(function(n){return Qt(t,e,n)}))},splitColor:ve,toArray:Zt,selector:te,mapRange:le,pipe:function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];return function(t){return e.reduce((function(t,e){return e(t)}),t)}},unitize:function(t,e){return function(n){return t(parseFloat(n))+(e||Gt(n))}},interpolate:function t(e,n,i,r){var o=isNaN(e+n)?0:function(t){return(1-t)*e+t*n};if(!o){var s,a,l,c,u,h=A(e),f={};if(!0===i&&(r=1)&&(i=null),h)e={p:e},n={p:n};else if(R(e)&&!R(n)){for(l=[],c=e.length,u=c-2,a=1;a<c;a++)l.push(t(e[a-1],e[a]));c--,o=function(t){t*=c;var e=Math.min(u,~~t);return l[e](t-e)},i=n}else r||(e=xt(R(e)?[]:{},e));if(!l){for(s in n)qe.call(f,e,s,"get",n[s]);o=function(t){return on(t,f)||(h?e.p:e)}}}return Vt(i,o)},shuffle:ee},install:U,effects:ot,ticker:Te,updateRoot:ze.updateRoot,plugins:rt,globalTimeline:s,core:{PropTween:un,globals:G,Tween:Qe,Timeline:ze,Animation:Re,getCache:ut,_removeLinkedListItem:kt,reverting:function(){return r},context:function(t){return t&&o&&(o.data.push(t),t._ctx=o),o},suppressOverwrites:function(t){return i=t}}};ft("to,from,fromTo,delayedCall,set,killTweensOf",(function(t){return bn[t]=Qe[t]})),Te.add(ze.updateRoot),f=bn.to({},{duration:0});var wn=function(t,e){for(var n=t._pt;n&&n.p!==e&&n.op!==e&&n.fp!==e;)n=n._next;return n},xn=function(t,e){return{name:t,rawVars:1,init:function(t,n,i){i._onInit=function(t){var i,r;if(A(n)&&(i={},ft(n,(function(t){return i[t]=1})),n=i),e){for(r in i={},n)i[r]=e(n[r]);n=i}!function(t,e){var n,i,r,o=t._targets;for(n in e)for(i=o.length;i--;)(r=t._ptLookup[i][n])&&(r=r.d)&&(r._pt&&(r=wn(r,n)),r&&r.modifier&&r.modifier(e[n],t,o[i],n))}(t,n)}}}},Tn=bn.registerPlugin({name:"attr",init:function(t,e,n,i,r){var o,s,a;for(o in this.tween=n,e)a=t.getAttribute(o)||"",(s=this.add(t,"setAttribute",(a||0)+"",e[o],i,r,0,0,o)).op=o,s.b=a,this._props.push(o)},render:function(t,e){for(var n=e._pt;n;)r?n.set(n.t,n.p,n.b,n):n.r(t,n.d),n=n._next}},{name:"endArray",init:function(t,e){for(var n=e.length;n--;)this.add(t,n,t[n]||0,e[n],0,0,0,0,0,1)}},xn("roundProps",ie),xn("modifiers"),xn("snap",re))||bn;Qe.version=ze.version=Tn.version="3.12.5",u=1,j()&&Ee();var En,Cn,Sn,kn,An,On,Dn,Mn,Pn=Ce.Power0,Ln=Ce.Power1,jn=Ce.Power2,Nn=Ce.Power3,In=Ce.Power4,Rn=Ce.Linear,zn=Ce.Quad,Fn=Ce.Cubic,$n=Ce.Quart,Hn=Ce.Quint,qn=Ce.Strong,Bn=Ce.Elastic,Wn=Ce.Back,Xn=Ce.SteppedEase,Yn=Ce.Bounce,Un=Ce.Sine,Vn=Ce.Expo,Qn=Ce.Circ,Gn={},Kn=180/Math.PI,Jn=Math.PI/180,Zn=Math.atan2,ti=/([A-Z])/g,ei=/(left|right|width|margin|padding|x)/i,ni=/[\s,\(]\S/,ii={autoAlpha:"opacity,visibility",scale:"scaleX,scaleY",alpha:"opacity"},ri=function(t,e){return e.set(e.t,e.p,Math.round(1e4*(e.s+e.c*t))/1e4+e.u,e)},oi=function(t,e){return e.set(e.t,e.p,1===t?e.e:Math.round(1e4*(e.s+e.c*t))/1e4+e.u,e)},si=function(t,e){return e.set(e.t,e.p,t?Math.round(1e4*(e.s+e.c*t))/1e4+e.u:e.b,e)},ai=function(t,e){var n=e.s+e.c*t;e.set(e.t,e.p,~~(n+(n<0?-.5:.5))+e.u,e)},li=function(t,e){return e.set(e.t,e.p,t?e.e:e.b,e)},ci=function(t,e){return e.set(e.t,e.p,1!==t?e.b:e.e,e)},ui=function(t,e,n){return t.style[e]=n},hi=function(t,e,n){return t.style.setProperty(e,n)},fi=function(t,e,n){return t._gsap[e]=n},di=function(t,e,n){return t._gsap.scaleX=t._gsap.scaleY=n},pi=function(t,e,n,i,r){var o=t._gsap;o.scaleX=o.scaleY=n,o.renderTransform(r,o)},gi=function(t,e,n,i,r){var o=t._gsap;o[e]=n,o.renderTransform(r,o)},mi="transform",vi=mi+"Origin",yi=function t(e,n){var i=this,r=this.target,o=r.style,s=r._gsap;if(e in Gn&&o){if(this.tfm=this.tfm||{},"transform"===e)return ii.transform.split(",").forEach((function(e){return t.call(i,e,n)}));if(~(e=ii[e]||e).indexOf(",")?e.split(",").forEach((function(t){return i.tfm[t]=Ii(r,t)})):this.tfm[e]=s.x?s[e]:Ii(r,e),e===vi&&(this.tfm.zOrigin=s.zOrigin),this.props.indexOf(mi)>=0)return;s.svg&&(this.svgo=r.getAttribute("data-svg-origin"),this.props.push(vi,n,"")),e=mi}(o||n)&&this.props.push(e,n,o[e])},_i=function(t){t.translate&&(t.removeProperty("translate"),t.removeProperty("scale"),t.removeProperty("rotate"))},bi=function(){var t,e,n=this.props,i=this.target,r=i.style,o=i._gsap;for(t=0;t<n.length;t+=3)n[t+1]?i[n[t]]=n[t+2]:n[t+2]?r[n[t]]=n[t+2]:r.removeProperty("--"===n[t].substr(0,2)?n[t]:n[t].replace(ti,"-$1").toLowerCase());if(this.tfm){for(e in this.tfm)o[e]=this.tfm[e];o.svg&&(o.renderTransform(),i.setAttribute("data-svg-origin",this.svgo||"")),(t=Dn())&&t.isStart||r[mi]||(_i(r),o.zOrigin&&r[vi]&&(r[vi]+=" "+o.zOrigin+"px",o.zOrigin=0,o.renderTransform()),o.uncache=1)}},wi=function(t,e){var n={target:t,props:[],revert:bi,save:yi};return t._gsap||Tn.core.getCache(t),e&&e.split(",").forEach((function(t){return n.save(t)})),n},xi=function(t,e){var n=Cn.createElementNS?Cn.createElementNS((e||"http://www.w3.org/1999/xhtml").replace(/^https/,"http"),t):Cn.createElement(t);return n&&n.style?n:Cn.createElement(t)},Ti=function t(e,n,i){var r=getComputedStyle(e);return r[n]||r.getPropertyValue(n.replace(ti,"-$1").toLowerCase())||r.getPropertyValue(n)||!i&&t(e,Ci(n)||n,1)||""},Ei="O,Moz,ms,Ms,Webkit".split(","),Ci=function(t,e,n){var i=(e||An).style,r=5;if(t in i&&!n)return t;for(t=t.charAt(0).toUpperCase()+t.substr(1);r--&&!(Ei[r]+t in i););return r<0?null:(3===r?"ms":r>=0?Ei[r]:"")+t},Si=function(){"undefined"!=typeof window&&window.document&&(En=window,Cn=En.document,Sn=Cn.documentElement,An=xi("div")||{style:{}},xi("div"),mi=Ci(mi),vi=mi+"Origin",An.style.cssText="border-width:0;line-height:0;position:absolute;padding:0",Mn=!!Ci("perspective"),Dn=Tn.core.reverting,kn=1)},ki=function t(e){var n,i=xi("svg",this.ownerSVGElement&&this.ownerSVGElement.getAttribute("xmlns")||"http://www.w3.org/2000/svg"),r=this.parentNode,o=this.nextSibling,s=this.style.cssText;if(Sn.appendChild(i),i.appendChild(this),this.style.display="block",e)try{n=this.getBBox(),this._gsapBBox=this.getBBox,this.getBBox=t}catch(t){}else this._gsapBBox&&(n=this._gsapBBox());return r&&(o?r.insertBefore(this,o):r.appendChild(this)),Sn.removeChild(i),this.style.cssText=s,n},Ai=function(t,e){for(var n=e.length;n--;)if(t.hasAttribute(e[n]))return t.getAttribute(e[n])},Oi=function(t){var e;try{e=t.getBBox()}catch(n){e=ki.call(t,!0)}return e&&(e.width||e.height)||t.getBBox===ki||(e=ki.call(t,!0)),!e||e.width||e.x||e.y?e:{x:+Ai(t,["x","cx","x1"])||0,y:+Ai(t,["y","cy","y1"])||0,width:0,height:0}},Di=function(t){return!(!t.getCTM||t.parentNode&&!t.ownerSVGElement||!Oi(t))},Mi=function(t,e){if(e){var n,i=t.style;e in Gn&&e!==vi&&(e=mi),i.removeProperty?("ms"!==(n=e.substr(0,2))&&"webkit"!==e.substr(0,6)||(e="-"+e),i.removeProperty("--"===n?e:e.replace(ti,"-$1").toLowerCase())):i.removeAttribute(e)}},Pi=function(t,e,n,i,r,o){var s=new un(t._pt,e,n,0,1,o?ci:li);return t._pt=s,s.b=i,s.e=r,t._props.push(n),s},Li={deg:1,rad:1,turn:1},ji={grid:1,flex:1},Ni=function t(e,n,i,r){var o,s,a,l,c=parseFloat(i)||0,u=(i+"").trim().substr((c+"").length)||"px",h=An.style,f=ei.test(n),d="svg"===e.tagName.toLowerCase(),p=(d?"client":"offset")+(f?"Width":"Height"),g=100,m="px"===r,v="%"===r;if(r===u||!c||Li[r]||Li[u])return c;if("px"!==u&&!m&&(c=t(e,n,i,"px")),l=e.getCTM&&Di(e),(v||"%"===u)&&(Gn[n]||~n.indexOf("adius")))return o=l?e.getBBox()[f?"width":"height"]:e[p],dt(v?c/o*g:c/100*o);if(h[f?"width":"height"]=g+(m?u:r),s=~n.indexOf("adius")||"em"===r&&e.appendChild&&!d?e:e.parentNode,l&&(s=(e.ownerSVGElement||{}).parentNode),s&&s!==Cn&&s.appendChild||(s=Cn.body),(a=s._gsap)&&v&&a.width&&f&&a.time===Te.time&&!a.uncache)return dt(c/a.width*g);if(!v||"height"!==n&&"width"!==n)(v||"%"===u)&&!ji[Ti(s,"display")]&&(h.position=Ti(e,"position")),s===e&&(h.position="static"),s.appendChild(An),o=An[p],s.removeChild(An),h.position="absolute";else{var y=e.style[n];e.style[n]=g+r,o=e[p],y?e.style[n]=y:Mi(e,n)}return f&&v&&((a=ut(s)).time=Te.time,a.width=s[p]),dt(m?o*c/g:o&&c?g/o*c:0)},Ii=function(t,e,n,i){var r;return kn||Si(),e in ii&&"transform"!==e&&~(e=ii[e]).indexOf(",")&&(e=e.split(",")[0]),Gn[e]&&"transform"!==e?(r=Ui(t,i),r="transformOrigin"!==e?r[e]:r.svg?r.origin:Vi(Ti(t,vi))+" "+r.zOrigin+"px"):(!(r=t.style[e])||"auto"===r||i||~(r+"").indexOf("calc("))&&(r=$i[e]&&$i[e](t,e,n)||Ti(t,e)||ht(t,e)||("opacity"===e?1:0)),n&&!~(r+"").trim().indexOf(" ")?Ni(t,e,r,n)+n:r},Ri=function(t,e,n,i){if(!n||"none"===n){var r=Ci(e,t,1),o=r&&Ti(t,r,1);o&&o!==n?(e=r,n=o):"borderColor"===e&&(n=Ti(t,"borderTopColor"))}var s,a,l,c,u,h,f,d,p,g,m,v=new un(this._pt,t.style,e,0,1,rn),_=0,b=0;if(v.b=n,v.e=i,n+="","auto"==(i+="")&&(h=t.style[e],t.style[e]=i,i=Ti(t,e)||i,h?t.style[e]=h:Mi(t,e)),xe(s=[n,i]),i=s[1],l=(n=s[0]).match($)||[],(i.match($)||[]).length){for(;a=$.exec(i);)f=a[0],p=i.substring(_,a.index),u?u=(u+1)%5:"rgba("!==p.substr(-5)&&"hsla("!==p.substr(-5)||(u=1),f!==(h=l[b++]||"")&&(c=parseFloat(h)||0,m=h.substr((c+"").length),"="===f.charAt(1)&&(f=gt(c,f)+m),d=parseFloat(f),g=f.substr((d+"").length),_=$.lastIndex-g.length,g||(g=g||y.units[e]||m,_===i.length&&(i+=g,v.e+=g)),m!==g&&(c=Ni(t,e,h,g)||0),v._pt={_next:v._pt,p:p||1===b?p:",",s:c,c:d-c,m:u&&u<4||"zIndex"===e?Math.round:0});v.c=_<i.length?i.substring(_,i.length):""}else v.r="display"===e&&"none"===i?ci:li;return q.test(i)&&(v.e=0),this._pt=v,v},zi={top:"0%",bottom:"100%",left:"0%",right:"100%",center:"50%"},Fi=function(t,e){if(e.tween&&e.tween._time===e.tween._dur){var n,i,r,o=e.t,s=o.style,a=e.u,l=o._gsap;if("all"===a||!0===a)s.cssText="",i=1;else for(r=(a=a.split(",")).length;--r>-1;)n=a[r],Gn[n]&&(i=1,n="transformOrigin"===n?vi:mi),Mi(o,n);i&&(Mi(o,mi),l&&(l.svg&&o.removeAttribute("transform"),Ui(o,1),l.uncache=1,_i(s)))}},$i={clearProps:function(t,e,n,i,r){if("isFromStart"!==r.data){var o=t._pt=new un(t._pt,e,n,0,0,Fi);return o.u=i,o.pr=-10,o.tween=r,t._props.push(n),1}}},Hi=[1,0,0,1,0,0],qi={},Bi=function(t){return"matrix(1, 0, 0, 1, 0, 0)"===t||"none"===t||!t},Wi=function(t){var e=Ti(t,mi);return Bi(e)?Hi:e.substr(7).match(F).map(dt)},Xi=function(t,e){var n,i,r,o,s=t._gsap||ut(t),a=t.style,l=Wi(t);return s.svg&&t.getAttribute("transform")?"1,0,0,1,0,0"===(l=[(r=t.transform.baseVal.consolidate().matrix).a,r.b,r.c,r.d,r.e,r.f]).join(",")?Hi:l:(l!==Hi||t.offsetParent||t===Sn||s.svg||(r=a.display,a.display="block",(n=t.parentNode)&&t.offsetParent||(o=1,i=t.nextElementSibling,Sn.appendChild(t)),l=Wi(t),r?a.display=r:Mi(t,"display"),o&&(i?n.insertBefore(t,i):n?n.appendChild(t):Sn.removeChild(t))),e&&l.length>6?[l[0],l[1],l[4],l[5],l[12],l[13]]:l)},Yi=function(t,e,n,i,r,o){var s,a,l,c=t._gsap,u=r||Xi(t,!0),h=c.xOrigin||0,f=c.yOrigin||0,d=c.xOffset||0,p=c.yOffset||0,g=u[0],m=u[1],v=u[2],y=u[3],_=u[4],b=u[5],w=e.split(" "),x=parseFloat(w[0])||0,T=parseFloat(w[1])||0;n?u!==Hi&&(a=g*y-m*v)&&(l=x*(-m/a)+T*(g/a)-(g*b-m*_)/a,x=x*(y/a)+T*(-v/a)+(v*b-y*_)/a,T=l):(x=(s=Oi(t)).x+(~w[0].indexOf("%")?x/100*s.width:x),T=s.y+(~(w[1]||w[0]).indexOf("%")?T/100*s.height:T)),i||!1!==i&&c.smooth?(_=x-h,b=T-f,c.xOffset=d+(_*g+b*v)-_,c.yOffset=p+(_*m+b*y)-b):c.xOffset=c.yOffset=0,c.xOrigin=x,c.yOrigin=T,c.smooth=!!i,c.origin=e,c.originIsAbsolute=!!n,t.style[vi]="0px 0px",o&&(Pi(o,c,"xOrigin",h,x),Pi(o,c,"yOrigin",f,T),Pi(o,c,"xOffset",d,c.xOffset),Pi(o,c,"yOffset",p,c.yOffset)),t.setAttribute("data-svg-origin",x+" "+T)},Ui=function(t,e){var n=t._gsap||new Ie(t);if("x"in n&&!e&&!n.uncache)return n;var i,r,o,s,a,l,c,u,h,f,d,p,g,m,v,_,b,w,x,T,E,C,S,k,A,O,D,M,P,L,j,N,I=t.style,R=n.scaleX<0,z="px",F="deg",$=getComputedStyle(t),H=Ti(t,vi)||"0";return i=r=o=l=c=u=h=f=d=0,s=a=1,n.svg=!(!t.getCTM||!Di(t)),$.translate&&("none"===$.translate&&"none"===$.scale&&"none"===$.rotate||(I[mi]=("none"!==$.translate?"translate3d("+($.translate+" 0 0").split(" ").slice(0,3).join(", ")+") ":"")+("none"!==$.rotate?"rotate("+$.rotate+") ":"")+("none"!==$.scale?"scale("+$.scale.split(" ").join(",")+") ":"")+("none"!==$[mi]?$[mi]:"")),I.scale=I.rotate=I.translate="none"),m=Xi(t,n.svg),n.svg&&(n.uncache?(A=t.getBBox(),H=n.xOrigin-A.x+"px "+(n.yOrigin-A.y)+"px",k=""):k=!e&&t.getAttribute("data-svg-origin"),Yi(t,k||H,!!k||n.originIsAbsolute,!1!==n.smooth,m)),p=n.xOrigin||0,g=n.yOrigin||0,m!==Hi&&(w=m[0],x=m[1],T=m[2],E=m[3],i=C=m[4],r=S=m[5],6===m.length?(s=Math.sqrt(w*w+x*x),a=Math.sqrt(E*E+T*T),l=w||x?Zn(x,w)*Kn:0,(h=T||E?Zn(T,E)*Kn+l:0)&&(a*=Math.abs(Math.cos(h*Jn))),n.svg&&(i-=p-(p*w+g*T),r-=g-(p*x+g*E))):(N=m[6],L=m[7],D=m[8],M=m[9],P=m[10],j=m[11],i=m[12],r=m[13],o=m[14],c=(v=Zn(N,P))*Kn,v&&(k=C*(_=Math.cos(-v))+D*(b=Math.sin(-v)),A=S*_+M*b,O=N*_+P*b,D=C*-b+D*_,M=S*-b+M*_,P=N*-b+P*_,j=L*-b+j*_,C=k,S=A,N=O),u=(v=Zn(-T,P))*Kn,v&&(_=Math.cos(-v),j=E*(b=Math.sin(-v))+j*_,w=k=w*_-D*b,x=A=x*_-M*b,T=O=T*_-P*b),l=(v=Zn(x,w))*Kn,v&&(k=w*(_=Math.cos(v))+x*(b=Math.sin(v)),A=C*_+S*b,x=x*_-w*b,S=S*_-C*b,w=k,C=A),c&&Math.abs(c)+Math.abs(l)>359.9&&(c=l=0,u=180-u),s=dt(Math.sqrt(w*w+x*x+T*T)),a=dt(Math.sqrt(S*S+N*N)),v=Zn(C,S),h=Math.abs(v)>2e-4?v*Kn:0,d=j?1/(j<0?-j:j):0),n.svg&&(k=t.getAttribute("transform"),n.forceCSS=t.setAttribute("transform","")||!Bi(Ti(t,mi)),k&&t.setAttribute("transform",k))),Math.abs(h)>90&&Math.abs(h)<270&&(R?(s*=-1,h+=l<=0?180:-180,l+=l<=0?180:-180):(a*=-1,h+=h<=0?180:-180)),e=e||n.uncache,n.x=i-((n.xPercent=i&&(!e&&n.xPercent||(Math.round(t.offsetWidth/2)===Math.round(-i)?-50:0)))?t.offsetWidth*n.xPercent/100:0)+z,n.y=r-((n.yPercent=r&&(!e&&n.yPercent||(Math.round(t.offsetHeight/2)===Math.round(-r)?-50:0)))?t.offsetHeight*n.yPercent/100:0)+z,n.z=o+z,n.scaleX=dt(s),n.scaleY=dt(a),n.rotation=dt(l)+F,n.rotationX=dt(c)+F,n.rotationY=dt(u)+F,n.skewX=h+F,n.skewY=f+F,n.transformPerspective=d+z,(n.zOrigin=parseFloat(H.split(" ")[2])||!e&&n.zOrigin||0)&&(I[vi]=Vi(H)),n.xOffset=n.yOffset=0,n.force3D=y.force3D,n.renderTransform=n.svg?er:Mn?tr:Gi,n.uncache=0,n},Vi=function(t){return(t=t.split(" "))[0]+" "+t[1]},Qi=function(t,e,n){var i=Gt(e);return dt(parseFloat(e)+parseFloat(Ni(t,"x",n+"px",i)))+i},Gi=function(t,e){e.z="0px",e.rotationY=e.rotationX="0deg",e.force3D=0,tr(t,e)},Ki="0deg",Ji="0px",Zi=") ",tr=function(t,e){var n=e||this,i=n.xPercent,r=n.yPercent,o=n.x,s=n.y,a=n.z,l=n.rotation,c=n.rotationY,u=n.rotationX,h=n.skewX,f=n.skewY,d=n.scaleX,p=n.scaleY,g=n.transformPerspective,m=n.force3D,v=n.target,y=n.zOrigin,_="",b="auto"===m&&t&&1!==t||!0===m;if(y&&(u!==Ki||c!==Ki)){var w,x=parseFloat(c)*Jn,T=Math.sin(x),E=Math.cos(x);x=parseFloat(u)*Jn,w=Math.cos(x),o=Qi(v,o,T*w*-y),s=Qi(v,s,-Math.sin(x)*-y),a=Qi(v,a,E*w*-y+y)}g!==Ji&&(_+="perspective("+g+Zi),(i||r)&&(_+="translate("+i+"%, "+r+"%) "),(b||o!==Ji||s!==Ji||a!==Ji)&&(_+=a!==Ji||b?"translate3d("+o+", "+s+", "+a+") ":"translate("+o+", "+s+Zi),l!==Ki&&(_+="rotate("+l+Zi),c!==Ki&&(_+="rotateY("+c+Zi),u!==Ki&&(_+="rotateX("+u+Zi),h===Ki&&f===Ki||(_+="skew("+h+", "+f+Zi),1===d&&1===p||(_+="scale("+d+", "+p+Zi),v.style[mi]=_||"translate(0, 0)"},er=function(t,e){var n,i,r,o,s,a=e||this,l=a.xPercent,c=a.yPercent,u=a.x,h=a.y,f=a.rotation,d=a.skewX,p=a.skewY,g=a.scaleX,m=a.scaleY,v=a.target,y=a.xOrigin,_=a.yOrigin,b=a.xOffset,w=a.yOffset,x=a.forceCSS,T=parseFloat(u),E=parseFloat(h);f=parseFloat(f),d=parseFloat(d),(p=parseFloat(p))&&(d+=p=parseFloat(p),f+=p),f||d?(f*=Jn,d*=Jn,n=Math.cos(f)*g,i=Math.sin(f)*g,r=Math.sin(f-d)*-m,o=Math.cos(f-d)*m,d&&(p*=Jn,s=Math.tan(d-p),r*=s=Math.sqrt(1+s*s),o*=s,p&&(s=Math.tan(p),n*=s=Math.sqrt(1+s*s),i*=s)),n=dt(n),i=dt(i),r=dt(r),o=dt(o)):(n=g,o=m,i=r=0),(T&&!~(u+"").indexOf("px")||E&&!~(h+"").indexOf("px"))&&(T=Ni(v,"x",u,"px"),E=Ni(v,"y",h,"px")),(y||_||b||w)&&(T=dt(T+y-(y*n+_*r)+b),E=dt(E+_-(y*i+_*o)+w)),(l||c)&&(s=v.getBBox(),T=dt(T+l/100*s.width),E=dt(E+c/100*s.height)),s="matrix("+n+","+i+","+r+","+o+","+T+","+E+")",v.setAttribute("transform",s),x&&(v.style[mi]=s)},nr=function(t,e,n,i,r){var o,s,a=360,l=A(r),c=parseFloat(r)*(l&&~r.indexOf("rad")?Kn:1)-i,u=i+c+"deg";return l&&("short"===(o=r.split("_")[1])&&(c%=a)!=c%180&&(c+=c<0?a:-360),"cw"===o&&c<0?c=(c+36e9)%a-~~(c/a)*a:"ccw"===o&&c>0&&(c=(c-36e9)%a-~~(c/a)*a)),t._pt=s=new un(t._pt,e,n,i,c,oi),s.e=u,s.u="deg",t._props.push(n),s},ir=function(t,e){for(var n in e)t[n]=e[n];return t},rr=function(t,e,n){var i,r,o,s,a,l,c,u=ir({},n._gsap),h=n.style;for(r in u.svg?(o=n.getAttribute("transform"),n.setAttribute("transform",""),h[mi]=e,i=Ui(n,1),Mi(n,mi),n.setAttribute("transform",o)):(o=getComputedStyle(n)[mi],h[mi]=e,i=Ui(n,1),h[mi]=o),Gn)(o=u[r])!==(s=i[r])&&"perspective,force3D,transformOrigin,svgOrigin".indexOf(r)<0&&(a=Gt(o)!==(c=Gt(s))?Ni(n,r,o,c):parseFloat(o),l=parseFloat(s),t._pt=new un(t._pt,i,r,a,l-a,ri),t._pt.u=c||0,t._props.push(r));ir(i,u)};ft("padding,margin,Width,Radius",(function(t,e){var n="Top",i="Right",r="Bottom",o="Left",s=(e<3?[n,i,r,o]:[n+o,n+i,r+i,r+o]).map((function(n){return e<2?t+n:"border"+n+t}));$i[e>1?"border"+t:t]=function(t,e,n,i,r){var o,a;if(arguments.length<4)return o=s.map((function(e){return Ii(t,e,n)})),5===(a=o.join(" ")).split(o[0]).length?o[0]:a;o=(i+"").split(" "),a={},s.forEach((function(t,e){return a[t]=o[e]=o[e]||o[(e-1)/2|0]})),t.init(e,a,r)}}));var or,sr,ar={name:"css",register:Si,targetTest:function(t){return t.style&&t.nodeType},init:function(t,e,n,i,r){var o,s,a,l,c,u,h,f,d,p,g,m,v,_,b,w,x,T,E,C,S=this._props,k=t.style,O=n.vars.startAt;for(h in kn||Si(),this.styles=this.styles||wi(t),w=this.styles.props,this.tween=n,e)if("autoRound"!==h&&(s=e[h],!rt[h]||!Be(h,e,n,i,t,r)))if(c=typeof s,u=$i[h],"function"===c&&(c=typeof(s=s.call(n,i,t,r))),"string"===c&&~s.indexOf("random(")&&(s=ae(s)),u)u(this,t,h,s,n)&&(b=1);else if("--"===h.substr(0,2))o=(getComputedStyle(t).getPropertyValue(h)+"").trim(),s+="",be.lastIndex=0,be.test(o)||(f=Gt(o),d=Gt(s)),d?f!==d&&(o=Ni(t,h,o,d)+d):f&&(s+=f),this.add(k,"setProperty",o,s,i,r,0,0,h),S.push(h),w.push(h,0,k[h]);else if("undefined"!==c){if(O&&h in O?(o="function"==typeof O[h]?O[h].call(n,i,t,r):O[h],A(o)&&~o.indexOf("random(")&&(o=ae(o)),Gt(o+"")||"auto"===o||(o+=y.units[h]||Gt(Ii(t,h))||""),"="===(o+"").charAt(1)&&(o=Ii(t,h))):o=Ii(t,h),l=parseFloat(o),(p="string"===c&&"="===s.charAt(1)&&s.substr(0,2))&&(s=s.substr(2)),a=parseFloat(s),h in ii&&("autoAlpha"===h&&(1===l&&"hidden"===Ii(t,"visibility")&&a&&(l=0),w.push("visibility",0,k.visibility),Pi(this,k,"visibility",l?"inherit":"hidden",a?"inherit":"hidden",!a)),"scale"!==h&&"transform"!==h&&~(h=ii[h]).indexOf(",")&&(h=h.split(",")[0])),g=h in Gn)if(this.styles.save(h),m||((v=t._gsap).renderTransform&&!e.parseTransform||Ui(t,e.parseTransform),_=!1!==e.smoothOrigin&&v.smooth,(m=this._pt=new un(this._pt,k,mi,0,1,v.renderTransform,v,0,-1)).dep=1),"scale"===h)this._pt=new un(this._pt,v,"scaleY",v.scaleY,(p?gt(v.scaleY,p+a):a)-v.scaleY||0,ri),this._pt.u=0,S.push("scaleY",h),h+="X";else{if("transformOrigin"===h){w.push(vi,0,k[vi]),T=void 0,E=void 0,C=void 0,E=(T=(x=s).split(" "))[0],C=T[1]||"50%","top"!==E&&"bottom"!==E&&"left"!==C&&"right"!==C||(x=E,E=C,C=x),T[0]=zi[E]||E,T[1]=zi[C]||C,s=T.join(" "),v.svg?Yi(t,s,0,_,0,this):((d=parseFloat(s.split(" ")[2])||0)!==v.zOrigin&&Pi(this,v,"zOrigin",v.zOrigin,d),Pi(this,k,h,Vi(o),Vi(s)));continue}if("svgOrigin"===h){Yi(t,s,1,_,0,this);continue}if(h in qi){nr(this,v,h,l,p?gt(l,p+s):s);continue}if("smoothOrigin"===h){Pi(this,v,"smooth",v.smooth,s);continue}if("force3D"===h){v[h]=s;continue}if("transform"===h){rr(this,s,t);continue}}else h in k||(h=Ci(h)||h);if(g||(a||0===a)&&(l||0===l)&&!ni.test(s)&&h in k)a||(a=0),(f=(o+"").substr((l+"").length))!==(d=Gt(s)||(h in y.units?y.units[h]:f))&&(l=Ni(t,h,o,d)),this._pt=new un(this._pt,g?v:k,h,l,(p?gt(l,p+a):a)-l,g||"px"!==d&&"zIndex"!==h||!1===e.autoRound?ri:ai),this._pt.u=d||0,f!==d&&"%"!==d&&(this._pt.b=o,this._pt.r=si);else if(h in k)Ri.call(this,t,h,o,p?p+s:s);else if(h in t)this.add(t,h,o||t[h],p?p+s:s,i,r);else if("parseTransform"!==h){V(h,s);continue}g||(h in k?w.push(h,0,k[h]):w.push(h,1,o||t[h])),S.push(h)}b&&cn(this)},render:function(t,e){if(e.tween._time||!Dn())for(var n=e._pt;n;)n.r(t,n.d),n=n._next;else e.styles.revert()},get:Ii,aliases:ii,getSetter:function(t,e,n){var i=ii[e];return i&&i.indexOf(",")<0&&(e=i),e in Gn&&e!==vi&&(t._gsap.x||Ii(t,"x"))?n&&On===n?"scale"===e?di:fi:(On=n||{})&&("scale"===e?pi:gi):t.style&&!M(t.style[e])?ui:~e.indexOf("-")?hi:tn(t,e)},core:{_removeProperty:Mi,_getMatrix:Xi}};Tn.utils.checkPrefix=Ci,Tn.core.getStyleSaver=wi,sr=ft("x,y,z,scale,scaleX,scaleY,xPercent,yPercent"+","+(or="rotation,rotationX,rotationY,skewX,skewY")+",transform,transformOrigin,svgOrigin,force3D,smoothOrigin,transformPerspective",(function(t){Gn[t]=1})),ft(or,(function(t){y.units[t]="deg",qi[t]=1})),ii[sr[13]]="x,y,z,scale,scaleX,scaleY,xPercent,yPercent,"+or,ft("0:translateX,1:translateY,2:translateZ,8:rotate,8:rotationZ,8:rotateZ,9:rotateX,10:rotateY",(function(t){var e=t.split(":");ii[e[1]]=sr[e[0]]})),ft("x,y,z,top,right,bottom,left,width,height,fontSize,padding,margin,perspective",(function(t){y.units[t]="px"})),Tn.registerPlugin(ar);var lr=Tn.registerPlugin(ar)||Tn,cr=lr.core.Tween;t.Back=Wn,t.Bounce=Yn,t.CSSPlugin=ar,t.Circ=Qn,t.Cubic=Fn,t.Elastic=Bn,t.Expo=Vn,t.Linear=Rn,t.Power0=Pn,t.Power1=Ln,t.Power2=jn,t.Power3=Nn,t.Power4=In,t.Quad=zn,t.Quart=$n,t.Quint=Hn,t.Sine=Un,t.SteppedEase=Xn,t.Strong=qn,t.TimelineLite=ze,t.TimelineMax=ze,t.TweenLite=Qe,t.TweenMax=cr,t.default=lr,t.gsap=lr,"undefined"==typeof window||window!==t?Object.defineProperty(t,"__esModule",{value:!0}):delete window.default})),function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t=t||self).window=t.window||{})}(this,(function(t){"use strict";function e(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}
/*!
   * Observer 3.12.5
   * https://gsap.com
   *
   * @license Copyright 2008-2024, GreenSock. All rights reserved.
   * Subject to the terms at https://gsap.com/standard-license or for
   * Club GSAP members, the agreement issued with that membership.
   * @author: Jack Doyle, <EMAIL>
  */var n,i,r,o,s,a,l,c,u,h,f,d,p,g=function(){return n||"undefined"!=typeof window&&(n=window.gsap)&&n.registerPlugin&&n},m=1,v=[],y=[],_=[],b=Date.now,w=function(t,e){return e},x=function(t,e){return~_.indexOf(t)&&_[_.indexOf(t)+1][e]},T=function(t){return!!~h.indexOf(t)},E=function(t,e,n,i,r){return t.addEventListener(e,n,{passive:!1!==i,capture:!!r})},C=function(t,e,n,i){return t.removeEventListener(e,n,!!i)},S="scrollLeft",k="scrollTop",A=function(){return f&&f.isPressed||y.cache++},O=function(t,e){var n=function n(i){if(i||0===i){m&&(r.history.scrollRestoration="manual");var o=f&&f.isPressed;i=n.v=Math.round(i)||(f&&f.iOS?1:0),t(i),n.cacheID=y.cache,o&&w("ss",i)}else(e||y.cache!==n.cacheID||w("ref"))&&(n.cacheID=y.cache,n.v=t());return n.v+n.offset};return n.offset=0,t&&n},D={s:S,p:"left",p2:"Left",os:"right",os2:"Right",d:"width",d2:"Width",a:"x",sc:O((function(t){return arguments.length?r.scrollTo(t,M.sc()):r.pageXOffset||o[S]||s[S]||a[S]||0}))},M={s:k,p:"top",p2:"Top",os:"bottom",os2:"Bottom",d:"height",d2:"Height",a:"y",op:D,sc:O((function(t){return arguments.length?r.scrollTo(D.sc(),t):r.pageYOffset||o[k]||s[k]||a[k]||0}))},P=function(t,e){return(e&&e._ctx&&e._ctx.selector||n.utils.toArray)(t)[0]||("string"==typeof t&&!1!==n.config().nullTargetWarn?console.warn("Element not found:",t):null)},L=function(t,e){var i=e.s,r=e.sc;T(t)&&(t=o.scrollingElement||s);var a=y.indexOf(t),l=r===M.sc?1:2;!~a&&(a=y.push(t)-1),y[a+l]||E(t,"scroll",A);var c=y[a+l],u=c||(y[a+l]=O(x(t,i),!0)||(T(t)?r:O((function(e){return arguments.length?t[i]=e:t[i]}))));return u.target=t,c||(u.smooth="smooth"===n.getProperty(t,"scrollBehavior")),u},j=function(t,e,n){var i=t,r=t,o=b(),s=o,a=e||50,l=Math.max(500,3*a),c=function(t,e){var l=b();e||l-o>a?(r=i,i=t,s=o,o=l):n?i+=t:i=r+(t-r)/(l-s)*(o-s)};return{update:c,reset:function(){r=i=n?0:i,s=o=0},getVelocity:function(t){var e=s,a=r,u=b();return(t||0===t)&&t!==i&&c(t),o===s||u-s>l?0:(i+(n?a:-a))/((n?u:o)-e)*1e3}}},N=function(t,e){return e&&!t._gsapAllow&&t.preventDefault(),t.changedTouches?t.changedTouches[0]:t},I=function(t){var e=Math.max.apply(Math,t),n=Math.min.apply(Math,t);return Math.abs(e)>=Math.abs(n)?e:n},R=function(){var t,e,i,r;(u=n.core.globals().ScrollTrigger)&&u.core&&(t=u.core,e=t.bridge||{},i=t._scrollers,r=t._proxies,i.push.apply(i,y),r.push.apply(r,_),y=i,_=r,w=function(t,n){return e[t](n)})},z=function(t){return n=t||g(),!i&&n&&"undefined"!=typeof document&&document.body&&(r=window,o=document,s=o.documentElement,a=o.body,h=[r,o,s,a],n.utils.clamp,p=n.core.context||function(){},c="onpointerenter"in a?"pointer":"mouse",l=F.isTouch=r.matchMedia&&r.matchMedia("(hover: none), (pointer: coarse)").matches?1:"ontouchstart"in r||navigator.maxTouchPoints>0||navigator.msMaxTouchPoints>0?2:0,d=F.eventTypes=("ontouchstart"in s?"touchstart,touchmove,touchcancel,touchend":"onpointerdown"in s?"pointerdown,pointermove,pointercancel,pointerup":"mousedown,mousemove,mouseup,mouseup").split(","),setTimeout((function(){return m=0}),500),R(),i=1),i};D.op=M,y.cache=0;var F=function(){function t(t){this.init(t)}var h,g;return t.prototype.init=function(t){i||z(n)||console.warn("Please gsap.registerPlugin(Observer)"),u||R();var e=t.tolerance,h=t.dragMinimum,g=t.type,m=t.target,y=t.lineHeight,_=t.debounce,w=t.preventDefault,x=t.onStop,S=t.onStopDelay,k=t.ignore,O=t.wheelSpeed,F=t.event,$=t.onDragStart,H=t.onDragEnd,q=t.onDrag,B=t.onPress,W=t.onRelease,X=t.onRight,Y=t.onLeft,U=t.onUp,V=t.onDown,Q=t.onChangeX,G=t.onChangeY,K=t.onChange,J=t.onToggleX,Z=t.onToggleY,tt=t.onHover,et=t.onHoverEnd,nt=t.onMove,it=t.ignoreCheck,rt=t.isNormalizer,ot=t.onGestureStart,st=t.onGestureEnd,at=t.onWheel,lt=t.onEnable,ct=t.onDisable,ut=t.onClick,ht=t.scrollSpeed,ft=t.capture,dt=t.allowClicks,pt=t.lockAxis,gt=t.onLockAxis;this.target=m=P(m)||s,this.vars=t,k&&(k=n.utils.toArray(k)),e=e||1e-9,h=h||0,O=O||1,ht=ht||1,g=g||"wheel,touch,pointer",_=!1!==_,y||(y=parseFloat(r.getComputedStyle(a).lineHeight)||22);var mt,vt,yt,_t,bt,wt,xt,Tt=this,Et=0,Ct=0,St=t.passive||!w,kt=L(m,D),At=L(m,M),Ot=kt(),Dt=At(),Mt=~g.indexOf("touch")&&!~g.indexOf("pointer")&&"pointerdown"===d[0],Pt=T(m),Lt=m.ownerDocument||o,jt=[0,0,0],Nt=[0,0,0],It=0,Rt=function(){return It=b()},zt=function(t,e){return(Tt.event=t)&&k&&~k.indexOf(t.target)||e&&Mt&&"touch"!==t.pointerType||it&&it(t,e)},Ft=function(){var t=Tt.deltaX=I(jt),n=Tt.deltaY=I(Nt),i=Math.abs(t)>=e,r=Math.abs(n)>=e;K&&(i||r)&&K(Tt,t,n,jt,Nt),i&&(X&&Tt.deltaX>0&&X(Tt),Y&&Tt.deltaX<0&&Y(Tt),Q&&Q(Tt),J&&Tt.deltaX<0!=Et<0&&J(Tt),Et=Tt.deltaX,jt[0]=jt[1]=jt[2]=0),r&&(V&&Tt.deltaY>0&&V(Tt),U&&Tt.deltaY<0&&U(Tt),G&&G(Tt),Z&&Tt.deltaY<0!=Ct<0&&Z(Tt),Ct=Tt.deltaY,Nt[0]=Nt[1]=Nt[2]=0),(_t||yt)&&(nt&&nt(Tt),yt&&(q(Tt),yt=!1),_t=!1),wt&&!(wt=!1)&&gt&&gt(Tt),bt&&(at(Tt),bt=!1),mt=0},$t=function(t,e,n){jt[n]+=t,Nt[n]+=e,Tt._vx.update(t),Tt._vy.update(e),_?mt||(mt=requestAnimationFrame(Ft)):Ft()},Ht=function(t,e){pt&&!xt&&(Tt.axis=xt=Math.abs(t)>Math.abs(e)?"x":"y",wt=!0),"y"!==xt&&(jt[2]+=t,Tt._vx.update(t,!0)),"x"!==xt&&(Nt[2]+=e,Tt._vy.update(e,!0)),_?mt||(mt=requestAnimationFrame(Ft)):Ft()},qt=function(t){if(!zt(t,1)){var e=(t=N(t,w)).clientX,n=t.clientY,i=e-Tt.x,r=n-Tt.y,o=Tt.isDragging;Tt.x=e,Tt.y=n,(o||Math.abs(Tt.startX-e)>=h||Math.abs(Tt.startY-n)>=h)&&(q&&(yt=!0),o||(Tt.isDragging=!0),Ht(i,r),o||$&&$(Tt))}},Bt=Tt.onPress=function(t){zt(t,1)||t&&t.button||(Tt.axis=xt=null,vt.pause(),Tt.isPressed=!0,t=N(t),Et=Ct=0,Tt.startX=Tt.x=t.clientX,Tt.startY=Tt.y=t.clientY,Tt._vx.reset(),Tt._vy.reset(),E(rt?m:Lt,d[1],qt,St,!0),Tt.deltaX=Tt.deltaY=0,B&&B(Tt))},Wt=Tt.onRelease=function(t){if(!zt(t,1)){C(rt?m:Lt,d[1],qt,!0);var e=!isNaN(Tt.y-Tt.startY),i=Tt.isDragging,o=i&&(Math.abs(Tt.x-Tt.startX)>3||Math.abs(Tt.y-Tt.startY)>3),s=N(t);!o&&e&&(Tt._vx.reset(),Tt._vy.reset(),w&&dt&&n.delayedCall(.08,(function(){if(b()-It>300&&!t.defaultPrevented)if(t.target.click)t.target.click();else if(Lt.createEvent){var e=Lt.createEvent("MouseEvents");e.initMouseEvent("click",!0,!0,r,1,s.screenX,s.screenY,s.clientX,s.clientY,!1,!1,!1,!1,0,null),t.target.dispatchEvent(e)}}))),Tt.isDragging=Tt.isGesturing=Tt.isPressed=!1,x&&i&&!rt&&vt.restart(!0),H&&i&&H(Tt),W&&W(Tt,o)}},Xt=function(t){return t.touches&&t.touches.length>1&&(Tt.isGesturing=!0)&&ot(t,Tt.isDragging)},Yt=function(){return(Tt.isGesturing=!1)||st(Tt)},Ut=function(t){if(!zt(t)){var e=kt(),n=At();$t((e-Ot)*ht,(n-Dt)*ht,1),Ot=e,Dt=n,x&&vt.restart(!0)}},Vt=function(t){if(!zt(t)){t=N(t,w),at&&(bt=!0);var e=(1===t.deltaMode?y:2===t.deltaMode?r.innerHeight:1)*O;$t(t.deltaX*e,t.deltaY*e,0),x&&!rt&&vt.restart(!0)}},Qt=function(t){if(!zt(t)){var e=t.clientX,n=t.clientY,i=e-Tt.x,r=n-Tt.y;Tt.x=e,Tt.y=n,_t=!0,x&&vt.restart(!0),(i||r)&&Ht(i,r)}},Gt=function(t){Tt.event=t,tt(Tt)},Kt=function(t){Tt.event=t,et(Tt)},Jt=function(t){return zt(t)||N(t,w)&&ut(Tt)};vt=Tt._dc=n.delayedCall(S||.25,(function(){Tt._vx.reset(),Tt._vy.reset(),vt.pause(),x&&x(Tt)})).pause(),Tt.deltaX=Tt.deltaY=0,Tt._vx=j(0,50,!0),Tt._vy=j(0,50,!0),Tt.scrollX=kt,Tt.scrollY=At,Tt.isDragging=Tt.isGesturing=Tt.isPressed=!1,p(this),Tt.enable=function(t){return Tt.isEnabled||(E(Pt?Lt:m,"scroll",A),g.indexOf("scroll")>=0&&E(Pt?Lt:m,"scroll",Ut,St,ft),g.indexOf("wheel")>=0&&E(m,"wheel",Vt,St,ft),(g.indexOf("touch")>=0&&l||g.indexOf("pointer")>=0)&&(E(m,d[0],Bt,St,ft),E(Lt,d[2],Wt),E(Lt,d[3],Wt),dt&&E(m,"click",Rt,!0,!0),ut&&E(m,"click",Jt),ot&&E(Lt,"gesturestart",Xt),st&&E(Lt,"gestureend",Yt),tt&&E(m,c+"enter",Gt),et&&E(m,c+"leave",Kt),nt&&E(m,c+"move",Qt)),Tt.isEnabled=!0,t&&t.type&&Bt(t),lt&&lt(Tt)),Tt},Tt.disable=function(){Tt.isEnabled&&(v.filter((function(t){return t!==Tt&&T(t.target)})).length||C(Pt?Lt:m,"scroll",A),Tt.isPressed&&(Tt._vx.reset(),Tt._vy.reset(),C(rt?m:Lt,d[1],qt,!0)),C(Pt?Lt:m,"scroll",Ut,ft),C(m,"wheel",Vt,ft),C(m,d[0],Bt,ft),C(Lt,d[2],Wt),C(Lt,d[3],Wt),C(m,"click",Rt,!0),C(m,"click",Jt),C(Lt,"gesturestart",Xt),C(Lt,"gestureend",Yt),C(m,c+"enter",Gt),C(m,c+"leave",Kt),C(m,c+"move",Qt),Tt.isEnabled=Tt.isPressed=Tt.isDragging=!1,ct&&ct(Tt))},Tt.kill=Tt.revert=function(){Tt.disable();var t=v.indexOf(Tt);t>=0&&v.splice(t,1),f===Tt&&(f=0)},v.push(Tt),rt&&T(m)&&(f=Tt),Tt.enable(F)},h=t,(g=[{key:"velocityX",get:function(){return this._vx.getVelocity()}},{key:"velocityY",get:function(){return this._vy.getVelocity()}}])&&e(h.prototype,g),t}();F.version="3.12.5",F.create=function(t){return new F(t)},F.register=z,F.getAll=function(){return v.slice()},F.getById=function(t){return v.filter((function(e){return e.vars.id===t}))[0]},g()&&n.registerPlugin(F);
/*!
   * ScrollTrigger 3.12.5
   * https://gsap.com
   *
   * @license Copyright 2008-2024, GreenSock. All rights reserved.
   * Subject to the terms at https://gsap.com/standard-license or for
   * Club GSAP members, the agreement issued with that membership.
   * @author: Jack Doyle, <EMAIL>
  */
var $,H,q,B,W,X,Y,U,V,Q,G,K,J,Z,tt,et,nt,it,rt,ot,st,at,lt,ct,ut,ht,ft,dt,pt,gt,mt,vt,yt,_t,bt,wt,xt,Tt,Et=1,Ct=Date.now,St=Ct(),kt=0,At=0,Ot=function(t,e,n){var i=Bt(t)&&("clamp("===t.substr(0,6)||t.indexOf("max")>-1);return n["_"+e+"Clamp"]=i,i?t.substr(6,t.length-7):t},Dt=function(t,e){return!e||Bt(t)&&"clamp("===t.substr(0,6)?t:"clamp("+t+")"},Mt=function t(){return At&&requestAnimationFrame(t)},Pt=function(){return Z=1},Lt=function(){return Z=0},jt=function(t){return t},Nt=function(t){return Math.round(1e5*t)/1e5||0},It=function(){return"undefined"!=typeof window},Rt=function(){return $||It()&&($=window.gsap)&&$.registerPlugin&&$},zt=function(t){return!!~Y.indexOf(t)},Ft=function(t){return("Height"===t?mt:q["inner"+t])||W["client"+t]||X["client"+t]},$t=function(t){return x(t,"getBoundingClientRect")||(zt(t)?function(){return Ze.width=q.innerWidth,Ze.height=mt,Ze}:function(){return fe(t)})},Ht=function(t,e){var n=e.s,i=e.d2,r=e.d,o=e.a;return Math.max(0,(n="scroll"+i)&&(o=x(t,n))?o()-$t(t)()[r]:zt(t)?(W[n]||X[n])-Ft(i):t[n]-t["offset"+i])},qt=function(t,e){for(var n=0;n<rt.length;n+=3)(!e||~e.indexOf(rt[n+1]))&&t(rt[n],rt[n+1],rt[n+2])},Bt=function(t){return"string"==typeof t},Wt=function(t){return"function"==typeof t},Xt=function(t){return"number"==typeof t},Yt=function(t){return"object"==typeof t},Ut=function(t,e,n){return t&&t.progress(e?0:1)&&n&&t.pause()},Vt=function(t,e){if(t.enabled){var n=t._ctx?t._ctx.add((function(){return e(t)})):e(t);n&&n.totalTime&&(t.callbackAnimation=n)}},Qt=Math.abs,Gt="left",Kt="right",Jt="bottom",Zt="width",te="height",ee="Right",ne="Left",ie="Top",re="Bottom",oe="padding",se="margin",ae="Width",le="Height",ce="px",ue=function(t){return q.getComputedStyle(t)},he=function(t,e){for(var n in e)n in t||(t[n]=e[n]);return t},fe=function(t,e){var n=e&&"matrix(1, 0, 0, 1, 0, 0)"!==ue(t)[tt]&&$.to(t,{x:0,y:0,xPercent:0,yPercent:0,rotation:0,rotationX:0,rotationY:0,scale:1,skewX:0,skewY:0}).progress(1),i=t.getBoundingClientRect();return n&&n.progress(0).kill(),i},de=function(t,e){var n=e.d2;return t["offset"+n]||t["client"+n]||0},pe=function(t){var e,n=[],i=t.labels,r=t.duration();for(e in i)n.push(i[e]/r);return n},ge=function(t){var e=$.utils.snap(t),n=Array.isArray(t)&&t.slice(0).sort((function(t,e){return t-e}));return n?function(t,i,r){var o;if(void 0===r&&(r=.001),!i)return e(t);if(i>0){for(t-=r,o=0;o<n.length;o++)if(n[o]>=t)return n[o];return n[o-1]}for(o=n.length,t+=r;o--;)if(n[o]<=t)return n[o];return n[0]}:function(n,i,r){void 0===r&&(r=.001);var o=e(n);return!i||Math.abs(o-n)<r||o-n<0==i<0?o:e(i<0?n-t:n+t)}},me=function(t,e,n,i){return n.split(",").forEach((function(n){return t(e,n,i)}))},ve=function(t,e,n,i,r){return t.addEventListener(e,n,{passive:!i,capture:!!r})},ye=function(t,e,n,i){return t.removeEventListener(e,n,!!i)},_e=function(t,e,n){(n=n&&n.wheelHandler)&&(t(e,"wheel",n),t(e,"touchmove",n))},be={startColor:"green",endColor:"red",indent:0,fontSize:"16px",fontWeight:"normal"},we={toggleActions:"play",anticipatePin:0},xe={top:0,left:0,center:.5,bottom:1,right:1},Te=function(t,e){if(Bt(t)){var n=t.indexOf("="),i=~n?+(t.charAt(n-1)+1)*parseFloat(t.substr(n+1)):0;~n&&(t.indexOf("%")>n&&(i*=e/100),t=t.substr(0,n-1)),t=i+(t in xe?xe[t]*e:~t.indexOf("%")?parseFloat(t)*e/100:parseFloat(t)||0)}return t},Ee=function(t,e,n,i,r,o,s,a){var l=r.startColor,c=r.endColor,u=r.fontSize,h=r.indent,f=r.fontWeight,d=B.createElement("div"),p=zt(n)||"fixed"===x(n,"pinType"),g=-1!==t.indexOf("scroller"),m=p?X:n,v=-1!==t.indexOf("start"),y=v?l:c,_="border-color:"+y+";font-size:"+u+";color:"+y+";font-weight:"+f+";pointer-events:none;white-space:nowrap;font-family:sans-serif,Arial;z-index:1000;padding:4px 8px;border-width:0;border-style:solid;";return _+="position:"+((g||a)&&p?"fixed;":"absolute;"),(g||a||!p)&&(_+=(i===M?Kt:Jt)+":"+(o+parseFloat(h))+"px;"),s&&(_+="box-sizing:border-box;text-align:left;width:"+s.offsetWidth+"px;"),d._isStart=v,d.setAttribute("class","gsap-marker-"+t+(e?" marker-"+e:"")),d.style.cssText=_,d.innerText=e||0===e?t+"-"+e:t,m.children[0]?m.insertBefore(d,m.children[0]):m.appendChild(d),d._offset=d["offset"+i.op.d2],Ce(d,0,i,v),d},Ce=function(t,e,n,i){var r={display:"block"},o=n[i?"os2":"p2"],s=n[i?"p2":"os2"];t._isFlipped=i,r[n.a+"Percent"]=i?-100:0,r[n.a]=i?"1px":0,r["border"+o+ae]=1,r["border"+s+ae]=0,r[n.p]=e+"px",$.set(t,r)},Se=[],ke={},Ae=function(){return Ct()-kt>34&&(bt||(bt=requestAnimationFrame(Ye)))},Oe=function(){(!lt||!lt.isPressed||lt.startX>X.clientWidth)&&(y.cache++,lt?bt||(bt=requestAnimationFrame(Ye)):Ye(),kt||Ne("scrollStart"),kt=Ct())},De=function(){ht=q.innerWidth,ut=q.innerHeight},Me=function(){y.cache++,!J&&!at&&!B.fullscreenElement&&!B.webkitFullscreenElement&&(!ct||ht!==q.innerWidth||Math.abs(q.innerHeight-ut)>.25*q.innerHeight)&&U.restart(!0)},Pe={},Le=[],je=function t(){return ye(an,"scrollEnd",t)||Be(!0)},Ne=function(t){return Pe[t]&&Pe[t].map((function(t){return t()}))||Le},Ie=[],Re=function(t){for(var e=0;e<Ie.length;e+=5)(!t||Ie[e+4]&&Ie[e+4].query===t)&&(Ie[e].style.cssText=Ie[e+1],Ie[e].getBBox&&Ie[e].setAttribute("transform",Ie[e+2]||""),Ie[e+3].uncache=1)},ze=function(t,e){var n;for(et=0;et<Se.length;et++)!(n=Se[et])||e&&n._ctx!==e||(t?n.kill(1):n.revert(!0,!0));vt=!0,e&&Re(e),e||Ne("revert")},Fe=function(t,e){y.cache++,(e||!wt)&&y.forEach((function(t){return Wt(t)&&t.cacheID++&&(t.rec=0)})),Bt(t)&&(q.history.scrollRestoration=pt=t)},$e=0,He=function(){X.appendChild(gt),mt=!lt&&gt.offsetHeight||q.innerHeight,X.removeChild(gt)},qe=function(t){return V(".gsap-marker-start, .gsap-marker-end, .gsap-marker-scroller-start, .gsap-marker-scroller-end").forEach((function(e){return e.style.display=t?"none":"block"}))},Be=function(t,e){if(!kt||t||vt){He(),wt=an.isRefreshing=!0,y.forEach((function(t){return Wt(t)&&++t.cacheID&&(t.rec=t())}));var n=Ne("refreshInit");ot&&an.sort(),e||ze(),y.forEach((function(t){Wt(t)&&(t.smooth&&(t.target.style.scrollBehavior="auto"),t(0))})),Se.slice(0).forEach((function(t){return t.refresh()})),vt=!1,Se.forEach((function(t){if(t._subPinOffset&&t.pin){var e=t.vars.horizontal?"offsetWidth":"offsetHeight",n=t.pin[e];t.revert(!0,1),t.adjustPinSpacing(t.pin[e]-n),t.refresh()}})),yt=1,qe(!0),Se.forEach((function(t){var e=Ht(t.scroller,t._dir),n="max"===t.vars.end||t._endClamp&&t.end>e,i=t._startClamp&&t.start>=e;(n||i)&&t.setPositions(i?e-1:t.start,n?Math.max(i?e:t.start+1,e):t.end,!0)})),qe(!1),yt=0,n.forEach((function(t){return t&&t.render&&t.render(-1)})),y.forEach((function(t){Wt(t)&&(t.smooth&&requestAnimationFrame((function(){return t.target.style.scrollBehavior="smooth"})),t.rec&&t(t.rec))})),Fe(pt,1),U.pause(),$e++,wt=2,Ye(2),Se.forEach((function(t){return Wt(t.vars.onRefresh)&&t.vars.onRefresh(t)})),wt=an.isRefreshing=!1,Ne("refresh")}else ve(an,"scrollEnd",je)},We=0,Xe=1,Ye=function(t){if(2===t||!wt&&!vt){an.isUpdating=!0,Tt&&Tt.update(0);var e=Se.length,n=Ct(),i=n-St>=50,r=e&&Se[0].scroll();if(Xe=We>r?-1:1,wt||(We=r),i&&(kt&&!Z&&n-kt>200&&(kt=0,Ne("scrollEnd")),G=St,St=n),Xe<0){for(et=e;et-- >0;)Se[et]&&Se[et].update(0,i);Xe=1}else for(et=0;et<e;et++)Se[et]&&Se[et].update(0,i);an.isUpdating=!1}bt=0},Ue=[Gt,"top",Jt,Kt,se+re,se+ee,se+ie,se+ne,"display","flexShrink","float","zIndex","gridColumnStart","gridColumnEnd","gridRowStart","gridRowEnd","gridArea","justifySelf","alignSelf","placeSelf","order"],Ve=Ue.concat([Zt,te,"boxSizing","max"+ae,"max"+le,"position",se,oe,oe+ie,oe+ee,oe+re,oe+ne]),Qe=function(t,e,n,i){if(!t._gsap.swappedIn){for(var r,o=Ue.length,s=e.style,a=t.style;o--;)s[r=Ue[o]]=n[r];s.position="absolute"===n.position?"absolute":"relative","inline"===n.display&&(s.display="inline-block"),a[Jt]=a[Kt]="auto",s.flexBasis=n.flexBasis||"auto",s.overflow="visible",s.boxSizing="border-box",s[Zt]=de(t,D)+ce,s[te]=de(t,M)+ce,s[oe]=a[se]=a.top=a[Gt]="0",Ke(i),a[Zt]=a["max"+ae]=n[Zt],a[te]=a["max"+le]=n[te],a[oe]=n[oe],t.parentNode!==e&&(t.parentNode.insertBefore(e,t),e.appendChild(t)),t._gsap.swappedIn=!0}},Ge=/([A-Z])/g,Ke=function(t){if(t){var e,n,i=t.t.style,r=t.length,o=0;for((t.t._gsap||$.core.getCache(t.t)).uncache=1;o<r;o+=2)n=t[o+1],e=t[o],n?i[e]=n:i[e]&&i.removeProperty(e.replace(Ge,"-$1").toLowerCase())}},Je=function(t){for(var e=Ve.length,n=t.style,i=[],r=0;r<e;r++)i.push(Ve[r],n[Ve[r]]);return i.t=t,i},Ze={left:0,top:0},tn=function(t,e,n,i,r,o,s,a,l,c,u,h,f,d){Wt(t)&&(t=t(a)),Bt(t)&&"max"===t.substr(0,3)&&(t=h+("="===t.charAt(4)?Te("0"+t.substr(3),n):0));var p,g,m,v=f?f.time():0;if(f&&f.seek(0),isNaN(t)||(t=+t),Xt(t))f&&(t=$.utils.mapRange(f.scrollTrigger.start,f.scrollTrigger.end,0,h,t)),s&&Ce(s,n,i,!0);else{Wt(e)&&(e=e(a));var y,_,b,w,x=(t||"0").split(" ");m=P(e,a)||X,(y=fe(m)||{})&&(y.left||y.top)||"none"!==ue(m).display||(w=m.style.display,m.style.display="block",y=fe(m),w?m.style.display=w:m.style.removeProperty("display")),_=Te(x[0],y[i.d]),b=Te(x[1]||"0",n),t=y[i.p]-l[i.p]-c+_+r-b,s&&Ce(s,b,i,n-b<20||s._isStart&&b>20),n-=n-b}if(d&&(a[d]=t||-.001,t<0&&(t=0)),o){var T=t+n,E=o._isStart;p="scroll"+i.d2,Ce(o,T,i,E&&T>20||!E&&(u?Math.max(X[p],W[p]):o.parentNode[p])<=T+1),u&&(l=fe(s),u&&(o.style[i.op.p]=l[i.op.p]-i.op.m-o._offset+ce))}return f&&m&&(p=fe(m),f.seek(h),g=fe(m),f._caScrollDist=p[i.p]-g[i.p],t=t/f._caScrollDist*h),f&&f.seek(v),f?t:Math.round(t)},en=/(webkit|moz|length|cssText|inset)/i,nn=function(t,e,n,i){if(t.parentNode!==e){var r,o,s=t.style;if(e===X){for(r in t._stOrig=s.cssText,o=ue(t))+r||en.test(r)||!o[r]||"string"!=typeof s[r]||"0"===r||(s[r]=o[r]);s.top=n,s.left=i}else s.cssText=t._stOrig;$.core.getCache(t).uncache=1,e.appendChild(t)}},rn=function(t,e,n){var i=e,r=i;return function(e){var o=Math.round(t());return o!==i&&o!==r&&Math.abs(o-i)>3&&Math.abs(o-r)>3&&(e=o,n&&n()),r=i,i=e,e}},on=function(t,e,n){var i={};i[e.p]="+="+n,$.set(t,i)},sn=function(t,e){var n=L(t,e),i="_scroll"+e.p2,r=function e(r,o,s,a,l){var c=e.tween,u=o.onComplete,h={};s=s||n();var f=rn(n,s,(function(){c.kill(),e.tween=0}));return l=a&&l||0,a=a||r-s,c&&c.kill(),o[i]=r,o.inherit=!1,o.modifiers=h,h[i]=function(){return f(s+a*c.ratio+l*c.ratio*c.ratio)},o.onUpdate=function(){y.cache++,e.tween&&Ye()},o.onComplete=function(){e.tween=0,u&&u.call(c)},c=e.tween=$.to(t,o)};return t[i]=n,n.wheelHandler=function(){return r.tween&&r.tween.kill()&&(r.tween=0)},ve(t,"wheel",n.wheelHandler),an.isTouch&&ve(t,"touchmove",n.wheelHandler),r},an=function(){function t(e,n){H||t.register($)||console.warn("Please gsap.registerPlugin(ScrollTrigger)"),dt(this),this.init(e,n)}return t.prototype.init=function(e,n){if(this.progress=this.start=0,this.vars&&this.kill(!0,!0),At){var i,r,o,s,a,l,c,u,h,f,d,p,g,m,v,b,w,T,E,C,S,k,A,O,j,N,I,R,z,F,H,Y,U,K,tt,nt,it,rt,at,lt,ct,ut,ht=e=he(Bt(e)||Xt(e)||e.nodeType?{trigger:e}:e,we),ft=ht.onUpdate,dt=ht.toggleClass,pt=ht.id,gt=ht.onToggle,mt=ht.onRefresh,vt=ht.scrub,bt=ht.trigger,St=ht.pin,Mt=ht.pinSpacing,Pt=ht.invalidateOnRefresh,Lt=ht.anticipatePin,It=ht.onScrubComplete,Rt=ht.onSnapComplete,qt=ht.once,Gt=ht.snap,Kt=ht.pinReparent,Jt=ht.pinSpacer,me=ht.containerAnimation,_e=ht.fastScrollEnd,xe=ht.preventOverlaps,Ce=e.horizontal||e.containerAnimation&&!1!==e.horizontal?D:M,Ae=!vt&&0!==vt,De=P(e.scroller||q),Pe=$.core.getCache(De),Le=zt(De),Ne="fixed"===("pinType"in e?e.pinType:x(De,"pinType")||Le&&"fixed"),Ie=[e.onEnter,e.onLeave,e.onEnterBack,e.onLeaveBack],Re=Ae&&e.toggleActions.split(" "),ze="markers"in e?e.markers:we.markers,Fe=Le?0:parseFloat(ue(De)["border"+Ce.p2+ae])||0,He=this,qe=e.onRefreshInit&&function(){return e.onRefreshInit(He)},We=function(t,e,n){var i=n.d,r=n.d2,o=n.a;return(o=x(t,"getBoundingClientRect"))?function(){return o()[i]}:function(){return(e?Ft(r):t["client"+r])||0}}(De,Le,Ce),Ye=function(t,e){return!e||~_.indexOf(t)?$t(t):function(){return Ze}}(De,Le),Ue=0,Ve=0,Ge=0,en=L(De,Ce);if(He._startClamp=He._endClamp=!1,He._dir=Ce,Lt*=45,He.scroller=De,He.scroll=me?me.time.bind(me):en,s=en(),He.vars=e,n=n||e.animation,"refreshPriority"in e&&(ot=1,-9999===e.refreshPriority&&(Tt=He)),Pe.tweenScroll=Pe.tweenScroll||{top:sn(De,M),left:sn(De,D)},He.tweenTo=i=Pe.tweenScroll[Ce.p],He.scrubDuration=function(t){(U=Xt(t)&&t)?Y?Y.duration(t):Y=$.to(n,{ease:"expo",totalProgress:"+=0",inherit:!1,duration:U,paused:!0,onComplete:function(){return It&&It(He)}}):(Y&&Y.progress(1).kill(),Y=0)},n&&(n.vars.lazy=!1,n._initted&&!He.isReverted||!1!==n.vars.immediateRender&&!1!==e.immediateRender&&n.duration()&&n.render(0,!0,!0),He.animation=n.pause(),n.scrollTrigger=He,He.scrubDuration(vt),F=0,pt||(pt=n.vars.id)),Gt&&(Yt(Gt)&&!Gt.push||(Gt={snapTo:Gt}),"scrollBehavior"in X.style&&$.set(Le?[X,W]:De,{scrollBehavior:"auto"}),y.forEach((function(t){return Wt(t)&&t.target===(Le?B.scrollingElement||W:De)&&(t.smooth=!1)})),o=Wt(Gt.snapTo)?Gt.snapTo:"labels"===Gt.snapTo?function(t){return function(e){return $.utils.snap(pe(t),e)}}(n):"labelsDirectional"===Gt.snapTo?(lt=n,function(t,e){return ge(pe(lt))(t,e.direction)}):!1!==Gt.directional?function(t,e){return ge(Gt.snapTo)(t,Ct()-Ve<500?0:e.direction)}:$.utils.snap(Gt.snapTo),K=Gt.duration||{min:.1,max:2},K=Yt(K)?Q(K.min,K.max):Q(K,K),tt=$.delayedCall(Gt.delay||U/2||.1,(function(){var t=en(),e=Ct()-Ve<500,r=i.tween;if(!(e||Math.abs(He.getVelocity())<10)||r||Z||Ue===t)He.isActive&&Ue!==t&&tt.restart(!0);else{var s,a,u=(t-l)/m,h=n&&!Ae?n.totalProgress():u,f=e?0:(h-H)/(Ct()-G)*1e3||0,d=$.utils.clamp(-u,1-u,Qt(f/2)*f/.185),p=u+(!1===Gt.inertia?0:d),g=Gt,v=g.onStart,y=g.onInterrupt,_=g.onComplete;if(s=o(p,He),Xt(s)||(s=p),a=Math.round(l+s*m),t<=c&&t>=l&&a!==t){if(r&&!r._initted&&r.data<=Qt(a-t))return;!1===Gt.inertia&&(d=s-u),i(a,{duration:K(Qt(.185*Math.max(Qt(p-h),Qt(s-h))/f/.05||0)),ease:Gt.ease||"power3",data:Qt(a-t),onInterrupt:function(){return tt.restart(!0)&&y&&y(He)},onComplete:function(){He.update(),Ue=en(),n&&(Y?Y.resetTo("totalProgress",s,n._tTime/n._tDur):n.progress(s)),F=H=n&&!Ae?n.totalProgress():He.progress,Rt&&Rt(He),_&&_(He)}},t,d*m,a-t-d*m),v&&v(He,i.tween)}}})).pause()),pt&&(ke[pt]=He),(at=(bt=He.trigger=P(bt||!0!==St&&St))&&bt._gsap&&bt._gsap.stRevert)&&(at=at(He)),St=!0===St?bt:P(St),Bt(dt)&&(dt={targets:bt,className:dt}),St&&(!1===Mt||Mt===se||(Mt=!(!Mt&&St.parentNode&&St.parentNode.style&&"flex"===ue(St.parentNode).display)&&oe),He.pin=St,(r=$.core.getCache(St)).spacer?v=r.pinState:(Jt&&((Jt=P(Jt))&&!Jt.nodeType&&(Jt=Jt.current||Jt.nativeElement),r.spacerIsNative=!!Jt,Jt&&(r.spacerState=Je(Jt))),r.spacer=T=Jt||B.createElement("div"),T.classList.add("pin-spacer"),pt&&T.classList.add("pin-spacer-"+pt),r.pinState=v=Je(St)),!1!==e.force3D&&$.set(St,{force3D:!0}),He.spacer=T=r.spacer,z=ue(St),O=z[Mt+Ce.os2],C=$.getProperty(St),S=$.quickSetter(St,Ce.a,ce),Qe(St,T,z),w=Je(St)),ze){p=Yt(ze)?he(ze,be):be,f=Ee("scroller-start",pt,De,Ce,p,0),d=Ee("scroller-end",pt,De,Ce,p,0,f),E=f["offset"+Ce.op.d2];var rn=P(x(De,"content")||De);u=this.markerStart=Ee("start",pt,rn,Ce,p,E,0,me),h=this.markerEnd=Ee("end",pt,rn,Ce,p,E,0,me),me&&(rt=$.quickSetter([u,h],Ce.a,ce)),Ne||_.length&&!0===x(De,"fixedMarkers")||(ut=ue(ct=Le?X:De).position,ct.style.position="absolute"===ut||"fixed"===ut?ut:"relative",$.set([f,d],{force3D:!0}),N=$.quickSetter(f,Ce.a,ce),R=$.quickSetter(d,Ce.a,ce))}if(me){var an=me.vars.onUpdate,ln=me.vars.onUpdateParams;me.eventCallback("onUpdate",(function(){He.update(0,0,1),an&&an.apply(me,ln||[])}))}if(He.previous=function(){return Se[Se.indexOf(He)-1]},He.next=function(){return Se[Se.indexOf(He)+1]},He.revert=function(t,e){if(!e)return He.kill(!0);var i=!1!==t||!He.enabled,r=J;i!==He.isReverted&&(i&&(nt=Math.max(en(),He.scroll.rec||0),Ge=He.progress,it=n&&n.progress()),u&&[u,h,f,d].forEach((function(t){return t.style.display=i?"none":"block"})),i&&(J=He,He.update(i)),!St||Kt&&He.isActive||(i?function(t,e,n){Ke(n);var i=t._gsap;if(i.spacerIsNative)Ke(i.spacerState);else if(t._gsap.swappedIn){var r=e.parentNode;r&&(r.insertBefore(t,e),r.removeChild(e))}t._gsap.swappedIn=!1}(St,T,v):Qe(St,T,ue(St),j)),i||He.update(i),J=r,He.isReverted=i)},He.refresh=function(r,o,p,y){if(!J&&He.enabled||o)if(St&&r&&kt)ve(t,"scrollEnd",je);else{!wt&&qe&&qe(He),J=He,i.tween&&!p&&(i.tween.kill(),i.tween=0),Y&&Y.pause(),Pt&&n&&n.revert({kill:!1}).invalidate(),He.isReverted||He.revert(!0,!0),He._subPinOffset=!1;var _,x,E,S,O,N,R,z,F,H,q,U,V,Q=We(),G=Ye(),K=me?me.duration():Ht(De,Ce),Z=m<=.01,et=0,rt=y||0,ot=Yt(p)?p.end:e.end,at=e.endTrigger||bt,lt=Yt(p)?p.start:e.start||(0!==e.start&&bt?St?"0 0":"0 100%":0),ct=He.pinnedContainer=e.pinnedContainer&&P(e.pinnedContainer,He),ut=bt&&Math.max(0,Se.indexOf(He))||0,ht=ut;for(ze&&Yt(p)&&(U=$.getProperty(f,Ce.p),V=$.getProperty(d,Ce.p));ht--;)(N=Se[ht]).end||N.refresh(0,1)||(J=He),!(R=N.pin)||R!==bt&&R!==St&&R!==ct||N.isReverted||(H||(H=[]),H.unshift(N),N.revert(!0,!0)),N!==Se[ht]&&(ut--,ht--);for(Wt(lt)&&(lt=lt(He)),lt=Ot(lt,"start",He),l=tn(lt,bt,Q,Ce,en(),u,f,He,G,Fe,Ne,K,me,He._startClamp&&"_startClamp")||(St?-.001:0),Wt(ot)&&(ot=ot(He)),Bt(ot)&&!ot.indexOf("+=")&&(~ot.indexOf(" ")?ot=(Bt(lt)?lt.split(" ")[0]:"")+ot:(et=Te(ot.substr(2),Q),ot=Bt(lt)?lt:(me?$.utils.mapRange(0,me.duration(),me.scrollTrigger.start,me.scrollTrigger.end,l):l)+et,at=bt)),ot=Ot(ot,"end",He),c=Math.max(l,tn(ot||(at?"100% 0":K),at,Q,Ce,en()+et,h,d,He,G,Fe,Ne,K,me,He._endClamp&&"_endClamp"))||-.001,et=0,ht=ut;ht--;)(R=(N=Se[ht]).pin)&&N.start-N._pinPush<=l&&!me&&N.end>0&&(_=N.end-(He._startClamp?Math.max(0,N.start):N.start),(R===bt&&N.start-N._pinPush<l||R===ct)&&isNaN(lt)&&(et+=_*(1-N.progress)),R===St&&(rt+=_));if(l+=et,c+=et,He._startClamp&&(He._startClamp+=et),He._endClamp&&!wt&&(He._endClamp=c||-.001,c=Math.min(c,Ht(De,Ce))),m=c-l||(l-=.01)&&.001,Z&&(Ge=$.utils.clamp(0,1,$.utils.normalize(l,c,nt))),He._pinPush=rt,u&&et&&((_={})[Ce.a]="+="+et,ct&&(_[Ce.p]="-="+en()),$.set([u,h],_)),!St||yt&&He.end>=Ht(De,Ce)){if(bt&&en()&&!me)for(x=bt.parentNode;x&&x!==X;)x._pinOffset&&(l-=x._pinOffset,c-=x._pinOffset),x=x.parentNode}else _=ue(St),S=Ce===M,E=en(),k=parseFloat(C(Ce.a))+rt,!K&&c>1&&(q={style:q=(Le?B.scrollingElement||W:De).style,value:q["overflow"+Ce.a.toUpperCase()]},Le&&"scroll"!==ue(X)["overflow"+Ce.a.toUpperCase()]&&(q.style["overflow"+Ce.a.toUpperCase()]="scroll")),Qe(St,T,_),w=Je(St),x=fe(St,!0),z=Ne&&L(De,S?D:M)(),Mt?((j=[Mt+Ce.os2,m+rt+ce]).t=T,(ht=Mt===oe?de(St,Ce)+m+rt:0)&&(j.push(Ce.d,ht+ce),"auto"!==T.style.flexBasis&&(T.style.flexBasis=ht+ce)),Ke(j),ct&&Se.forEach((function(t){t.pin===ct&&!1!==t.vars.pinSpacing&&(t._subPinOffset=!0)})),Ne&&en(nt)):(ht=de(St,Ce))&&"auto"!==T.style.flexBasis&&(T.style.flexBasis=ht+ce),Ne&&((O={top:x.top+(S?E-l:z)+ce,left:x.left+(S?z:E-l)+ce,boxSizing:"border-box",position:"fixed"})[Zt]=O["max"+ae]=Math.ceil(x.width)+ce,O[te]=O["max"+le]=Math.ceil(x.height)+ce,O[se]=O[se+ie]=O[se+ee]=O[se+re]=O[se+ne]="0",O[oe]=_[oe],O[oe+ie]=_[oe+ie],O[oe+ee]=_[oe+ee],O[oe+re]=_[oe+re],O[oe+ne]=_[oe+ne],b=function(t,e,n){for(var i,r=[],o=t.length,s=n?8:0;s<o;s+=2)i=t[s],r.push(i,i in e?e[i]:t[s+1]);return r.t=t.t,r}(v,O,Kt),wt&&en(0)),n?(F=n._initted,st(1),n.render(n.duration(),!0,!0),A=C(Ce.a)-k+m+rt,I=Math.abs(m-A)>1,Ne&&I&&b.splice(b.length-2,2),n.render(0,!0,!0),F||n.invalidate(!0),n.parent||n.totalTime(n.totalTime()),st(0)):A=m,q&&(q.value?q.style["overflow"+Ce.a.toUpperCase()]=q.value:q.style.removeProperty("overflow-"+Ce.a));H&&H.forEach((function(t){return t.revert(!1,!0)})),He.start=l,He.end=c,s=a=wt?nt:en(),me||wt||(s<nt&&en(nt),He.scroll.rec=0),He.revert(!1,!0),Ve=Ct(),tt&&(Ue=-1,tt.restart(!0)),J=0,n&&Ae&&(n._initted||it)&&n.progress()!==it&&n.progress(it||0,!0).render(n.time(),!0,!0),(Z||Ge!==He.progress||me||Pt)&&(n&&!Ae&&n.totalProgress(me&&l<-.001&&!Ge?$.utils.normalize(l,c,0):Ge,!0),He.progress=Z||(s-l)/m===Ge?0:Ge),St&&Mt&&(T._pinOffset=Math.round(He.progress*A)),Y&&Y.invalidate(),isNaN(U)||(U-=$.getProperty(f,Ce.p),V-=$.getProperty(d,Ce.p),on(f,Ce,U),on(u,Ce,U-(y||0)),on(d,Ce,V),on(h,Ce,V-(y||0))),Z&&!wt&&He.update(),!mt||wt||g||(g=!0,mt(He),g=!1)}},He.getVelocity=function(){return(en()-a)/(Ct()-G)*1e3||0},He.endAnimation=function(){Ut(He.callbackAnimation),n&&(Y?Y.progress(1):n.paused()?Ae||Ut(n,He.direction<0,1):Ut(n,n.reversed()))},He.labelToScroll=function(t){return n&&n.labels&&(l||He.refresh()||l)+n.labels[t]/n.duration()*m||0},He.getTrailing=function(t){var e=Se.indexOf(He),n=He.direction>0?Se.slice(0,e).reverse():Se.slice(e+1);return(Bt(t)?n.filter((function(e){return e.vars.preventOverlaps===t})):n).filter((function(t){return He.direction>0?t.end<=l:t.start>=c}))},He.update=function(t,e,r){if(!me||r||t){var o,u,h,d,p,g,v,y=!0===wt?nt:He.scroll(),_=t?0:(y-l)/m,x=_<0?0:_>1?1:_||0,E=He.progress;if(e&&(a=s,s=me?en():y,Gt&&(H=F,F=n&&!Ae?n.totalProgress():x)),Lt&&St&&!J&&!Et&&kt&&(!x&&l<y+(y-a)/(Ct()-G)*Lt?x=1e-4:1===x&&c>y+(y-a)/(Ct()-G)*Lt&&(x=.9999)),x!==E&&He.enabled){if(d=(p=(o=He.isActive=!!x&&x<1)!=(!!E&&E<1))||!!x!=!!E,He.direction=x>E?1:-1,He.progress=x,d&&!J&&(u=x&&!E?0:1===x?1:1===E?2:3,Ae&&(h=!p&&"none"!==Re[u+1]&&Re[u+1]||Re[u],v=n&&("complete"===h||"reset"===h||h in n))),xe&&(p||v)&&(v||vt||!n)&&(Wt(xe)?xe(He):He.getTrailing(xe).forEach((function(t){return t.endAnimation()}))),Ae||(!Y||J||Et?n&&n.totalProgress(x,!(!J||!Ve&&!t)):(Y._dp._time-Y._start!==Y._time&&Y.render(Y._dp._time-Y._start),Y.resetTo?Y.resetTo("totalProgress",x,n._tTime/n._tDur):(Y.vars.totalProgress=x,Y.invalidate().restart()))),St)if(t&&Mt&&(T.style[Mt+Ce.os2]=O),Ne){if(d){if(g=!t&&x>E&&c+1>y&&y+1>=Ht(De,Ce),Kt)if(t||!o&&!g)nn(St,T);else{var C=fe(St,!0),D=y-l;nn(St,X,C.top+(Ce===M?D:0)+ce,C.left+(Ce===M?0:D)+ce)}Ke(o||g?b:w),I&&x<1&&o||S(k+(1!==x||g?0:A))}}else S(Nt(k+A*x));Gt&&!i.tween&&!J&&!Et&&tt.restart(!0),dt&&(p||qt&&x&&(x<1||!_t))&&V(dt.targets).forEach((function(t){return t.classList[o||qt?"add":"remove"](dt.className)})),ft&&!Ae&&!t&&ft(He),d&&!J?(Ae&&(v&&("complete"===h?n.pause().totalProgress(1):"reset"===h?n.restart(!0).pause():"restart"===h?n.restart(!0):n[h]()),ft&&ft(He)),!p&&_t||(gt&&p&&Vt(He,gt),Ie[u]&&Vt(He,Ie[u]),qt&&(1===x?He.kill(!1,1):Ie[u]=0),p||Ie[u=1===x?1:3]&&Vt(He,Ie[u])),_e&&!o&&Math.abs(He.getVelocity())>(Xt(_e)?_e:2500)&&(Ut(He.callbackAnimation),Y?Y.progress(1):Ut(n,"reverse"===h?1:!x,1))):Ae&&ft&&!J&&ft(He)}if(R){var P=me?y/me.duration()*(me._caScrollDist||0):y;N(P+(f._isFlipped?1:0)),R(P)}rt&&rt(-y/me.duration()*(me._caScrollDist||0))}},He.enable=function(e,n){He.enabled||(He.enabled=!0,ve(De,"resize",Me),Le||ve(De,"scroll",Oe),qe&&ve(t,"refreshInit",qe),!1!==e&&(He.progress=Ge=0,s=a=Ue=en()),!1!==n&&He.refresh())},He.getTween=function(t){return t&&i?i.tween:Y},He.setPositions=function(t,e,n,i){if(me){var r=me.scrollTrigger,o=me.duration(),s=r.end-r.start;t=r.start+s*t/o,e=r.start+s*e/o}He.refresh(!1,!1,{start:Dt(t,n&&!!He._startClamp),end:Dt(e,n&&!!He._endClamp)},i),He.update()},He.adjustPinSpacing=function(t){if(j&&t){var e=j.indexOf(Ce.d)+1;j[e]=parseFloat(j[e])+t+ce,j[1]=parseFloat(j[1])+t+ce,Ke(j)}},He.disable=function(e,n){if(He.enabled&&(!1!==e&&He.revert(!0,!0),He.enabled=He.isActive=!1,n||Y&&Y.pause(),nt=0,r&&(r.uncache=1),qe&&ye(t,"refreshInit",qe),tt&&(tt.pause(),i.tween&&i.tween.kill()&&(i.tween=0)),!Le)){for(var o=Se.length;o--;)if(Se[o].scroller===De&&Se[o]!==He)return;ye(De,"resize",Me),Le||ye(De,"scroll",Oe)}},He.kill=function(t,i){He.disable(t,i),Y&&!i&&Y.kill(),pt&&delete ke[pt];var o=Se.indexOf(He);o>=0&&Se.splice(o,1),o===et&&Xe>0&&et--,o=0,Se.forEach((function(t){return t.scroller===He.scroller&&(o=1)})),o||wt||(He.scroll.rec=0),n&&(n.scrollTrigger=null,t&&n.revert({kill:!1}),i||n.kill()),u&&[u,h,f,d].forEach((function(t){return t.parentNode&&t.parentNode.removeChild(t)})),Tt===He&&(Tt=0),St&&(r&&(r.uncache=1),o=0,Se.forEach((function(t){return t.pin===St&&o++})),o||(r.spacer=0)),e.onKill&&e.onKill(He)},Se.push(He),He.enable(!1,!1),at&&at(He),n&&n.add&&!m){var cn=He.update;He.update=function(){He.update=cn,l||c||He.refresh()},$.delayedCall(.01,He.update),m=.01,l=c=0}else He.refresh();St&&function(){if(xt!==$e){var t=xt=$e;requestAnimationFrame((function(){return t===$e&&Be(!0)}))}}()}else this.update=this.refresh=this.kill=jt},t.register=function(e){return H||($=e||Rt(),It()&&window.document&&t.enable(),H=At),H},t.defaults=function(t){if(t)for(var e in t)we[e]=t[e];return we},t.disable=function(t,e){At=0,Se.forEach((function(n){return n[e?"kill":"disable"](t)})),ye(q,"wheel",Oe),ye(B,"scroll",Oe),clearInterval(K),ye(B,"touchcancel",jt),ye(X,"touchstart",jt),me(ye,B,"pointerdown,touchstart,mousedown",Pt),me(ye,B,"pointerup,touchend,mouseup",Lt),U.kill(),qt(ye);for(var n=0;n<y.length;n+=3)_e(ye,y[n],y[n+1]),_e(ye,y[n],y[n+2])},t.enable=function(){if(q=window,B=document,W=B.documentElement,X=B.body,$&&(V=$.utils.toArray,Q=$.utils.clamp,dt=$.core.context||jt,st=$.core.suppressOverwrites||jt,pt=q.history.scrollRestoration||"auto",We=q.pageYOffset,$.core.globals("ScrollTrigger",t),X)){At=1,(gt=document.createElement("div")).style.height="100vh",gt.style.position="absolute",He(),Mt(),F.register($),t.isTouch=F.isTouch,ft=F.isTouch&&/(iPad|iPhone|iPod|Mac)/g.test(navigator.userAgent),ct=1===F.isTouch,ve(q,"wheel",Oe),Y=[q,B,W,X],$.matchMedia?(t.matchMedia=function(t){var e,n=$.matchMedia();for(e in t)n.add(e,t[e]);return n},$.addEventListener("matchMediaInit",(function(){return ze()})),$.addEventListener("matchMediaRevert",(function(){return Re()})),$.addEventListener("matchMedia",(function(){Be(0,1),Ne("matchMedia")})),$.matchMedia("(orientation: portrait)",(function(){return De(),De}))):console.warn("Requires GSAP 3.11.0 or later"),De(),ve(B,"scroll",Oe);var e,n,i=X.style,r=i.borderTopStyle,o=$.core.Animation.prototype;for(o.revert||Object.defineProperty(o,"revert",{value:function(){return this.time(-.01,!0)}}),i.borderTopStyle="solid",e=fe(X),M.m=Math.round(e.top+M.sc())||0,D.m=Math.round(e.left+D.sc())||0,r?i.borderTopStyle=r:i.removeProperty("border-top-style"),K=setInterval(Ae,250),$.delayedCall(.5,(function(){return Et=0})),ve(B,"touchcancel",jt),ve(X,"touchstart",jt),me(ve,B,"pointerdown,touchstart,mousedown",Pt),me(ve,B,"pointerup,touchend,mouseup",Lt),tt=$.utils.checkPrefix("transform"),Ve.push(tt),H=Ct(),U=$.delayedCall(.2,Be).pause(),rt=[B,"visibilitychange",function(){var t=q.innerWidth,e=q.innerHeight;B.hidden?(nt=t,it=e):nt===t&&it===e||Me()},B,"DOMContentLoaded",Be,q,"load",Be,q,"resize",Me],qt(ve),Se.forEach((function(t){return t.enable(0,1)})),n=0;n<y.length;n+=3)_e(ye,y[n],y[n+1]),_e(ye,y[n],y[n+2])}},t.config=function(e){"limitCallbacks"in e&&(_t=!!e.limitCallbacks);var n=e.syncInterval;n&&clearInterval(K)||(K=n)&&setInterval(Ae,n),"ignoreMobileResize"in e&&(ct=1===t.isTouch&&e.ignoreMobileResize),"autoRefreshEvents"in e&&(qt(ye)||qt(ve,e.autoRefreshEvents||"none"),at=-1===(e.autoRefreshEvents+"").indexOf("resize"))},t.scrollerProxy=function(t,e){var n=P(t),i=y.indexOf(n),r=zt(n);~i&&y.splice(i,r?6:2),e&&(r?_.unshift(q,e,X,e,W,e):_.unshift(n,e))},t.clearMatchMedia=function(t){Se.forEach((function(e){return e._ctx&&e._ctx.query===t&&e._ctx.kill(!0,!0)}))},t.isInViewport=function(t,e,n){var i=(Bt(t)?P(t):t).getBoundingClientRect(),r=i[n?Zt:te]*e||0;return n?i.right-r>0&&i.left+r<q.innerWidth:i.bottom-r>0&&i.top+r<q.innerHeight},t.positionInViewport=function(t,e,n){Bt(t)&&(t=P(t));var i=t.getBoundingClientRect(),r=i[n?Zt:te],o=null==e?r/2:e in xe?xe[e]*r:~e.indexOf("%")?parseFloat(e)*r/100:parseFloat(e)||0;return n?(i.left+o)/q.innerWidth:(i.top+o)/q.innerHeight},t.killAll=function(t){if(Se.slice(0).forEach((function(t){return"ScrollSmoother"!==t.vars.id&&t.kill()})),!0!==t){var e=Pe.killAll||[];Pe={},e.forEach((function(t){return t()}))}},t}();an.version="3.12.5",an.saveStyles=function(t){return t?V(t).forEach((function(t){if(t&&t.style){var e=Ie.indexOf(t);e>=0&&Ie.splice(e,5),Ie.push(t,t.style.cssText,t.getBBox&&t.getAttribute("transform"),$.core.getCache(t),dt())}})):Ie},an.revert=function(t,e){return ze(!t,e)},an.create=function(t,e){return new an(t,e)},an.refresh=function(t){return t?Me():(H||an.register())&&Be(!0)},an.update=function(t){return++y.cache&&Ye(!0===t?2:0)},an.clearScrollMemory=Fe,an.maxScroll=function(t,e){return Ht(t,e?D:M)},an.getScrollFunc=function(t,e){return L(P(t),e?D:M)},an.getById=function(t){return ke[t]},an.getAll=function(){return Se.filter((function(t){return"ScrollSmoother"!==t.vars.id}))},an.isScrolling=function(){return!!kt},an.snapDirectional=ge,an.addEventListener=function(t,e){var n=Pe[t]||(Pe[t]=[]);~n.indexOf(e)||n.push(e)},an.removeEventListener=function(t,e){var n=Pe[t],i=n&&n.indexOf(e);i>=0&&n.splice(i,1)},an.batch=function(t,e){var n,i=[],r={},o=e.interval||.016,s=e.batchMax||1e9,a=function(t,e){var n=[],i=[],r=$.delayedCall(o,(function(){e(n,i),n=[],i=[]})).pause();return function(t){n.length||r.restart(!0),n.push(t.trigger),i.push(t),s<=n.length&&r.progress(1)}};for(n in e)r[n]="on"===n.substr(0,2)&&Wt(e[n])&&"onRefreshInit"!==n?a(0,e[n]):e[n];return Wt(s)&&(s=s(),ve(an,"refresh",(function(){return s=e.batchMax()}))),V(t).forEach((function(t){var e={};for(n in r)e[n]=r[n];e.trigger=t,i.push(an.create(e))})),i};var ln,cn=function(t,e,n,i){return e>i?t(i):e<0&&t(0),n>i?(i-e)/(n-e):n<0?e/(e-n):1},un=function t(e,n){!0===n?e.style.removeProperty("touch-action"):e.style.touchAction=!0===n?"auto":n?"pan-"+n+(F.isTouch?" pinch-zoom":""):"none",e===W&&t(X,n)},hn={auto:1,scroll:1},fn=function(t){var e,n=t.event,i=t.target,r=t.axis,o=(n.changedTouches?n.changedTouches[0]:n).target,s=o._gsap||$.core.getCache(o),a=Ct();if(!s._isScrollT||a-s._isScrollT>2e3){for(;o&&o!==X&&(o.scrollHeight<=o.clientHeight&&o.scrollWidth<=o.clientWidth||!hn[(e=ue(o)).overflowY]&&!hn[e.overflowX]);)o=o.parentNode;s._isScroll=o&&o!==i&&!zt(o)&&(hn[(e=ue(o)).overflowY]||hn[e.overflowX]),s._isScrollT=a}(s._isScroll||"x"===r)&&(n.stopPropagation(),n._gsapAllow=!0)},dn=function(t,e,n,i){return F.create({target:t,capture:!0,debounce:!1,lockAxis:!0,type:e,onWheel:i=i&&fn,onPress:i,onDrag:i,onScroll:i,onEnable:function(){return n&&ve(B,F.eventTypes[0],gn,!1,!0)},onDisable:function(){return ye(B,F.eventTypes[0],gn,!0)}})},pn=/(input|label|select|textarea)/i,gn=function(t){var e=pn.test(t.target.tagName);(e||ln)&&(t._gsapAllow=!0,ln=e)};an.sort=function(t){return Se.sort(t||function(t,e){return-1e6*(t.vars.refreshPriority||0)+t.start-(e.start+-1e6*(e.vars.refreshPriority||0))})},an.observe=function(t){return new F(t)},an.normalizeScroll=function(t){if(void 0===t)return lt;if(!0===t&&lt)return lt.enable();if(!1===t)return lt&&lt.kill(),void(lt=t);var e=t instanceof F?t:function(t){Yt(t)||(t={}),t.preventDefault=t.isNormalizer=t.allowClicks=!0,t.type||(t.type="wheel,touch"),t.debounce=!!t.debounce,t.id=t.id||"normalizer";var e,n,i,r,o,s,a,l,c=t,u=c.normalizeScrollX,h=c.momentum,f=c.allowNestedScroll,d=c.onRelease,p=P(t.target)||W,g=$.core.globals().ScrollSmoother,m=g&&g.get(),v=ft&&(t.content&&P(t.content)||m&&!1!==t.content&&!m.smooth()&&m.content()),_=L(p,M),b=L(p,D),w=1,x=(F.isTouch&&q.visualViewport?q.visualViewport.scale*q.visualViewport.width:q.outerWidth)/q.innerWidth,T=0,E=Wt(h)?function(){return h(e)}:function(){return h||2.8},C=dn(p,t.type,!0,f),S=function(){return r=!1},k=jt,A=jt,O=function(){n=Ht(p,M),A=Q(ft?1:0,n),u&&(k=Q(0,Ht(p,D))),i=$e},j=function(){v._gsap.y=Nt(parseFloat(v._gsap.y)+_.offset)+"px",v.style.transform="matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, "+parseFloat(v._gsap.y)+", 0, 1)",_.offset=_.cacheID=0},N=function(){O(),o.isActive()&&o.vars.scrollY>n&&(_()>n?o.progress(1)&&_(n):o.resetTo("scrollY",n))};return v&&$.set(v,{y:"+=0"}),t.ignoreCheck=function(t){return ft&&"touchmove"===t.type&&function(){if(r){requestAnimationFrame(S);var t=Nt(e.deltaY/2),n=A(_.v-t);if(v&&n!==_.v+_.offset){_.offset=n-_.v;var i=Nt((parseFloat(v&&v._gsap.y)||0)-_.offset);v.style.transform="matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, "+i+", 0, 1)",v._gsap.y=i+"px",_.cacheID=y.cache,Ye()}return!0}_.offset&&j(),r=!0}()||w>1.05&&"touchstart"!==t.type||e.isGesturing||t.touches&&t.touches.length>1},t.onPress=function(){r=!1;var t=w;w=Nt((q.visualViewport&&q.visualViewport.scale||1)/x),o.pause(),t!==w&&un(p,w>1.01||!u&&"x"),s=b(),a=_(),O(),i=$e},t.onRelease=t.onGestureStart=function(t,e){if(_.offset&&j(),e){y.cache++;var i,r,s=E();u&&(r=(i=b())+.05*s*-t.velocityX/.227,s*=cn(b,i,r,Ht(p,D)),o.vars.scrollX=k(r)),r=(i=_())+.05*s*-t.velocityY/.227,s*=cn(_,i,r,Ht(p,M)),o.vars.scrollY=A(r),o.invalidate().duration(s).play(.01),(ft&&o.vars.scrollY>=n||i>=n-1)&&$.to({},{onUpdate:N,duration:s})}else l.restart(!0);d&&d(t)},t.onWheel=function(){o._ts&&o.pause(),Ct()-T>1e3&&(i=0,T=Ct())},t.onChange=function(t,e,n,r,o){if($e!==i&&O(),e&&u&&b(k(r[2]===e?s+(t.startX-t.x):b()+e-r[1])),n){_.offset&&j();var l=o[2]===n,c=l?a+t.startY-t.y:_()+n-o[1],h=A(c);l&&c!==h&&(a+=h-c),_(h)}(n||e)&&Ye()},t.onEnable=function(){un(p,!u&&"x"),an.addEventListener("refresh",N),ve(q,"resize",N),_.smooth&&(_.target.style.scrollBehavior="auto",_.smooth=b.smooth=!1),C.enable()},t.onDisable=function(){un(p,!0),ye(q,"resize",N),an.removeEventListener("refresh",N),C.kill()},t.lockAxis=!1!==t.lockAxis,(e=new F(t)).iOS=ft,ft&&!_()&&_(1),ft&&$.ticker.add(jt),l=e._dc,o=$.to(e,{ease:"power4",paused:!0,inherit:!1,scrollX:u?"+=0.1":"+=0",scrollY:"+=0.1",modifiers:{scrollY:rn(_,_(),(function(){return o.pause()}))},onUpdate:Ye,onComplete:l.vars.onComplete}),e}(t);return lt&&lt.target===e.target&&lt.kill(),zt(e.target)&&(lt=e),e},an.core={_getVelocityProp:j,_inputObserver:dn,_scrollers:y,_proxies:_,bridge:{ss:function(){kt||Ne("scrollStart"),kt=Ct()},ref:function(){return J}}},Rt()&&$.registerPlugin(an),t.ScrollTrigger=an,t.default=an,"undefined"==typeof window||window!==t?Object.defineProperty(t,"__esModule",{value:!0}):delete window.default})),function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t=t||self).window=t.window||{})}(this,(function(t){"use strict";
/*!
	 * ScrollToPlugin 3.12.5
	 * https://gsap.com
	 *
	 * @license Copyright 2008-2024, GreenSock. All rights reserved.
	 * Subject to the terms at https://gsap.com/standard-license or for
	 * Club GSAP members, the agreement issued with that membership.
	 * @author: Jack Doyle, <EMAIL>
	*/var e,n,i,r,o,s,a,l,c=function(){return"undefined"!=typeof window},u=function(){return e||c()&&(e=window.gsap)&&e.registerPlugin&&e},h=function(t){return"string"==typeof t},f=function(t){return"function"==typeof t},d=function(t,e){var n="x"===e?"Width":"Height",s="scroll"+n,a="client"+n;return t===i||t===r||t===o?Math.max(r[s],o[s])-(i["inner"+n]||r[a]||o[a]):t[s]-t["offset"+n]},p=function(t,e){var n="scroll"+("x"===e?"Left":"Top");return t===i&&(null!=t.pageXOffset?n="page"+e.toUpperCase()+"Offset":t=null!=r[n]?r:o),function(){return t[n]}},g=function(t,e){if(!(t=s(t)[0])||!t.getBoundingClientRect)return console.warn("scrollTo target doesn't exist. Using 0")||{x:0,y:0};var n=t.getBoundingClientRect(),a=!e||e===i||e===o,l=a?{top:r.clientTop-(i.pageYOffset||r.scrollTop||o.scrollTop||0),left:r.clientLeft-(i.pageXOffset||r.scrollLeft||o.scrollLeft||0)}:e.getBoundingClientRect(),c={x:n.left-l.left,y:n.top-l.top};return!a&&e&&(c.x+=p(e,"x")(),c.y+=p(e,"y")()),c},m=function(t,e,n,i,r){return isNaN(t)||"object"==typeof t?h(t)&&"="===t.charAt(1)?parseFloat(t.substr(2))*("-"===t.charAt(0)?-1:1)+i-r:"max"===t?d(e,n)-r:Math.min(d(e,n),g(t,e)[n]-r):parseFloat(t)-r},v=function(){e=u(),c()&&e&&"undefined"!=typeof document&&document.body&&(i=window,o=document.body,r=document.documentElement,s=e.utils.toArray,e.config({autoKillThreshold:7}),a=e.config(),n=1)},y={version:"3.12.5",name:"scrollTo",rawVars:1,register:function(t){e=t,v()},init:function(t,r,o,s,a){n||v();var c=this,u=e.getProperty(t,"scrollSnapType");c.isWin=t===i,c.target=t,c.tween=o,r=function(t,e,n,i){if(f(t)&&(t=t(e,n,i)),"object"!=typeof t)return h(t)&&"max"!==t&&"="!==t.charAt(1)?{x:t,y:t}:{y:t};if(t.nodeType)return{y:t,x:t};var r,o={};for(r in t)o[r]="onAutoKill"!==r&&f(t[r])?t[r](e,n,i):t[r];return o}(r,s,t,a),c.vars=r,c.autoKill=!!r.autoKill,c.getX=p(t,"x"),c.getY=p(t,"y"),c.x=c.xPrev=c.getX(),c.y=c.yPrev=c.getY(),l||(l=e.core.globals().ScrollTrigger),"smooth"===e.getProperty(t,"scrollBehavior")&&e.set(t,{scrollBehavior:"auto"}),u&&"none"!==u&&(c.snap=1,c.snapInline=t.style.scrollSnapType,t.style.scrollSnapType="none"),null!=r.x?(c.add(c,"x",c.x,m(r.x,t,"x",c.x,r.offsetX||0),s,a),c._props.push("scrollTo_x")):c.skipX=1,null!=r.y?(c.add(c,"y",c.y,m(r.y,t,"y",c.y,r.offsetY||0),s,a),c._props.push("scrollTo_y")):c.skipY=1},render:function(t,e){for(var n,r,o,s,c,u=e._pt,h=e.target,f=e.tween,p=e.autoKill,g=e.xPrev,m=e.yPrev,v=e.isWin,y=e.snap,_=e.snapInline;u;)u.r(t,u.d),u=u._next;n=v||!e.skipX?e.getX():g,o=(r=v||!e.skipY?e.getY():m)-m,s=n-g,c=a.autoKillThreshold,e.x<0&&(e.x=0),e.y<0&&(e.y=0),p&&(!e.skipX&&(s>c||s<-c)&&n<d(h,"x")&&(e.skipX=1),!e.skipY&&(o>c||o<-c)&&r<d(h,"y")&&(e.skipY=1),e.skipX&&e.skipY&&(f.kill(),e.vars.onAutoKill&&e.vars.onAutoKill.apply(f,e.vars.onAutoKillParams||[]))),v?i.scrollTo(e.skipX?n:e.x,e.skipY?r:e.y):(e.skipY||(h.scrollTop=e.y),e.skipX||(h.scrollLeft=e.x)),!y||1!==t&&0!==t||(r=h.scrollTop,n=h.scrollLeft,_?h.style.scrollSnapType=_:h.style.removeProperty("scroll-snap-type"),h.scrollTop=r+1,h.scrollLeft=n+1,h.scrollTop=r,h.scrollLeft=n),e.xPrev=e.x,e.yPrev=e.y,l&&l.update()},kill:function(t){var e="scrollTo"===t,n=this._props.indexOf(t);return(e||"scrollTo_x"===t)&&(this.skipX=1),(e||"scrollTo_y"===t)&&(this.skipY=1),n>-1&&this._props.splice(n,1),!this._props.length}};y.max=d,y.getOffset=g,y.buildGetter=p,u()&&e.registerPlugin(y),t.ScrollToPlugin=y,t.default=y,Object.defineProperty(t,"__esModule",{value:!0})})),
/*!
  * Bootstrap v5.3.2 (https://getbootstrap.com/)
  * Copyright 2011-2023 The Bootstrap Authors (https://github.com/twbs/bootstrap/graphs/contributors)
  * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)
  */
function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t="undefined"!=typeof globalThis?globalThis:t||self).bootstrap=e()}(this,(function(){"use strict";const t=new Map,e={set(e,n,i){t.has(e)||t.set(e,new Map);const r=t.get(e);r.has(n)||0===r.size?r.set(n,i):console.error(`Bootstrap doesn't allow more than one instance per element. Bound instance: ${Array.from(r.keys())[0]}.`)},get:(e,n)=>t.has(e)&&t.get(e).get(n)||null,remove(e,n){if(!t.has(e))return;const i=t.get(e);i.delete(n),0===i.size&&t.delete(e)}},n="transitionend",i=t=>(t&&window.CSS&&window.CSS.escape&&(t=t.replace(/#([^\s"#']+)/g,((t,e)=>`#${CSS.escape(e)}`))),t),r=t=>{t.dispatchEvent(new Event(n))},o=t=>!(!t||"object"!=typeof t)&&(void 0!==t.jquery&&(t=t[0]),void 0!==t.nodeType),s=t=>o(t)?t.jquery?t[0]:t:"string"==typeof t&&t.length>0?document.querySelector(i(t)):null,a=t=>{if(!o(t)||0===t.getClientRects().length)return!1;const e="visible"===getComputedStyle(t).getPropertyValue("visibility"),n=t.closest("details:not([open])");if(!n)return e;if(n!==t){const e=t.closest("summary");if(e&&e.parentNode!==n)return!1;if(null===e)return!1}return e},l=t=>!t||t.nodeType!==Node.ELEMENT_NODE||!!t.classList.contains("disabled")||(void 0!==t.disabled?t.disabled:t.hasAttribute("disabled")&&"false"!==t.getAttribute("disabled")),c=t=>{if(!document.documentElement.attachShadow)return null;if("function"==typeof t.getRootNode){const e=t.getRootNode();return e instanceof ShadowRoot?e:null}return t instanceof ShadowRoot?t:t.parentNode?c(t.parentNode):null},u=()=>{},h=t=>{t.offsetHeight},f=()=>window.jQuery&&!document.body.hasAttribute("data-bs-no-jquery")?window.jQuery:null,d=[],p=()=>"rtl"===document.documentElement.dir,g=t=>{var e;e=()=>{const e=f();if(e){const n=t.NAME,i=e.fn[n];e.fn[n]=t.jQueryInterface,e.fn[n].Constructor=t,e.fn[n].noConflict=()=>(e.fn[n]=i,t.jQueryInterface)}},"loading"===document.readyState?(d.length||document.addEventListener("DOMContentLoaded",(()=>{for(const t of d)t()})),d.push(e)):e()},m=(t,e=[],n=t)=>"function"==typeof t?t(...e):n,v=(t,e,i=!0)=>{if(!i)return void m(t);const o=(t=>{if(!t)return 0;let{transitionDuration:e,transitionDelay:n}=window.getComputedStyle(t);const i=Number.parseFloat(e),r=Number.parseFloat(n);return i||r?(e=e.split(",")[0],n=n.split(",")[0],1e3*(Number.parseFloat(e)+Number.parseFloat(n))):0})(e)+5;let s=!1;const a=({target:i})=>{i===e&&(s=!0,e.removeEventListener(n,a),m(t))};e.addEventListener(n,a),setTimeout((()=>{s||r(e)}),o)},y=(t,e,n,i)=>{const r=t.length;let o=t.indexOf(e);return-1===o?!n&&i?t[r-1]:t[0]:(o+=n?1:-1,i&&(o=(o+r)%r),t[Math.max(0,Math.min(o,r-1))])},_=/[^.]*(?=\..*)\.|.*/,b=/\..*/,w=/::\d+$/,x={};let T=1;const E={mouseenter:"mouseover",mouseleave:"mouseout"},C=new Set(["click","dblclick","mouseup","mousedown","contextmenu","mousewheel","DOMMouseScroll","mouseover","mouseout","mousemove","selectstart","selectend","keydown","keypress","keyup","orientationchange","touchstart","touchmove","touchend","touchcancel","pointerdown","pointermove","pointerup","pointerleave","pointercancel","gesturestart","gesturechange","gestureend","focus","blur","change","reset","select","submit","focusin","focusout","load","unload","beforeunload","resize","move","DOMContentLoaded","readystatechange","error","abort","scroll"]);function S(t,e){return e&&`${e}::${T++}`||t.uidEvent||T++}function k(t){const e=S(t);return t.uidEvent=e,x[e]=x[e]||{},x[e]}function A(t,e,n=null){return Object.values(t).find((t=>t.callable===e&&t.delegationSelector===n))}function O(t,e,n){const i="string"==typeof e,r=i?n:e||n;let o=L(t);return C.has(o)||(o=t),[i,r,o]}function D(t,e,n,i,r){if("string"!=typeof e||!t)return;let[o,s,a]=O(e,n,i);if(e in E){const t=t=>function(e){if(!e.relatedTarget||e.relatedTarget!==e.delegateTarget&&!e.delegateTarget.contains(e.relatedTarget))return t.call(this,e)};s=t(s)}const l=k(t),c=l[a]||(l[a]={}),u=A(c,s,o?n:null);if(u)return void(u.oneOff=u.oneOff&&r);const h=S(s,e.replace(_,"")),f=o?function(t,e,n){return function i(r){const o=t.querySelectorAll(e);for(let{target:s}=r;s&&s!==this;s=s.parentNode)for(const a of o)if(a===s)return N(r,{delegateTarget:s}),i.oneOff&&j.off(t,r.type,e,n),n.apply(s,[r])}}(t,n,s):function(t,e){return function n(i){return N(i,{delegateTarget:t}),n.oneOff&&j.off(t,i.type,e),e.apply(t,[i])}}(t,s);f.delegationSelector=o?n:null,f.callable=s,f.oneOff=r,f.uidEvent=h,c[h]=f,t.addEventListener(a,f,o)}function M(t,e,n,i,r){const o=A(e[n],i,r);o&&(t.removeEventListener(n,o,Boolean(r)),delete e[n][o.uidEvent])}function P(t,e,n,i){const r=e[n]||{};for(const[o,s]of Object.entries(r))o.includes(i)&&M(t,e,n,s.callable,s.delegationSelector)}function L(t){return t=t.replace(b,""),E[t]||t}const j={on(t,e,n,i){D(t,e,n,i,!1)},one(t,e,n,i){D(t,e,n,i,!0)},off(t,e,n,i){if("string"!=typeof e||!t)return;const[r,o,s]=O(e,n,i),a=s!==e,l=k(t),c=l[s]||{},u=e.startsWith(".");if(void 0===o){if(u)for(const n of Object.keys(l))P(t,l,n,e.slice(1));for(const[n,i]of Object.entries(c)){const r=n.replace(w,"");a&&!e.includes(r)||M(t,l,s,i.callable,i.delegationSelector)}}else{if(!Object.keys(c).length)return;M(t,l,s,o,r?n:null)}},trigger(t,e,n){if("string"!=typeof e||!t)return null;const i=f();let r=null,o=!0,s=!0,a=!1;e!==L(e)&&i&&(r=i.Event(e,n),i(t).trigger(r),o=!r.isPropagationStopped(),s=!r.isImmediatePropagationStopped(),a=r.isDefaultPrevented());const l=N(new Event(e,{bubbles:o,cancelable:!0}),n);return a&&l.preventDefault(),s&&t.dispatchEvent(l),l.defaultPrevented&&r&&r.preventDefault(),l}};function N(t,e={}){for(const[n,i]of Object.entries(e))try{t[n]=i}catch(e){Object.defineProperty(t,n,{configurable:!0,get:()=>i})}return t}function I(t){if("true"===t)return!0;if("false"===t)return!1;if(t===Number(t).toString())return Number(t);if(""===t||"null"===t)return null;if("string"!=typeof t)return t;try{return JSON.parse(decodeURIComponent(t))}catch(e){return t}}function R(t){return t.replace(/[A-Z]/g,(t=>`-${t.toLowerCase()}`))}const z={setDataAttribute(t,e,n){t.setAttribute(`data-bs-${R(e)}`,n)},removeDataAttribute(t,e){t.removeAttribute(`data-bs-${R(e)}`)},getDataAttributes(t){if(!t)return{};const e={},n=Object.keys(t.dataset).filter((t=>t.startsWith("bs")&&!t.startsWith("bsConfig")));for(const i of n){let n=i.replace(/^bs/,"");n=n.charAt(0).toLowerCase()+n.slice(1,n.length),e[n]=I(t.dataset[i])}return e},getDataAttribute:(t,e)=>I(t.getAttribute(`data-bs-${R(e)}`))};class F{static get Default(){return{}}static get DefaultType(){return{}}static get NAME(){throw new Error('You have to implement the static method "NAME", for each component!')}_getConfig(t){return t=this._mergeConfigObj(t),t=this._configAfterMerge(t),this._typeCheckConfig(t),t}_configAfterMerge(t){return t}_mergeConfigObj(t,e){const n=o(e)?z.getDataAttribute(e,"config"):{};return{...this.constructor.Default,..."object"==typeof n?n:{},...o(e)?z.getDataAttributes(e):{},..."object"==typeof t?t:{}}}_typeCheckConfig(t,e=this.constructor.DefaultType){for(const[i,r]of Object.entries(e)){const e=t[i],s=o(e)?"element":null==(n=e)?`${n}`:Object.prototype.toString.call(n).match(/\s([a-z]+)/i)[1].toLowerCase();if(!new RegExp(r).test(s))throw new TypeError(`${this.constructor.NAME.toUpperCase()}: Option "${i}" provided type "${s}" but expected type "${r}".`)}var n}}class $ extends F{constructor(t,n){super(),(t=s(t))&&(this._element=t,this._config=this._getConfig(n),e.set(this._element,this.constructor.DATA_KEY,this))}dispose(){e.remove(this._element,this.constructor.DATA_KEY),j.off(this._element,this.constructor.EVENT_KEY);for(const t of Object.getOwnPropertyNames(this))this[t]=null}_queueCallback(t,e,n=!0){v(t,e,n)}_getConfig(t){return t=this._mergeConfigObj(t,this._element),t=this._configAfterMerge(t),this._typeCheckConfig(t),t}static getInstance(t){return e.get(s(t),this.DATA_KEY)}static getOrCreateInstance(t,e={}){return this.getInstance(t)||new this(t,"object"==typeof e?e:null)}static get VERSION(){return"5.3.2"}static get DATA_KEY(){return`bs.${this.NAME}`}static get EVENT_KEY(){return`.${this.DATA_KEY}`}static eventName(t){return`${t}${this.EVENT_KEY}`}}const H=t=>{let e=t.getAttribute("data-bs-target");if(!e||"#"===e){let n=t.getAttribute("href");if(!n||!n.includes("#")&&!n.startsWith("."))return null;n.includes("#")&&!n.startsWith("#")&&(n=`#${n.split("#")[1]}`),e=n&&"#"!==n?i(n.trim()):null}return e},q={find:(t,e=document.documentElement)=>[].concat(...Element.prototype.querySelectorAll.call(e,t)),findOne:(t,e=document.documentElement)=>Element.prototype.querySelector.call(e,t),children:(t,e)=>[].concat(...t.children).filter((t=>t.matches(e))),parents(t,e){const n=[];let i=t.parentNode.closest(e);for(;i;)n.push(i),i=i.parentNode.closest(e);return n},prev(t,e){let n=t.previousElementSibling;for(;n;){if(n.matches(e))return[n];n=n.previousElementSibling}return[]},next(t,e){let n=t.nextElementSibling;for(;n;){if(n.matches(e))return[n];n=n.nextElementSibling}return[]},focusableChildren(t){const e=["a","button","input","textarea","select","details","[tabindex]",'[contenteditable="true"]'].map((t=>`${t}:not([tabindex^="-"])`)).join(",");return this.find(e,t).filter((t=>!l(t)&&a(t)))},getSelectorFromElement(t){const e=H(t);return e&&q.findOne(e)?e:null},getElementFromSelector(t){const e=H(t);return e?q.findOne(e):null},getMultipleElementsFromSelector(t){const e=H(t);return e?q.find(e):[]}},B=(t,e="hide")=>{const n=`click.dismiss${t.EVENT_KEY}`,i=t.NAME;j.on(document,n,`[data-bs-dismiss="${i}"]`,(function(n){if(["A","AREA"].includes(this.tagName)&&n.preventDefault(),l(this))return;const r=q.getElementFromSelector(this)||this.closest(`.${i}`);t.getOrCreateInstance(r)[e]()}))},W=".bs.alert",X=`close${W}`,Y=`closed${W}`;class U extends ${static get NAME(){return"alert"}close(){if(j.trigger(this._element,X).defaultPrevented)return;this._element.classList.remove("show");const t=this._element.classList.contains("fade");this._queueCallback((()=>this._destroyElement()),this._element,t)}_destroyElement(){this._element.remove(),j.trigger(this._element,Y),this.dispose()}static jQueryInterface(t){return this.each((function(){const e=U.getOrCreateInstance(this);if("string"==typeof t){if(void 0===e[t]||t.startsWith("_")||"constructor"===t)throw new TypeError(`No method named "${t}"`);e[t](this)}}))}}B(U,"close"),g(U);const V='[data-bs-toggle="button"]';class Q extends ${static get NAME(){return"button"}toggle(){this._element.setAttribute("aria-pressed",this._element.classList.toggle("active"))}static jQueryInterface(t){return this.each((function(){const e=Q.getOrCreateInstance(this);"toggle"===t&&e[t]()}))}}j.on(document,"click.bs.button.data-api",V,(t=>{t.preventDefault();const e=t.target.closest(V);Q.getOrCreateInstance(e).toggle()})),g(Q);const G=".bs.swipe",K=`touchstart${G}`,J=`touchmove${G}`,Z=`touchend${G}`,tt=`pointerdown${G}`,et=`pointerup${G}`,nt={endCallback:null,leftCallback:null,rightCallback:null},it={endCallback:"(function|null)",leftCallback:"(function|null)",rightCallback:"(function|null)"};class rt extends F{constructor(t,e){super(),this._element=t,t&&rt.isSupported()&&(this._config=this._getConfig(e),this._deltaX=0,this._supportPointerEvents=Boolean(window.PointerEvent),this._initEvents())}static get Default(){return nt}static get DefaultType(){return it}static get NAME(){return"swipe"}dispose(){j.off(this._element,G)}_start(t){this._supportPointerEvents?this._eventIsPointerPenTouch(t)&&(this._deltaX=t.clientX):this._deltaX=t.touches[0].clientX}_end(t){this._eventIsPointerPenTouch(t)&&(this._deltaX=t.clientX-this._deltaX),this._handleSwipe(),m(this._config.endCallback)}_move(t){this._deltaX=t.touches&&t.touches.length>1?0:t.touches[0].clientX-this._deltaX}_handleSwipe(){const t=Math.abs(this._deltaX);if(t<=40)return;const e=t/this._deltaX;this._deltaX=0,e&&m(e>0?this._config.rightCallback:this._config.leftCallback)}_initEvents(){this._supportPointerEvents?(j.on(this._element,tt,(t=>this._start(t))),j.on(this._element,et,(t=>this._end(t))),this._element.classList.add("pointer-event")):(j.on(this._element,K,(t=>this._start(t))),j.on(this._element,J,(t=>this._move(t))),j.on(this._element,Z,(t=>this._end(t))))}_eventIsPointerPenTouch(t){return this._supportPointerEvents&&("pen"===t.pointerType||"touch"===t.pointerType)}static isSupported(){return"ontouchstart"in document.documentElement||navigator.maxTouchPoints>0}}const ot=".bs.carousel",st=".data-api",at="next",lt="prev",ct="left",ut="right",ht=`slide${ot}`,ft=`slid${ot}`,dt=`keydown${ot}`,pt=`mouseenter${ot}`,gt=`mouseleave${ot}`,mt=`dragstart${ot}`,vt=`load${ot}${st}`,yt=`click${ot}${st}`,_t="carousel",bt="active",wt=".active",xt=".carousel-item",Tt=wt+xt,Et={ArrowLeft:ut,ArrowRight:ct},Ct={interval:5e3,keyboard:!0,pause:"hover",ride:!1,touch:!0,wrap:!0},St={interval:"(number|boolean)",keyboard:"boolean",pause:"(string|boolean)",ride:"(boolean|string)",touch:"boolean",wrap:"boolean"};class kt extends ${constructor(t,e){super(t,e),this._interval=null,this._activeElement=null,this._isSliding=!1,this.touchTimeout=null,this._swipeHelper=null,this._indicatorsElement=q.findOne(".carousel-indicators",this._element),this._addEventListeners(),this._config.ride===_t&&this.cycle()}static get Default(){return Ct}static get DefaultType(){return St}static get NAME(){return"carousel"}next(){this._slide(at)}nextWhenVisible(){!document.hidden&&a(this._element)&&this.next()}prev(){this._slide(lt)}pause(){this._isSliding&&r(this._element),this._clearInterval()}cycle(){this._clearInterval(),this._updateInterval(),this._interval=setInterval((()=>this.nextWhenVisible()),this._config.interval)}_maybeEnableCycle(){this._config.ride&&(this._isSliding?j.one(this._element,ft,(()=>this.cycle())):this.cycle())}to(t){const e=this._getItems();if(t>e.length-1||t<0)return;if(this._isSliding)return void j.one(this._element,ft,(()=>this.to(t)));const n=this._getItemIndex(this._getActive());if(n===t)return;const i=t>n?at:lt;this._slide(i,e[t])}dispose(){this._swipeHelper&&this._swipeHelper.dispose(),super.dispose()}_configAfterMerge(t){return t.defaultInterval=t.interval,t}_addEventListeners(){this._config.keyboard&&j.on(this._element,dt,(t=>this._keydown(t))),"hover"===this._config.pause&&(j.on(this._element,pt,(()=>this.pause())),j.on(this._element,gt,(()=>this._maybeEnableCycle()))),this._config.touch&&rt.isSupported()&&this._addTouchEventListeners()}_addTouchEventListeners(){for(const t of q.find(".carousel-item img",this._element))j.on(t,mt,(t=>t.preventDefault()));const t={leftCallback:()=>this._slide(this._directionToOrder(ct)),rightCallback:()=>this._slide(this._directionToOrder(ut)),endCallback:()=>{"hover"===this._config.pause&&(this.pause(),this.touchTimeout&&clearTimeout(this.touchTimeout),this.touchTimeout=setTimeout((()=>this._maybeEnableCycle()),500+this._config.interval))}};this._swipeHelper=new rt(this._element,t)}_keydown(t){if(/input|textarea/i.test(t.target.tagName))return;const e=Et[t.key];e&&(t.preventDefault(),this._slide(this._directionToOrder(e)))}_getItemIndex(t){return this._getItems().indexOf(t)}_setActiveIndicatorElement(t){if(!this._indicatorsElement)return;const e=q.findOne(wt,this._indicatorsElement);e.classList.remove(bt),e.removeAttribute("aria-current");const n=q.findOne(`[data-bs-slide-to="${t}"]`,this._indicatorsElement);n&&(n.classList.add(bt),n.setAttribute("aria-current","true"))}_updateInterval(){const t=this._activeElement||this._getActive();if(!t)return;const e=Number.parseInt(t.getAttribute("data-bs-interval"),10);this._config.interval=e||this._config.defaultInterval}_slide(t,e=null){if(this._isSliding)return;const n=this._getActive(),i=t===at,r=e||y(this._getItems(),n,i,this._config.wrap);if(r===n)return;const o=this._getItemIndex(r),s=e=>j.trigger(this._element,e,{relatedTarget:r,direction:this._orderToDirection(t),from:this._getItemIndex(n),to:o});if(s(ht).defaultPrevented)return;if(!n||!r)return;const a=Boolean(this._interval);this.pause(),this._isSliding=!0,this._setActiveIndicatorElement(o),this._activeElement=r;const l=i?"carousel-item-start":"carousel-item-end",c=i?"carousel-item-next":"carousel-item-prev";r.classList.add(c),h(r),n.classList.add(l),r.classList.add(l),this._queueCallback((()=>{r.classList.remove(l,c),r.classList.add(bt),n.classList.remove(bt,c,l),this._isSliding=!1,s(ft)}),n,this._isAnimated()),a&&this.cycle()}_isAnimated(){return this._element.classList.contains("slide")}_getActive(){return q.findOne(Tt,this._element)}_getItems(){return q.find(xt,this._element)}_clearInterval(){this._interval&&(clearInterval(this._interval),this._interval=null)}_directionToOrder(t){return p()?t===ct?lt:at:t===ct?at:lt}_orderToDirection(t){return p()?t===lt?ct:ut:t===lt?ut:ct}static jQueryInterface(t){return this.each((function(){const e=kt.getOrCreateInstance(this,t);if("number"!=typeof t){if("string"==typeof t){if(void 0===e[t]||t.startsWith("_")||"constructor"===t)throw new TypeError(`No method named "${t}"`);e[t]()}}else e.to(t)}))}}j.on(document,yt,"[data-bs-slide], [data-bs-slide-to]",(function(t){const e=q.getElementFromSelector(this);if(!e||!e.classList.contains(_t))return;t.preventDefault();const n=kt.getOrCreateInstance(e),i=this.getAttribute("data-bs-slide-to");return i?(n.to(i),void n._maybeEnableCycle()):"next"===z.getDataAttribute(this,"slide")?(n.next(),void n._maybeEnableCycle()):(n.prev(),void n._maybeEnableCycle())})),j.on(window,vt,(()=>{const t=q.find('[data-bs-ride="carousel"]');for(const e of t)kt.getOrCreateInstance(e)})),g(kt);const At=".bs.collapse",Ot=`show${At}`,Dt=`shown${At}`,Mt=`hide${At}`,Pt=`hidden${At}`,Lt=`click${At}.data-api`,jt="show",Nt="collapse",It="collapsing",Rt=`:scope .${Nt} .${Nt}`,zt='[data-bs-toggle="collapse"]',Ft={parent:null,toggle:!0},$t={parent:"(null|element)",toggle:"boolean"};class Ht extends ${constructor(t,e){super(t,e),this._isTransitioning=!1,this._triggerArray=[];const n=q.find(zt);for(const t of n){const e=q.getSelectorFromElement(t),n=q.find(e).filter((t=>t===this._element));null!==e&&n.length&&this._triggerArray.push(t)}this._initializeChildren(),this._config.parent||this._addAriaAndCollapsedClass(this._triggerArray,this._isShown()),this._config.toggle&&this.toggle()}static get Default(){return Ft}static get DefaultType(){return $t}static get NAME(){return"collapse"}toggle(){this._isShown()?this.hide():this.show()}show(){if(this._isTransitioning||this._isShown())return;let t=[];if(this._config.parent&&(t=this._getFirstLevelChildren(".collapse.show, .collapse.collapsing").filter((t=>t!==this._element)).map((t=>Ht.getOrCreateInstance(t,{toggle:!1})))),t.length&&t[0]._isTransitioning)return;if(j.trigger(this._element,Ot).defaultPrevented)return;for(const e of t)e.hide();const e=this._getDimension();this._element.classList.remove(Nt),this._element.classList.add(It),this._element.style[e]=0,this._addAriaAndCollapsedClass(this._triggerArray,!0),this._isTransitioning=!0;const n=`scroll${e[0].toUpperCase()+e.slice(1)}`;this._queueCallback((()=>{this._isTransitioning=!1,this._element.classList.remove(It),this._element.classList.add(Nt,jt),this._element.style[e]="",j.trigger(this._element,Dt)}),this._element,!0),this._element.style[e]=`${this._element[n]}px`}hide(){if(this._isTransitioning||!this._isShown())return;if(j.trigger(this._element,Mt).defaultPrevented)return;const t=this._getDimension();this._element.style[t]=`${this._element.getBoundingClientRect()[t]}px`,h(this._element),this._element.classList.add(It),this._element.classList.remove(Nt,jt);for(const t of this._triggerArray){const e=q.getElementFromSelector(t);e&&!this._isShown(e)&&this._addAriaAndCollapsedClass([t],!1)}this._isTransitioning=!0,this._element.style[t]="",this._queueCallback((()=>{this._isTransitioning=!1,this._element.classList.remove(It),this._element.classList.add(Nt),j.trigger(this._element,Pt)}),this._element,!0)}_isShown(t=this._element){return t.classList.contains(jt)}_configAfterMerge(t){return t.toggle=Boolean(t.toggle),t.parent=s(t.parent),t}_getDimension(){return this._element.classList.contains("collapse-horizontal")?"width":"height"}_initializeChildren(){if(!this._config.parent)return;const t=this._getFirstLevelChildren(zt);for(const e of t){const t=q.getElementFromSelector(e);t&&this._addAriaAndCollapsedClass([e],this._isShown(t))}}_getFirstLevelChildren(t){const e=q.find(Rt,this._config.parent);return q.find(t,this._config.parent).filter((t=>!e.includes(t)))}_addAriaAndCollapsedClass(t,e){if(t.length)for(const n of t)n.classList.toggle("collapsed",!e),n.setAttribute("aria-expanded",e)}static jQueryInterface(t){const e={};return"string"==typeof t&&/show|hide/.test(t)&&(e.toggle=!1),this.each((function(){const n=Ht.getOrCreateInstance(this,e);if("string"==typeof t){if(void 0===n[t])throw new TypeError(`No method named "${t}"`);n[t]()}}))}}j.on(document,Lt,zt,(function(t){("A"===t.target.tagName||t.delegateTarget&&"A"===t.delegateTarget.tagName)&&t.preventDefault();for(const t of q.getMultipleElementsFromSelector(this))Ht.getOrCreateInstance(t,{toggle:!1}).toggle()})),g(Ht);var qt="top",Bt="bottom",Wt="right",Xt="left",Yt="auto",Ut=[qt,Bt,Wt,Xt],Vt="start",Qt="end",Gt="clippingParents",Kt="viewport",Jt="popper",Zt="reference",te=Ut.reduce((function(t,e){return t.concat([e+"-"+Vt,e+"-"+Qt])}),[]),ee=[].concat(Ut,[Yt]).reduce((function(t,e){return t.concat([e,e+"-"+Vt,e+"-"+Qt])}),[]),ne="beforeRead",ie="read",re="afterRead",oe="beforeMain",se="main",ae="afterMain",le="beforeWrite",ce="write",ue="afterWrite",he=[ne,ie,re,oe,se,ae,le,ce,ue];function fe(t){return t?(t.nodeName||"").toLowerCase():null}function de(t){if(null==t)return window;if("[object Window]"!==t.toString()){var e=t.ownerDocument;return e&&e.defaultView||window}return t}function pe(t){return t instanceof de(t).Element||t instanceof Element}function ge(t){return t instanceof de(t).HTMLElement||t instanceof HTMLElement}function me(t){return"undefined"!=typeof ShadowRoot&&(t instanceof de(t).ShadowRoot||t instanceof ShadowRoot)}const ve={name:"applyStyles",enabled:!0,phase:"write",fn:function(t){var e=t.state;Object.keys(e.elements).forEach((function(t){var n=e.styles[t]||{},i=e.attributes[t]||{},r=e.elements[t];ge(r)&&fe(r)&&(Object.assign(r.style,n),Object.keys(i).forEach((function(t){var e=i[t];!1===e?r.removeAttribute(t):r.setAttribute(t,!0===e?"":e)})))}))},effect:function(t){var e=t.state,n={popper:{position:e.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(e.elements.popper.style,n.popper),e.styles=n,e.elements.arrow&&Object.assign(e.elements.arrow.style,n.arrow),function(){Object.keys(e.elements).forEach((function(t){var i=e.elements[t],r=e.attributes[t]||{},o=Object.keys(e.styles.hasOwnProperty(t)?e.styles[t]:n[t]).reduce((function(t,e){return t[e]="",t}),{});ge(i)&&fe(i)&&(Object.assign(i.style,o),Object.keys(r).forEach((function(t){i.removeAttribute(t)})))}))}},requires:["computeStyles"]};function ye(t){return t.split("-")[0]}var _e=Math.max,be=Math.min,we=Math.round;function xe(){var t=navigator.userAgentData;return null!=t&&t.brands&&Array.isArray(t.brands)?t.brands.map((function(t){return t.brand+"/"+t.version})).join(" "):navigator.userAgent}function Te(){return!/^((?!chrome|android).)*safari/i.test(xe())}function Ee(t,e,n){void 0===e&&(e=!1),void 0===n&&(n=!1);var i=t.getBoundingClientRect(),r=1,o=1;e&&ge(t)&&(r=t.offsetWidth>0&&we(i.width)/t.offsetWidth||1,o=t.offsetHeight>0&&we(i.height)/t.offsetHeight||1);var s=(pe(t)?de(t):window).visualViewport,a=!Te()&&n,l=(i.left+(a&&s?s.offsetLeft:0))/r,c=(i.top+(a&&s?s.offsetTop:0))/o,u=i.width/r,h=i.height/o;return{width:u,height:h,top:c,right:l+u,bottom:c+h,left:l,x:l,y:c}}function Ce(t){var e=Ee(t),n=t.offsetWidth,i=t.offsetHeight;return Math.abs(e.width-n)<=1&&(n=e.width),Math.abs(e.height-i)<=1&&(i=e.height),{x:t.offsetLeft,y:t.offsetTop,width:n,height:i}}function Se(t,e){var n=e.getRootNode&&e.getRootNode();if(t.contains(e))return!0;if(n&&me(n)){var i=e;do{if(i&&t.isSameNode(i))return!0;i=i.parentNode||i.host}while(i)}return!1}function ke(t){return de(t).getComputedStyle(t)}function Ae(t){return["table","td","th"].indexOf(fe(t))>=0}function Oe(t){return((pe(t)?t.ownerDocument:t.document)||window.document).documentElement}function De(t){return"html"===fe(t)?t:t.assignedSlot||t.parentNode||(me(t)?t.host:null)||Oe(t)}function Me(t){return ge(t)&&"fixed"!==ke(t).position?t.offsetParent:null}function Pe(t){for(var e=de(t),n=Me(t);n&&Ae(n)&&"static"===ke(n).position;)n=Me(n);return n&&("html"===fe(n)||"body"===fe(n)&&"static"===ke(n).position)?e:n||function(t){var e=/firefox/i.test(xe());if(/Trident/i.test(xe())&&ge(t)&&"fixed"===ke(t).position)return null;var n=De(t);for(me(n)&&(n=n.host);ge(n)&&["html","body"].indexOf(fe(n))<0;){var i=ke(n);if("none"!==i.transform||"none"!==i.perspective||"paint"===i.contain||-1!==["transform","perspective"].indexOf(i.willChange)||e&&"filter"===i.willChange||e&&i.filter&&"none"!==i.filter)return n;n=n.parentNode}return null}(t)||e}function Le(t){return["top","bottom"].indexOf(t)>=0?"x":"y"}function je(t,e,n){return _e(t,be(e,n))}function Ne(t){return Object.assign({},{top:0,right:0,bottom:0,left:0},t)}function Ie(t,e){return e.reduce((function(e,n){return e[n]=t,e}),{})}const Re={name:"arrow",enabled:!0,phase:"main",fn:function(t){var e,n=t.state,i=t.name,r=t.options,o=n.elements.arrow,s=n.modifiersData.popperOffsets,a=ye(n.placement),l=Le(a),c=[Xt,Wt].indexOf(a)>=0?"height":"width";if(o&&s){var u=function(t,e){return Ne("number"!=typeof(t="function"==typeof t?t(Object.assign({},e.rects,{placement:e.placement})):t)?t:Ie(t,Ut))}(r.padding,n),h=Ce(o),f="y"===l?qt:Xt,d="y"===l?Bt:Wt,p=n.rects.reference[c]+n.rects.reference[l]-s[l]-n.rects.popper[c],g=s[l]-n.rects.reference[l],m=Pe(o),v=m?"y"===l?m.clientHeight||0:m.clientWidth||0:0,y=p/2-g/2,_=u[f],b=v-h[c]-u[d],w=v/2-h[c]/2+y,x=je(_,w,b),T=l;n.modifiersData[i]=((e={})[T]=x,e.centerOffset=x-w,e)}},effect:function(t){var e=t.state,n=t.options.element,i=void 0===n?"[data-popper-arrow]":n;null!=i&&("string"!=typeof i||(i=e.elements.popper.querySelector(i)))&&Se(e.elements.popper,i)&&(e.elements.arrow=i)},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function ze(t){return t.split("-")[1]}var Fe={top:"auto",right:"auto",bottom:"auto",left:"auto"};function $e(t){var e,n=t.popper,i=t.popperRect,r=t.placement,o=t.variation,s=t.offsets,a=t.position,l=t.gpuAcceleration,c=t.adaptive,u=t.roundOffsets,h=t.isFixed,f=s.x,d=void 0===f?0:f,p=s.y,g=void 0===p?0:p,m="function"==typeof u?u({x:d,y:g}):{x:d,y:g};d=m.x,g=m.y;var v=s.hasOwnProperty("x"),y=s.hasOwnProperty("y"),_=Xt,b=qt,w=window;if(c){var x=Pe(n),T="clientHeight",E="clientWidth";x===de(n)&&"static"!==ke(x=Oe(n)).position&&"absolute"===a&&(T="scrollHeight",E="scrollWidth"),(r===qt||(r===Xt||r===Wt)&&o===Qt)&&(b=Bt,g-=(h&&x===w&&w.visualViewport?w.visualViewport.height:x[T])-i.height,g*=l?1:-1),r!==Xt&&(r!==qt&&r!==Bt||o!==Qt)||(_=Wt,d-=(h&&x===w&&w.visualViewport?w.visualViewport.width:x[E])-i.width,d*=l?1:-1)}var C,S=Object.assign({position:a},c&&Fe),k=!0===u?function(t,e){var n=t.x,i=t.y,r=e.devicePixelRatio||1;return{x:we(n*r)/r||0,y:we(i*r)/r||0}}({x:d,y:g},de(n)):{x:d,y:g};return d=k.x,g=k.y,l?Object.assign({},S,((C={})[b]=y?"0":"",C[_]=v?"0":"",C.transform=(w.devicePixelRatio||1)<=1?"translate("+d+"px, "+g+"px)":"translate3d("+d+"px, "+g+"px, 0)",C)):Object.assign({},S,((e={})[b]=y?g+"px":"",e[_]=v?d+"px":"",e.transform="",e))}const He={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(t){var e=t.state,n=t.options,i=n.gpuAcceleration,r=void 0===i||i,o=n.adaptive,s=void 0===o||o,a=n.roundOffsets,l=void 0===a||a,c={placement:ye(e.placement),variation:ze(e.placement),popper:e.elements.popper,popperRect:e.rects.popper,gpuAcceleration:r,isFixed:"fixed"===e.options.strategy};null!=e.modifiersData.popperOffsets&&(e.styles.popper=Object.assign({},e.styles.popper,$e(Object.assign({},c,{offsets:e.modifiersData.popperOffsets,position:e.options.strategy,adaptive:s,roundOffsets:l})))),null!=e.modifiersData.arrow&&(e.styles.arrow=Object.assign({},e.styles.arrow,$e(Object.assign({},c,{offsets:e.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:l})))),e.attributes.popper=Object.assign({},e.attributes.popper,{"data-popper-placement":e.placement})},data:{}};var qe={passive:!0};const Be={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(t){var e=t.state,n=t.instance,i=t.options,r=i.scroll,o=void 0===r||r,s=i.resize,a=void 0===s||s,l=de(e.elements.popper),c=[].concat(e.scrollParents.reference,e.scrollParents.popper);return o&&c.forEach((function(t){t.addEventListener("scroll",n.update,qe)})),a&&l.addEventListener("resize",n.update,qe),function(){o&&c.forEach((function(t){t.removeEventListener("scroll",n.update,qe)})),a&&l.removeEventListener("resize",n.update,qe)}},data:{}};var We={left:"right",right:"left",bottom:"top",top:"bottom"};function Xe(t){return t.replace(/left|right|bottom|top/g,(function(t){return We[t]}))}var Ye={start:"end",end:"start"};function Ue(t){return t.replace(/start|end/g,(function(t){return Ye[t]}))}function Ve(t){var e=de(t);return{scrollLeft:e.pageXOffset,scrollTop:e.pageYOffset}}function Qe(t){return Ee(Oe(t)).left+Ve(t).scrollLeft}function Ge(t){var e=ke(t),n=e.overflow,i=e.overflowX,r=e.overflowY;return/auto|scroll|overlay|hidden/.test(n+r+i)}function Ke(t){return["html","body","#document"].indexOf(fe(t))>=0?t.ownerDocument.body:ge(t)&&Ge(t)?t:Ke(De(t))}function Je(t,e){var n;void 0===e&&(e=[]);var i=Ke(t),r=i===(null==(n=t.ownerDocument)?void 0:n.body),o=de(i),s=r?[o].concat(o.visualViewport||[],Ge(i)?i:[]):i,a=e.concat(s);return r?a:a.concat(Je(De(s)))}function Ze(t){return Object.assign({},t,{left:t.x,top:t.y,right:t.x+t.width,bottom:t.y+t.height})}function tn(t,e,n){return e===Kt?Ze(function(t,e){var n=de(t),i=Oe(t),r=n.visualViewport,o=i.clientWidth,s=i.clientHeight,a=0,l=0;if(r){o=r.width,s=r.height;var c=Te();(c||!c&&"fixed"===e)&&(a=r.offsetLeft,l=r.offsetTop)}return{width:o,height:s,x:a+Qe(t),y:l}}(t,n)):pe(e)?function(t,e){var n=Ee(t,!1,"fixed"===e);return n.top=n.top+t.clientTop,n.left=n.left+t.clientLeft,n.bottom=n.top+t.clientHeight,n.right=n.left+t.clientWidth,n.width=t.clientWidth,n.height=t.clientHeight,n.x=n.left,n.y=n.top,n}(e,n):Ze(function(t){var e,n=Oe(t),i=Ve(t),r=null==(e=t.ownerDocument)?void 0:e.body,o=_e(n.scrollWidth,n.clientWidth,r?r.scrollWidth:0,r?r.clientWidth:0),s=_e(n.scrollHeight,n.clientHeight,r?r.scrollHeight:0,r?r.clientHeight:0),a=-i.scrollLeft+Qe(t),l=-i.scrollTop;return"rtl"===ke(r||n).direction&&(a+=_e(n.clientWidth,r?r.clientWidth:0)-o),{width:o,height:s,x:a,y:l}}(Oe(t)))}function en(t){var e,n=t.reference,i=t.element,r=t.placement,o=r?ye(r):null,s=r?ze(r):null,a=n.x+n.width/2-i.width/2,l=n.y+n.height/2-i.height/2;switch(o){case qt:e={x:a,y:n.y-i.height};break;case Bt:e={x:a,y:n.y+n.height};break;case Wt:e={x:n.x+n.width,y:l};break;case Xt:e={x:n.x-i.width,y:l};break;default:e={x:n.x,y:n.y}}var c=o?Le(o):null;if(null!=c){var u="y"===c?"height":"width";switch(s){case Vt:e[c]=e[c]-(n[u]/2-i[u]/2);break;case Qt:e[c]=e[c]+(n[u]/2-i[u]/2)}}return e}function nn(t,e){void 0===e&&(e={});var n=e,i=n.placement,r=void 0===i?t.placement:i,o=n.strategy,s=void 0===o?t.strategy:o,a=n.boundary,l=void 0===a?Gt:a,c=n.rootBoundary,u=void 0===c?Kt:c,h=n.elementContext,f=void 0===h?Jt:h,d=n.altBoundary,p=void 0!==d&&d,g=n.padding,m=void 0===g?0:g,v=Ne("number"!=typeof m?m:Ie(m,Ut)),y=f===Jt?Zt:Jt,_=t.rects.popper,b=t.elements[p?y:f],w=function(t,e,n,i){var r="clippingParents"===e?function(t){var e=Je(De(t)),n=["absolute","fixed"].indexOf(ke(t).position)>=0&&ge(t)?Pe(t):t;return pe(n)?e.filter((function(t){return pe(t)&&Se(t,n)&&"body"!==fe(t)})):[]}(t):[].concat(e),o=[].concat(r,[n]),s=o[0],a=o.reduce((function(e,n){var r=tn(t,n,i);return e.top=_e(r.top,e.top),e.right=be(r.right,e.right),e.bottom=be(r.bottom,e.bottom),e.left=_e(r.left,e.left),e}),tn(t,s,i));return a.width=a.right-a.left,a.height=a.bottom-a.top,a.x=a.left,a.y=a.top,a}(pe(b)?b:b.contextElement||Oe(t.elements.popper),l,u,s),x=Ee(t.elements.reference),T=en({reference:x,element:_,strategy:"absolute",placement:r}),E=Ze(Object.assign({},_,T)),C=f===Jt?E:x,S={top:w.top-C.top+v.top,bottom:C.bottom-w.bottom+v.bottom,left:w.left-C.left+v.left,right:C.right-w.right+v.right},k=t.modifiersData.offset;if(f===Jt&&k){var A=k[r];Object.keys(S).forEach((function(t){var e=[Wt,Bt].indexOf(t)>=0?1:-1,n=[qt,Bt].indexOf(t)>=0?"y":"x";S[t]+=A[n]*e}))}return S}const rn={name:"flip",enabled:!0,phase:"main",fn:function(t){var e=t.state,n=t.options,i=t.name;if(!e.modifiersData[i]._skip){for(var r=n.mainAxis,o=void 0===r||r,s=n.altAxis,a=void 0===s||s,l=n.fallbackPlacements,c=n.padding,u=n.boundary,h=n.rootBoundary,f=n.altBoundary,d=n.flipVariations,p=void 0===d||d,g=n.allowedAutoPlacements,m=e.options.placement,v=ye(m),y=l||(v!==m&&p?function(t){if(ye(t)===Yt)return[];var e=Xe(t);return[Ue(t),e,Ue(e)]}(m):[Xe(m)]),_=[m].concat(y).reduce((function(t,n){return t.concat(ye(n)===Yt?function(t,e){void 0===e&&(e={});var n=e,i=n.placement,r=n.boundary,o=n.rootBoundary,s=n.padding,a=n.flipVariations,l=n.allowedAutoPlacements,c=void 0===l?ee:l,u=ze(i),h=u?a?te:te.filter((function(t){return ze(t)===u})):Ut,f=h.filter((function(t){return c.indexOf(t)>=0}));0===f.length&&(f=h);var d=f.reduce((function(e,n){return e[n]=nn(t,{placement:n,boundary:r,rootBoundary:o,padding:s})[ye(n)],e}),{});return Object.keys(d).sort((function(t,e){return d[t]-d[e]}))}(e,{placement:n,boundary:u,rootBoundary:h,padding:c,flipVariations:p,allowedAutoPlacements:g}):n)}),[]),b=e.rects.reference,w=e.rects.popper,x=new Map,T=!0,E=_[0],C=0;C<_.length;C++){var S=_[C],k=ye(S),A=ze(S)===Vt,O=[qt,Bt].indexOf(k)>=0,D=O?"width":"height",M=nn(e,{placement:S,boundary:u,rootBoundary:h,altBoundary:f,padding:c}),P=O?A?Wt:Xt:A?Bt:qt;b[D]>w[D]&&(P=Xe(P));var L=Xe(P),j=[];if(o&&j.push(M[k]<=0),a&&j.push(M[P]<=0,M[L]<=0),j.every((function(t){return t}))){E=S,T=!1;break}x.set(S,j)}if(T)for(var N=function(t){var e=_.find((function(e){var n=x.get(e);if(n)return n.slice(0,t).every((function(t){return t}))}));if(e)return E=e,"break"},I=p?3:1;I>0&&"break"!==N(I);I--);e.placement!==E&&(e.modifiersData[i]._skip=!0,e.placement=E,e.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}};function on(t,e,n){return void 0===n&&(n={x:0,y:0}),{top:t.top-e.height-n.y,right:t.right-e.width+n.x,bottom:t.bottom-e.height+n.y,left:t.left-e.width-n.x}}function sn(t){return[qt,Wt,Bt,Xt].some((function(e){return t[e]>=0}))}const an={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(t){var e=t.state,n=t.name,i=e.rects.reference,r=e.rects.popper,o=e.modifiersData.preventOverflow,s=nn(e,{elementContext:"reference"}),a=nn(e,{altBoundary:!0}),l=on(s,i),c=on(a,r,o),u=sn(l),h=sn(c);e.modifiersData[n]={referenceClippingOffsets:l,popperEscapeOffsets:c,isReferenceHidden:u,hasPopperEscaped:h},e.attributes.popper=Object.assign({},e.attributes.popper,{"data-popper-reference-hidden":u,"data-popper-escaped":h})}},ln={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(t){var e=t.state,n=t.options,i=t.name,r=n.offset,o=void 0===r?[0,0]:r,s=ee.reduce((function(t,n){return t[n]=function(t,e,n){var i=ye(t),r=[Xt,qt].indexOf(i)>=0?-1:1,o="function"==typeof n?n(Object.assign({},e,{placement:t})):n,s=o[0],a=o[1];return s=s||0,a=(a||0)*r,[Xt,Wt].indexOf(i)>=0?{x:a,y:s}:{x:s,y:a}}(n,e.rects,o),t}),{}),a=s[e.placement],l=a.x,c=a.y;null!=e.modifiersData.popperOffsets&&(e.modifiersData.popperOffsets.x+=l,e.modifiersData.popperOffsets.y+=c),e.modifiersData[i]=s}},cn={name:"popperOffsets",enabled:!0,phase:"read",fn:function(t){var e=t.state,n=t.name;e.modifiersData[n]=en({reference:e.rects.reference,element:e.rects.popper,strategy:"absolute",placement:e.placement})},data:{}},un={name:"preventOverflow",enabled:!0,phase:"main",fn:function(t){var e=t.state,n=t.options,i=t.name,r=n.mainAxis,o=void 0===r||r,s=n.altAxis,a=void 0!==s&&s,l=n.boundary,c=n.rootBoundary,u=n.altBoundary,h=n.padding,f=n.tether,d=void 0===f||f,p=n.tetherOffset,g=void 0===p?0:p,m=nn(e,{boundary:l,rootBoundary:c,padding:h,altBoundary:u}),v=ye(e.placement),y=ze(e.placement),_=!y,b=Le(v),w="x"===b?"y":"x",x=e.modifiersData.popperOffsets,T=e.rects.reference,E=e.rects.popper,C="function"==typeof g?g(Object.assign({},e.rects,{placement:e.placement})):g,S="number"==typeof C?{mainAxis:C,altAxis:C}:Object.assign({mainAxis:0,altAxis:0},C),k=e.modifiersData.offset?e.modifiersData.offset[e.placement]:null,A={x:0,y:0};if(x){if(o){var O,D="y"===b?qt:Xt,M="y"===b?Bt:Wt,P="y"===b?"height":"width",L=x[b],j=L+m[D],N=L-m[M],I=d?-E[P]/2:0,R=y===Vt?T[P]:E[P],z=y===Vt?-E[P]:-T[P],F=e.elements.arrow,$=d&&F?Ce(F):{width:0,height:0},H=e.modifiersData["arrow#persistent"]?e.modifiersData["arrow#persistent"].padding:{top:0,right:0,bottom:0,left:0},q=H[D],B=H[M],W=je(0,T[P],$[P]),X=_?T[P]/2-I-W-q-S.mainAxis:R-W-q-S.mainAxis,Y=_?-T[P]/2+I+W+B+S.mainAxis:z+W+B+S.mainAxis,U=e.elements.arrow&&Pe(e.elements.arrow),V=U?"y"===b?U.clientTop||0:U.clientLeft||0:0,Q=null!=(O=null==k?void 0:k[b])?O:0,G=L+Y-Q,K=je(d?be(j,L+X-Q-V):j,L,d?_e(N,G):N);x[b]=K,A[b]=K-L}if(a){var J,Z="x"===b?qt:Xt,tt="x"===b?Bt:Wt,et=x[w],nt="y"===w?"height":"width",it=et+m[Z],rt=et-m[tt],ot=-1!==[qt,Xt].indexOf(v),st=null!=(J=null==k?void 0:k[w])?J:0,at=ot?it:et-T[nt]-E[nt]-st+S.altAxis,lt=ot?et+T[nt]+E[nt]-st-S.altAxis:rt,ct=d&&ot?function(t,e,n){var i=je(t,e,n);return i>n?n:i}(at,et,lt):je(d?at:it,et,d?lt:rt);x[w]=ct,A[w]=ct-et}e.modifiersData[i]=A}},requiresIfExists:["offset"]};function hn(t,e,n){void 0===n&&(n=!1);var i,r,o=ge(e),s=ge(e)&&function(t){var e=t.getBoundingClientRect(),n=we(e.width)/t.offsetWidth||1,i=we(e.height)/t.offsetHeight||1;return 1!==n||1!==i}(e),a=Oe(e),l=Ee(t,s,n),c={scrollLeft:0,scrollTop:0},u={x:0,y:0};return(o||!o&&!n)&&(("body"!==fe(e)||Ge(a))&&(c=(i=e)!==de(i)&&ge(i)?{scrollLeft:(r=i).scrollLeft,scrollTop:r.scrollTop}:Ve(i)),ge(e)?((u=Ee(e,!0)).x+=e.clientLeft,u.y+=e.clientTop):a&&(u.x=Qe(a))),{x:l.left+c.scrollLeft-u.x,y:l.top+c.scrollTop-u.y,width:l.width,height:l.height}}function fn(t){var e=new Map,n=new Set,i=[];function r(t){n.add(t.name),[].concat(t.requires||[],t.requiresIfExists||[]).forEach((function(t){if(!n.has(t)){var i=e.get(t);i&&r(i)}})),i.push(t)}return t.forEach((function(t){e.set(t.name,t)})),t.forEach((function(t){n.has(t.name)||r(t)})),i}var dn={placement:"bottom",modifiers:[],strategy:"absolute"};function pn(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];return!e.some((function(t){return!(t&&"function"==typeof t.getBoundingClientRect)}))}function gn(t){void 0===t&&(t={});var e=t,n=e.defaultModifiers,i=void 0===n?[]:n,r=e.defaultOptions,o=void 0===r?dn:r;return function(t,e,n){void 0===n&&(n=o);var r,s,a={placement:"bottom",orderedModifiers:[],options:Object.assign({},dn,o),modifiersData:{},elements:{reference:t,popper:e},attributes:{},styles:{}},l=[],c=!1,u={state:a,setOptions:function(n){var r="function"==typeof n?n(a.options):n;h(),a.options=Object.assign({},o,a.options,r),a.scrollParents={reference:pe(t)?Je(t):t.contextElement?Je(t.contextElement):[],popper:Je(e)};var s,c,f=function(t){var e=fn(t);return he.reduce((function(t,n){return t.concat(e.filter((function(t){return t.phase===n})))}),[])}((s=[].concat(i,a.options.modifiers),c=s.reduce((function(t,e){var n=t[e.name];return t[e.name]=n?Object.assign({},n,e,{options:Object.assign({},n.options,e.options),data:Object.assign({},n.data,e.data)}):e,t}),{}),Object.keys(c).map((function(t){return c[t]}))));return a.orderedModifiers=f.filter((function(t){return t.enabled})),a.orderedModifiers.forEach((function(t){var e=t.name,n=t.options,i=void 0===n?{}:n,r=t.effect;if("function"==typeof r){var o=r({state:a,name:e,instance:u,options:i});l.push(o||function(){})}})),u.update()},forceUpdate:function(){if(!c){var t=a.elements,e=t.reference,n=t.popper;if(pn(e,n)){a.rects={reference:hn(e,Pe(n),"fixed"===a.options.strategy),popper:Ce(n)},a.reset=!1,a.placement=a.options.placement,a.orderedModifiers.forEach((function(t){return a.modifiersData[t.name]=Object.assign({},t.data)}));for(var i=0;i<a.orderedModifiers.length;i++)if(!0!==a.reset){var r=a.orderedModifiers[i],o=r.fn,s=r.options,l=void 0===s?{}:s,h=r.name;"function"==typeof o&&(a=o({state:a,options:l,name:h,instance:u})||a)}else a.reset=!1,i=-1}}},update:(r=function(){return new Promise((function(t){u.forceUpdate(),t(a)}))},function(){return s||(s=new Promise((function(t){Promise.resolve().then((function(){s=void 0,t(r())}))}))),s}),destroy:function(){h(),c=!0}};if(!pn(t,e))return u;function h(){l.forEach((function(t){return t()})),l=[]}return u.setOptions(n).then((function(t){!c&&n.onFirstUpdate&&n.onFirstUpdate(t)})),u}}var mn=gn(),vn=gn({defaultModifiers:[Be,cn,He,ve]}),yn=gn({defaultModifiers:[Be,cn,He,ve,ln,rn,un,Re,an]});const _n=Object.freeze(Object.defineProperty({__proto__:null,afterMain:ae,afterRead:re,afterWrite:ue,applyStyles:ve,arrow:Re,auto:Yt,basePlacements:Ut,beforeMain:oe,beforeRead:ne,beforeWrite:le,bottom:Bt,clippingParents:Gt,computeStyles:He,createPopper:yn,createPopperBase:mn,createPopperLite:vn,detectOverflow:nn,end:Qt,eventListeners:Be,flip:rn,hide:an,left:Xt,main:se,modifierPhases:he,offset:ln,placements:ee,popper:Jt,popperGenerator:gn,popperOffsets:cn,preventOverflow:un,read:ie,reference:Zt,right:Wt,start:Vt,top:qt,variationPlacements:te,viewport:Kt,write:ce},Symbol.toStringTag,{value:"Module"})),bn="dropdown",wn=".bs.dropdown",xn=".data-api",Tn="ArrowUp",En="ArrowDown",Cn=`hide${wn}`,Sn=`hidden${wn}`,kn=`show${wn}`,An=`shown${wn}`,On=`click${wn}${xn}`,Dn=`keydown${wn}${xn}`,Mn=`keyup${wn}${xn}`,Pn="show",Ln='[data-bs-toggle="dropdown"]:not(.disabled):not(:disabled)',jn=`${Ln}.${Pn}`,Nn=".dropdown-menu",In=p()?"top-end":"top-start",Rn=p()?"top-start":"top-end",zn=p()?"bottom-end":"bottom-start",Fn=p()?"bottom-start":"bottom-end",$n=p()?"left-start":"right-start",Hn=p()?"right-start":"left-start",qn={autoClose:!0,boundary:"clippingParents",display:"dynamic",offset:[0,2],popperConfig:null,reference:"toggle"},Bn={autoClose:"(boolean|string)",boundary:"(string|element)",display:"string",offset:"(array|string|function)",popperConfig:"(null|object|function)",reference:"(string|element|object)"};class Wn extends ${constructor(t,e){super(t,e),this._popper=null,this._parent=this._element.parentNode,this._menu=q.next(this._element,Nn)[0]||q.prev(this._element,Nn)[0]||q.findOne(Nn,this._parent),this._inNavbar=this._detectNavbar()}static get Default(){return qn}static get DefaultType(){return Bn}static get NAME(){return bn}toggle(){return this._isShown()?this.hide():this.show()}show(){if(l(this._element)||this._isShown())return;const t={relatedTarget:this._element};if(!j.trigger(this._element,kn,t).defaultPrevented){if(this._createPopper(),"ontouchstart"in document.documentElement&&!this._parent.closest(".navbar-nav"))for(const t of[].concat(...document.body.children))j.on(t,"mouseover",u);this._element.focus(),this._element.setAttribute("aria-expanded",!0),this._menu.classList.add(Pn),this._element.classList.add(Pn),j.trigger(this._element,An,t)}}hide(){if(l(this._element)||!this._isShown())return;const t={relatedTarget:this._element};this._completeHide(t)}dispose(){this._popper&&this._popper.destroy(),super.dispose()}update(){this._inNavbar=this._detectNavbar(),this._popper&&this._popper.update()}_completeHide(t){if(!j.trigger(this._element,Cn,t).defaultPrevented){if("ontouchstart"in document.documentElement)for(const t of[].concat(...document.body.children))j.off(t,"mouseover",u);this._popper&&this._popper.destroy(),this._menu.classList.remove(Pn),this._element.classList.remove(Pn),this._element.setAttribute("aria-expanded","false"),z.removeDataAttribute(this._menu,"popper"),j.trigger(this._element,Sn,t)}}_getConfig(t){if("object"==typeof(t=super._getConfig(t)).reference&&!o(t.reference)&&"function"!=typeof t.reference.getBoundingClientRect)throw new TypeError(`${bn.toUpperCase()}: Option "reference" provided type "object" without a required "getBoundingClientRect" method.`);return t}_createPopper(){if(void 0===_n)throw new TypeError("Bootstrap's dropdowns require Popper (https://popper.js.org)");let t=this._element;"parent"===this._config.reference?t=this._parent:o(this._config.reference)?t=s(this._config.reference):"object"==typeof this._config.reference&&(t=this._config.reference);const e=this._getPopperConfig();this._popper=yn(t,this._menu,e)}_isShown(){return this._menu.classList.contains(Pn)}_getPlacement(){const t=this._parent;if(t.classList.contains("dropend"))return $n;if(t.classList.contains("dropstart"))return Hn;if(t.classList.contains("dropup-center"))return"top";if(t.classList.contains("dropdown-center"))return"bottom";const e="end"===getComputedStyle(this._menu).getPropertyValue("--bs-position").trim();return t.classList.contains("dropup")?e?Rn:In:e?Fn:zn}_detectNavbar(){return null!==this._element.closest(".navbar")}_getOffset(){const{offset:t}=this._config;return"string"==typeof t?t.split(",").map((t=>Number.parseInt(t,10))):"function"==typeof t?e=>t(e,this._element):t}_getPopperConfig(){const t={placement:this._getPlacement(),modifiers:[{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"offset",options:{offset:this._getOffset()}}]};return(this._inNavbar||"static"===this._config.display)&&(z.setDataAttribute(this._menu,"popper","static"),t.modifiers=[{name:"applyStyles",enabled:!1}]),{...t,...m(this._config.popperConfig,[t])}}_selectMenuItem({key:t,target:e}){const n=q.find(".dropdown-menu .dropdown-item:not(.disabled):not(:disabled)",this._menu).filter((t=>a(t)));n.length&&y(n,e,t===En,!n.includes(e)).focus()}static jQueryInterface(t){return this.each((function(){const e=Wn.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t])throw new TypeError(`No method named "${t}"`);e[t]()}}))}static clearMenus(t){if(2===t.button||"keyup"===t.type&&"Tab"!==t.key)return;const e=q.find(jn);for(const n of e){const e=Wn.getInstance(n);if(!e||!1===e._config.autoClose)continue;const i=t.composedPath(),r=i.includes(e._menu);if(i.includes(e._element)||"inside"===e._config.autoClose&&!r||"outside"===e._config.autoClose&&r)continue;if(e._menu.contains(t.target)&&("keyup"===t.type&&"Tab"===t.key||/input|select|option|textarea|form/i.test(t.target.tagName)))continue;const o={relatedTarget:e._element};"click"===t.type&&(o.clickEvent=t),e._completeHide(o)}}static dataApiKeydownHandler(t){const e=/input|textarea/i.test(t.target.tagName),n="Escape"===t.key,i=[Tn,En].includes(t.key);if(!i&&!n)return;if(e&&!n)return;t.preventDefault();const r=this.matches(Ln)?this:q.prev(this,Ln)[0]||q.next(this,Ln)[0]||q.findOne(Ln,t.delegateTarget.parentNode),o=Wn.getOrCreateInstance(r);if(i)return t.stopPropagation(),o.show(),void o._selectMenuItem(t);o._isShown()&&(t.stopPropagation(),o.hide(),r.focus())}}j.on(document,Dn,Ln,Wn.dataApiKeydownHandler),j.on(document,Dn,Nn,Wn.dataApiKeydownHandler),j.on(document,On,Wn.clearMenus),j.on(document,Mn,Wn.clearMenus),j.on(document,On,Ln,(function(t){t.preventDefault(),Wn.getOrCreateInstance(this).toggle()})),g(Wn);const Xn="backdrop",Yn="show",Un=`mousedown.bs.${Xn}`,Vn={className:"modal-backdrop",clickCallback:null,isAnimated:!1,isVisible:!0,rootElement:"body"},Qn={className:"string",clickCallback:"(function|null)",isAnimated:"boolean",isVisible:"boolean",rootElement:"(element|string)"};class Gn extends F{constructor(t){super(),this._config=this._getConfig(t),this._isAppended=!1,this._element=null}static get Default(){return Vn}static get DefaultType(){return Qn}static get NAME(){return Xn}show(t){if(!this._config.isVisible)return void m(t);this._append();const e=this._getElement();this._config.isAnimated&&h(e),e.classList.add(Yn),this._emulateAnimation((()=>{m(t)}))}hide(t){this._config.isVisible?(this._getElement().classList.remove(Yn),this._emulateAnimation((()=>{this.dispose(),m(t)}))):m(t)}dispose(){this._isAppended&&(j.off(this._element,Un),this._element.remove(),this._isAppended=!1)}_getElement(){if(!this._element){const t=document.createElement("div");t.className=this._config.className,this._config.isAnimated&&t.classList.add("fade"),this._element=t}return this._element}_configAfterMerge(t){return t.rootElement=s(t.rootElement),t}_append(){if(this._isAppended)return;const t=this._getElement();this._config.rootElement.append(t),j.on(t,Un,(()=>{m(this._config.clickCallback)})),this._isAppended=!0}_emulateAnimation(t){v(t,this._getElement(),this._config.isAnimated)}}const Kn=".bs.focustrap",Jn=`focusin${Kn}`,Zn=`keydown.tab${Kn}`,ti="backward",ei={autofocus:!0,trapElement:null},ni={autofocus:"boolean",trapElement:"element"};class ii extends F{constructor(t){super(),this._config=this._getConfig(t),this._isActive=!1,this._lastTabNavDirection=null}static get Default(){return ei}static get DefaultType(){return ni}static get NAME(){return"focustrap"}activate(){this._isActive||(this._config.autofocus&&this._config.trapElement.focus(),j.off(document,Kn),j.on(document,Jn,(t=>this._handleFocusin(t))),j.on(document,Zn,(t=>this._handleKeydown(t))),this._isActive=!0)}deactivate(){this._isActive&&(this._isActive=!1,j.off(document,Kn))}_handleFocusin(t){const{trapElement:e}=this._config;if(t.target===document||t.target===e||e.contains(t.target))return;const n=q.focusableChildren(e);0===n.length?e.focus():this._lastTabNavDirection===ti?n[n.length-1].focus():n[0].focus()}_handleKeydown(t){"Tab"===t.key&&(this._lastTabNavDirection=t.shiftKey?ti:"forward")}}const ri=".fixed-top, .fixed-bottom, .is-fixed, .sticky-top",oi=".sticky-top",si="padding-right",ai="margin-right";class li{constructor(){this._element=document.body}getWidth(){const t=document.documentElement.clientWidth;return Math.abs(window.innerWidth-t)}hide(){const t=this.getWidth();this._disableOverFlow(),this._setElementAttributes(this._element,si,(e=>e+t)),this._setElementAttributes(ri,si,(e=>e+t)),this._setElementAttributes(oi,ai,(e=>e-t))}reset(){this._resetElementAttributes(this._element,"overflow"),this._resetElementAttributes(this._element,si),this._resetElementAttributes(ri,si),this._resetElementAttributes(oi,ai)}isOverflowing(){return this.getWidth()>0}_disableOverFlow(){this._saveInitialAttribute(this._element,"overflow"),this._element.style.overflow="hidden"}_setElementAttributes(t,e,n){const i=this.getWidth();this._applyManipulationCallback(t,(t=>{if(t!==this._element&&window.innerWidth>t.clientWidth+i)return;this._saveInitialAttribute(t,e);const r=window.getComputedStyle(t).getPropertyValue(e);t.style.setProperty(e,`${n(Number.parseFloat(r))}px`)}))}_saveInitialAttribute(t,e){const n=t.style.getPropertyValue(e);n&&z.setDataAttribute(t,e,n)}_resetElementAttributes(t,e){this._applyManipulationCallback(t,(t=>{const n=z.getDataAttribute(t,e);null!==n?(z.removeDataAttribute(t,e),t.style.setProperty(e,n)):t.style.removeProperty(e)}))}_applyManipulationCallback(t,e){if(o(t))e(t);else for(const n of q.find(t,this._element))e(n)}}const ci=".bs.modal",ui=`hide${ci}`,hi=`hidePrevented${ci}`,fi=`hidden${ci}`,di=`show${ci}`,pi=`shown${ci}`,gi=`resize${ci}`,mi=`click.dismiss${ci}`,vi=`mousedown.dismiss${ci}`,yi=`keydown.dismiss${ci}`,_i=`click${ci}.data-api`,bi="modal-open",wi="show",xi="modal-static",Ti={backdrop:!0,focus:!0,keyboard:!0},Ei={backdrop:"(boolean|string)",focus:"boolean",keyboard:"boolean"};class Ci extends ${constructor(t,e){super(t,e),this._dialog=q.findOne(".modal-dialog",this._element),this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._isShown=!1,this._isTransitioning=!1,this._scrollBar=new li,this._addEventListeners()}static get Default(){return Ti}static get DefaultType(){return Ei}static get NAME(){return"modal"}toggle(t){return this._isShown?this.hide():this.show(t)}show(t){this._isShown||this._isTransitioning||j.trigger(this._element,di,{relatedTarget:t}).defaultPrevented||(this._isShown=!0,this._isTransitioning=!0,this._scrollBar.hide(),document.body.classList.add(bi),this._adjustDialog(),this._backdrop.show((()=>this._showElement(t))))}hide(){this._isShown&&!this._isTransitioning&&(j.trigger(this._element,ui).defaultPrevented||(this._isShown=!1,this._isTransitioning=!0,this._focustrap.deactivate(),this._element.classList.remove(wi),this._queueCallback((()=>this._hideModal()),this._element,this._isAnimated())))}dispose(){j.off(window,ci),j.off(this._dialog,ci),this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}handleUpdate(){this._adjustDialog()}_initializeBackDrop(){return new Gn({isVisible:Boolean(this._config.backdrop),isAnimated:this._isAnimated()})}_initializeFocusTrap(){return new ii({trapElement:this._element})}_showElement(t){document.body.contains(this._element)||document.body.append(this._element),this._element.style.display="block",this._element.removeAttribute("aria-hidden"),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.scrollTop=0;const e=q.findOne(".modal-body",this._dialog);e&&(e.scrollTop=0),h(this._element),this._element.classList.add(wi),this._queueCallback((()=>{this._config.focus&&this._focustrap.activate(),this._isTransitioning=!1,j.trigger(this._element,pi,{relatedTarget:t})}),this._dialog,this._isAnimated())}_addEventListeners(){j.on(this._element,yi,(t=>{"Escape"===t.key&&(this._config.keyboard?this.hide():this._triggerBackdropTransition())})),j.on(window,gi,(()=>{this._isShown&&!this._isTransitioning&&this._adjustDialog()})),j.on(this._element,vi,(t=>{j.one(this._element,mi,(e=>{this._element===t.target&&this._element===e.target&&("static"!==this._config.backdrop?this._config.backdrop&&this.hide():this._triggerBackdropTransition())}))}))}_hideModal(){this._element.style.display="none",this._element.setAttribute("aria-hidden",!0),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._isTransitioning=!1,this._backdrop.hide((()=>{document.body.classList.remove(bi),this._resetAdjustments(),this._scrollBar.reset(),j.trigger(this._element,fi)}))}_isAnimated(){return this._element.classList.contains("fade")}_triggerBackdropTransition(){if(j.trigger(this._element,hi).defaultPrevented)return;const t=this._element.scrollHeight>document.documentElement.clientHeight,e=this._element.style.overflowY;"hidden"===e||this._element.classList.contains(xi)||(t||(this._element.style.overflowY="hidden"),this._element.classList.add(xi),this._queueCallback((()=>{this._element.classList.remove(xi),this._queueCallback((()=>{this._element.style.overflowY=e}),this._dialog)}),this._dialog),this._element.focus())}_adjustDialog(){const t=this._element.scrollHeight>document.documentElement.clientHeight,e=this._scrollBar.getWidth(),n=e>0;if(n&&!t){const t=p()?"paddingLeft":"paddingRight";this._element.style[t]=`${e}px`}if(!n&&t){const t=p()?"paddingRight":"paddingLeft";this._element.style[t]=`${e}px`}}_resetAdjustments(){this._element.style.paddingLeft="",this._element.style.paddingRight=""}static jQueryInterface(t,e){return this.each((function(){const n=Ci.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===n[t])throw new TypeError(`No method named "${t}"`);n[t](e)}}))}}j.on(document,_i,'[data-bs-toggle="modal"]',(function(t){const e=q.getElementFromSelector(this);["A","AREA"].includes(this.tagName)&&t.preventDefault(),j.one(e,di,(t=>{t.defaultPrevented||j.one(e,fi,(()=>{a(this)&&this.focus()}))}));const n=q.findOne(".modal.show");n&&Ci.getInstance(n).hide(),Ci.getOrCreateInstance(e).toggle(this)})),B(Ci),g(Ci);const Si=".bs.offcanvas",ki=".data-api",Ai=`load${Si}${ki}`,Oi="show",Di="showing",Mi="hiding",Pi=".offcanvas.show",Li=`show${Si}`,ji=`shown${Si}`,Ni=`hide${Si}`,Ii=`hidePrevented${Si}`,Ri=`hidden${Si}`,zi=`resize${Si}`,Fi=`click${Si}${ki}`,$i=`keydown.dismiss${Si}`,Hi={backdrop:!0,keyboard:!0,scroll:!1},qi={backdrop:"(boolean|string)",keyboard:"boolean",scroll:"boolean"};class Bi extends ${constructor(t,e){super(t,e),this._isShown=!1,this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._addEventListeners()}static get Default(){return Hi}static get DefaultType(){return qi}static get NAME(){return"offcanvas"}toggle(t){return this._isShown?this.hide():this.show(t)}show(t){this._isShown||j.trigger(this._element,Li,{relatedTarget:t}).defaultPrevented||(this._isShown=!0,this._backdrop.show(),this._config.scroll||(new li).hide(),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.classList.add(Di),this._queueCallback((()=>{this._config.scroll&&!this._config.backdrop||this._focustrap.activate(),this._element.classList.add(Oi),this._element.classList.remove(Di),j.trigger(this._element,ji,{relatedTarget:t})}),this._element,!0))}hide(){this._isShown&&(j.trigger(this._element,Ni).defaultPrevented||(this._focustrap.deactivate(),this._element.blur(),this._isShown=!1,this._element.classList.add(Mi),this._backdrop.hide(),this._queueCallback((()=>{this._element.classList.remove(Oi,Mi),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._config.scroll||(new li).reset(),j.trigger(this._element,Ri)}),this._element,!0)))}dispose(){this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}_initializeBackDrop(){const t=Boolean(this._config.backdrop);return new Gn({className:"offcanvas-backdrop",isVisible:t,isAnimated:!0,rootElement:this._element.parentNode,clickCallback:t?()=>{"static"!==this._config.backdrop?this.hide():j.trigger(this._element,Ii)}:null})}_initializeFocusTrap(){return new ii({trapElement:this._element})}_addEventListeners(){j.on(this._element,$i,(t=>{"Escape"===t.key&&(this._config.keyboard?this.hide():j.trigger(this._element,Ii))}))}static jQueryInterface(t){return this.each((function(){const e=Bi.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t]||t.startsWith("_")||"constructor"===t)throw new TypeError(`No method named "${t}"`);e[t](this)}}))}}j.on(document,Fi,'[data-bs-toggle="offcanvas"]',(function(t){const e=q.getElementFromSelector(this);if(["A","AREA"].includes(this.tagName)&&t.preventDefault(),l(this))return;j.one(e,Ri,(()=>{a(this)&&this.focus()}));const n=q.findOne(Pi);n&&n!==e&&Bi.getInstance(n).hide(),Bi.getOrCreateInstance(e).toggle(this)})),j.on(window,Ai,(()=>{for(const t of q.find(Pi))Bi.getOrCreateInstance(t).show()})),j.on(window,zi,(()=>{for(const t of q.find("[aria-modal][class*=show][class*=offcanvas-]"))"fixed"!==getComputedStyle(t).position&&Bi.getOrCreateInstance(t).hide()})),B(Bi),g(Bi);const Wi={"*":["class","dir","id","lang","role",/^aria-[\w-]*$/i],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],div:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","srcset","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]},Xi=new Set(["background","cite","href","itemtype","longdesc","poster","src","xlink:href"]),Yi=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:/?#]*(?:[/?#]|$))/i,Ui=(t,e)=>{const n=t.nodeName.toLowerCase();return e.includes(n)?!Xi.has(n)||Boolean(Yi.test(t.nodeValue)):e.filter((t=>t instanceof RegExp)).some((t=>t.test(n)))},Vi={allowList:Wi,content:{},extraClass:"",html:!1,sanitize:!0,sanitizeFn:null,template:"<div></div>"},Qi={allowList:"object",content:"object",extraClass:"(string|function)",html:"boolean",sanitize:"boolean",sanitizeFn:"(null|function)",template:"string"},Gi={entry:"(string|element|function|null)",selector:"(string|element)"};class Ki extends F{constructor(t){super(),this._config=this._getConfig(t)}static get Default(){return Vi}static get DefaultType(){return Qi}static get NAME(){return"TemplateFactory"}getContent(){return Object.values(this._config.content).map((t=>this._resolvePossibleFunction(t))).filter(Boolean)}hasContent(){return this.getContent().length>0}changeContent(t){return this._checkContent(t),this._config.content={...this._config.content,...t},this}toHtml(){const t=document.createElement("div");t.innerHTML=this._maybeSanitize(this._config.template);for(const[e,n]of Object.entries(this._config.content))this._setContent(t,n,e);const e=t.children[0],n=this._resolvePossibleFunction(this._config.extraClass);return n&&e.classList.add(...n.split(" ")),e}_typeCheckConfig(t){super._typeCheckConfig(t),this._checkContent(t.content)}_checkContent(t){for(const[e,n]of Object.entries(t))super._typeCheckConfig({selector:e,entry:n},Gi)}_setContent(t,e,n){const i=q.findOne(n,t);i&&((e=this._resolvePossibleFunction(e))?o(e)?this._putElementInTemplate(s(e),i):this._config.html?i.innerHTML=this._maybeSanitize(e):i.textContent=e:i.remove())}_maybeSanitize(t){return this._config.sanitize?function(t,e,n){if(!t.length)return t;if(n&&"function"==typeof n)return n(t);const i=(new window.DOMParser).parseFromString(t,"text/html"),r=[].concat(...i.body.querySelectorAll("*"));for(const t of r){const n=t.nodeName.toLowerCase();if(!Object.keys(e).includes(n)){t.remove();continue}const i=[].concat(...t.attributes),r=[].concat(e["*"]||[],e[n]||[]);for(const e of i)Ui(e,r)||t.removeAttribute(e.nodeName)}return i.body.innerHTML}(t,this._config.allowList,this._config.sanitizeFn):t}_resolvePossibleFunction(t){return m(t,[this])}_putElementInTemplate(t,e){if(this._config.html)return e.innerHTML="",void e.append(t);e.textContent=t.textContent}}const Ji=new Set(["sanitize","allowList","sanitizeFn"]),Zi="fade",tr="show",er=".modal",nr="hide.bs.modal",ir="hover",rr="focus",or={AUTO:"auto",TOP:"top",RIGHT:p()?"left":"right",BOTTOM:"bottom",LEFT:p()?"right":"left"},sr={allowList:Wi,animation:!0,boundary:"clippingParents",container:!1,customClass:"",delay:0,fallbackPlacements:["top","right","bottom","left"],html:!1,offset:[0,6],placement:"top",popperConfig:null,sanitize:!0,sanitizeFn:null,selector:!1,template:'<div class="tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',title:"",trigger:"hover focus"},ar={allowList:"object",animation:"boolean",boundary:"(string|element)",container:"(string|element|boolean)",customClass:"(string|function)",delay:"(number|object)",fallbackPlacements:"array",html:"boolean",offset:"(array|string|function)",placement:"(string|function)",popperConfig:"(null|object|function)",sanitize:"boolean",sanitizeFn:"(null|function)",selector:"(string|boolean)",template:"string",title:"(string|element|function)",trigger:"string"};class lr extends ${constructor(t,e){if(void 0===_n)throw new TypeError("Bootstrap's tooltips require Popper (https://popper.js.org)");super(t,e),this._isEnabled=!0,this._timeout=0,this._isHovered=null,this._activeTrigger={},this._popper=null,this._templateFactory=null,this._newContent=null,this.tip=null,this._setListeners(),this._config.selector||this._fixTitle()}static get Default(){return sr}static get DefaultType(){return ar}static get NAME(){return"tooltip"}enable(){this._isEnabled=!0}disable(){this._isEnabled=!1}toggleEnabled(){this._isEnabled=!this._isEnabled}toggle(){this._isEnabled&&(this._activeTrigger.click=!this._activeTrigger.click,this._isShown()?this._leave():this._enter())}dispose(){clearTimeout(this._timeout),j.off(this._element.closest(er),nr,this._hideModalHandler),this._element.getAttribute("data-bs-original-title")&&this._element.setAttribute("title",this._element.getAttribute("data-bs-original-title")),this._disposePopper(),super.dispose()}show(){if("none"===this._element.style.display)throw new Error("Please use show on visible elements");if(!this._isWithContent()||!this._isEnabled)return;const t=j.trigger(this._element,this.constructor.eventName("show")),e=(c(this._element)||this._element.ownerDocument.documentElement).contains(this._element);if(t.defaultPrevented||!e)return;this._disposePopper();const n=this._getTipElement();this._element.setAttribute("aria-describedby",n.getAttribute("id"));const{container:i}=this._config;if(this._element.ownerDocument.documentElement.contains(this.tip)||(i.append(n),j.trigger(this._element,this.constructor.eventName("inserted"))),this._popper=this._createPopper(n),n.classList.add(tr),"ontouchstart"in document.documentElement)for(const t of[].concat(...document.body.children))j.on(t,"mouseover",u);this._queueCallback((()=>{j.trigger(this._element,this.constructor.eventName("shown")),!1===this._isHovered&&this._leave(),this._isHovered=!1}),this.tip,this._isAnimated())}hide(){if(this._isShown()&&!j.trigger(this._element,this.constructor.eventName("hide")).defaultPrevented){if(this._getTipElement().classList.remove(tr),"ontouchstart"in document.documentElement)for(const t of[].concat(...document.body.children))j.off(t,"mouseover",u);this._activeTrigger.click=!1,this._activeTrigger[rr]=!1,this._activeTrigger[ir]=!1,this._isHovered=null,this._queueCallback((()=>{this._isWithActiveTrigger()||(this._isHovered||this._disposePopper(),this._element.removeAttribute("aria-describedby"),j.trigger(this._element,this.constructor.eventName("hidden")))}),this.tip,this._isAnimated())}}update(){this._popper&&this._popper.update()}_isWithContent(){return Boolean(this._getTitle())}_getTipElement(){return this.tip||(this.tip=this._createTipElement(this._newContent||this._getContentForTemplate())),this.tip}_createTipElement(t){const e=this._getTemplateFactory(t).toHtml();if(!e)return null;e.classList.remove(Zi,tr),e.classList.add(`bs-${this.constructor.NAME}-auto`);const n=(t=>{do{t+=Math.floor(1e6*Math.random())}while(document.getElementById(t));return t})(this.constructor.NAME).toString();return e.setAttribute("id",n),this._isAnimated()&&e.classList.add(Zi),e}setContent(t){this._newContent=t,this._isShown()&&(this._disposePopper(),this.show())}_getTemplateFactory(t){return this._templateFactory?this._templateFactory.changeContent(t):this._templateFactory=new Ki({...this._config,content:t,extraClass:this._resolvePossibleFunction(this._config.customClass)}),this._templateFactory}_getContentForTemplate(){return{".tooltip-inner":this._getTitle()}}_getTitle(){return this._resolvePossibleFunction(this._config.title)||this._element.getAttribute("data-bs-original-title")}_initializeOnDelegatedTarget(t){return this.constructor.getOrCreateInstance(t.delegateTarget,this._getDelegateConfig())}_isAnimated(){return this._config.animation||this.tip&&this.tip.classList.contains(Zi)}_isShown(){return this.tip&&this.tip.classList.contains(tr)}_createPopper(t){const e=m(this._config.placement,[this,t,this._element]),n=or[e.toUpperCase()];return yn(this._element,t,this._getPopperConfig(n))}_getOffset(){const{offset:t}=this._config;return"string"==typeof t?t.split(",").map((t=>Number.parseInt(t,10))):"function"==typeof t?e=>t(e,this._element):t}_resolvePossibleFunction(t){return m(t,[this._element])}_getPopperConfig(t){const e={placement:t,modifiers:[{name:"flip",options:{fallbackPlacements:this._config.fallbackPlacements}},{name:"offset",options:{offset:this._getOffset()}},{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"arrow",options:{element:`.${this.constructor.NAME}-arrow`}},{name:"preSetPlacement",enabled:!0,phase:"beforeMain",fn:t=>{this._getTipElement().setAttribute("data-popper-placement",t.state.placement)}}]};return{...e,...m(this._config.popperConfig,[e])}}_setListeners(){const t=this._config.trigger.split(" ");for(const e of t)if("click"===e)j.on(this._element,this.constructor.eventName("click"),this._config.selector,(t=>{this._initializeOnDelegatedTarget(t).toggle()}));else if("manual"!==e){const t=e===ir?this.constructor.eventName("mouseenter"):this.constructor.eventName("focusin"),n=e===ir?this.constructor.eventName("mouseleave"):this.constructor.eventName("focusout");j.on(this._element,t,this._config.selector,(t=>{const e=this._initializeOnDelegatedTarget(t);e._activeTrigger["focusin"===t.type?rr:ir]=!0,e._enter()})),j.on(this._element,n,this._config.selector,(t=>{const e=this._initializeOnDelegatedTarget(t);e._activeTrigger["focusout"===t.type?rr:ir]=e._element.contains(t.relatedTarget),e._leave()}))}this._hideModalHandler=()=>{this._element&&this.hide()},j.on(this._element.closest(er),nr,this._hideModalHandler)}_fixTitle(){const t=this._element.getAttribute("title");t&&(this._element.getAttribute("aria-label")||this._element.textContent.trim()||this._element.setAttribute("aria-label",t),this._element.setAttribute("data-bs-original-title",t),this._element.removeAttribute("title"))}_enter(){this._isShown()||this._isHovered?this._isHovered=!0:(this._isHovered=!0,this._setTimeout((()=>{this._isHovered&&this.show()}),this._config.delay.show))}_leave(){this._isWithActiveTrigger()||(this._isHovered=!1,this._setTimeout((()=>{this._isHovered||this.hide()}),this._config.delay.hide))}_setTimeout(t,e){clearTimeout(this._timeout),this._timeout=setTimeout(t,e)}_isWithActiveTrigger(){return Object.values(this._activeTrigger).includes(!0)}_getConfig(t){const e=z.getDataAttributes(this._element);for(const t of Object.keys(e))Ji.has(t)&&delete e[t];return t={...e,..."object"==typeof t&&t?t:{}},t=this._mergeConfigObj(t),t=this._configAfterMerge(t),this._typeCheckConfig(t),t}_configAfterMerge(t){return t.container=!1===t.container?document.body:s(t.container),"number"==typeof t.delay&&(t.delay={show:t.delay,hide:t.delay}),"number"==typeof t.title&&(t.title=t.title.toString()),"number"==typeof t.content&&(t.content=t.content.toString()),t}_getDelegateConfig(){const t={};for(const[e,n]of Object.entries(this._config))this.constructor.Default[e]!==n&&(t[e]=n);return t.selector=!1,t.trigger="manual",t}_disposePopper(){this._popper&&(this._popper.destroy(),this._popper=null),this.tip&&(this.tip.remove(),this.tip=null)}static jQueryInterface(t){return this.each((function(){const e=lr.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t])throw new TypeError(`No method named "${t}"`);e[t]()}}))}}g(lr);const cr={...lr.Default,content:"",offset:[0,8],placement:"right",template:'<div class="popover" role="tooltip"><div class="popover-arrow"></div><h3 class="popover-header"></h3><div class="popover-body"></div></div>',trigger:"click"},ur={...lr.DefaultType,content:"(null|string|element|function)"};class hr extends lr{static get Default(){return cr}static get DefaultType(){return ur}static get NAME(){return"popover"}_isWithContent(){return this._getTitle()||this._getContent()}_getContentForTemplate(){return{".popover-header":this._getTitle(),".popover-body":this._getContent()}}_getContent(){return this._resolvePossibleFunction(this._config.content)}static jQueryInterface(t){return this.each((function(){const e=hr.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t])throw new TypeError(`No method named "${t}"`);e[t]()}}))}}g(hr);const fr=".bs.scrollspy",dr=`activate${fr}`,pr=`click${fr}`,gr=`load${fr}.data-api`,mr="active",vr="[href]",yr=".nav-link",_r=`${yr}, .nav-item > ${yr}, .list-group-item`,br={offset:null,rootMargin:"0px 0px -25%",smoothScroll:!1,target:null,threshold:[.1,.5,1]},wr={offset:"(number|null)",rootMargin:"string",smoothScroll:"boolean",target:"element",threshold:"array"};class xr extends ${constructor(t,e){super(t,e),this._targetLinks=new Map,this._observableSections=new Map,this._rootElement="visible"===getComputedStyle(this._element).overflowY?null:this._element,this._activeTarget=null,this._observer=null,this._previousScrollData={visibleEntryTop:0,parentScrollTop:0},this.refresh()}static get Default(){return br}static get DefaultType(){return wr}static get NAME(){return"scrollspy"}refresh(){this._initializeTargetsAndObservables(),this._maybeEnableSmoothScroll(),this._observer?this._observer.disconnect():this._observer=this._getNewObserver();for(const t of this._observableSections.values())this._observer.observe(t)}dispose(){this._observer.disconnect(),super.dispose()}_configAfterMerge(t){return t.target=s(t.target)||document.body,t.rootMargin=t.offset?`${t.offset}px 0px -30%`:t.rootMargin,"string"==typeof t.threshold&&(t.threshold=t.threshold.split(",").map((t=>Number.parseFloat(t)))),t}_maybeEnableSmoothScroll(){this._config.smoothScroll&&(j.off(this._config.target,pr),j.on(this._config.target,pr,vr,(t=>{const e=this._observableSections.get(t.target.hash);if(e){t.preventDefault();const n=this._rootElement||window,i=e.offsetTop-this._element.offsetTop;if(n.scrollTo)return void n.scrollTo({top:i,behavior:"smooth"});n.scrollTop=i}})))}_getNewObserver(){const t={root:this._rootElement,threshold:this._config.threshold,rootMargin:this._config.rootMargin};return new IntersectionObserver((t=>this._observerCallback(t)),t)}_observerCallback(t){const e=t=>this._targetLinks.get(`#${t.target.id}`),n=t=>{this._previousScrollData.visibleEntryTop=t.target.offsetTop,this._process(e(t))},i=(this._rootElement||document.documentElement).scrollTop,r=i>=this._previousScrollData.parentScrollTop;this._previousScrollData.parentScrollTop=i;for(const o of t){if(!o.isIntersecting){this._activeTarget=null,this._clearActiveClass(e(o));continue}const t=o.target.offsetTop>=this._previousScrollData.visibleEntryTop;if(r&&t){if(n(o),!i)return}else r||t||n(o)}}_initializeTargetsAndObservables(){this._targetLinks=new Map,this._observableSections=new Map;const t=q.find(vr,this._config.target);for(const e of t){if(!e.hash||l(e))continue;const t=q.findOne(decodeURI(e.hash),this._element);a(t)&&(this._targetLinks.set(decodeURI(e.hash),e),this._observableSections.set(e.hash,t))}}_process(t){this._activeTarget!==t&&(this._clearActiveClass(this._config.target),this._activeTarget=t,t.classList.add(mr),this._activateParents(t),j.trigger(this._element,dr,{relatedTarget:t}))}_activateParents(t){if(t.classList.contains("dropdown-item"))q.findOne(".dropdown-toggle",t.closest(".dropdown")).classList.add(mr);else for(const e of q.parents(t,".nav, .list-group"))for(const t of q.prev(e,_r))t.classList.add(mr)}_clearActiveClass(t){t.classList.remove(mr);const e=q.find(`${vr}.${mr}`,t);for(const t of e)t.classList.remove(mr)}static jQueryInterface(t){return this.each((function(){const e=xr.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t]||t.startsWith("_")||"constructor"===t)throw new TypeError(`No method named "${t}"`);e[t]()}}))}}j.on(window,gr,(()=>{for(const t of q.find('[data-bs-spy="scroll"]'))xr.getOrCreateInstance(t)})),g(xr);const Tr=".bs.tab",Er=`hide${Tr}`,Cr=`hidden${Tr}`,Sr=`show${Tr}`,kr=`shown${Tr}`,Ar=`click${Tr}`,Or=`keydown${Tr}`,Dr=`load${Tr}`,Mr="ArrowLeft",Pr="ArrowRight",Lr="ArrowUp",jr="ArrowDown",Nr="Home",Ir="End",Rr="active",zr="fade",Fr="show",$r=".dropdown-toggle",Hr=`:not(${$r})`,qr='[data-bs-toggle="tab"], [data-bs-toggle="pill"], [data-bs-toggle="list"]',Br=`.nav-link${Hr}, .list-group-item${Hr}, [role="tab"]${Hr}, ${qr}`,Wr=`.${Rr}[data-bs-toggle="tab"], .${Rr}[data-bs-toggle="pill"], .${Rr}[data-bs-toggle="list"]`;class Xr extends ${constructor(t){super(t),this._parent=this._element.closest('.list-group, .nav, [role="tablist"]'),this._parent&&(this._setInitialAttributes(this._parent,this._getChildren()),j.on(this._element,Or,(t=>this._keydown(t))))}static get NAME(){return"tab"}show(){const t=this._element;if(this._elemIsActive(t))return;const e=this._getActiveElem(),n=e?j.trigger(e,Er,{relatedTarget:t}):null;j.trigger(t,Sr,{relatedTarget:e}).defaultPrevented||n&&n.defaultPrevented||(this._deactivate(e,t),this._activate(t,e))}_activate(t,e){t&&(t.classList.add(Rr),this._activate(q.getElementFromSelector(t)),this._queueCallback((()=>{"tab"===t.getAttribute("role")?(t.removeAttribute("tabindex"),t.setAttribute("aria-selected",!0),this._toggleDropDown(t,!0),j.trigger(t,kr,{relatedTarget:e})):t.classList.add(Fr)}),t,t.classList.contains(zr)))}_deactivate(t,e){t&&(t.classList.remove(Rr),t.blur(),this._deactivate(q.getElementFromSelector(t)),this._queueCallback((()=>{"tab"===t.getAttribute("role")?(t.setAttribute("aria-selected",!1),t.setAttribute("tabindex","-1"),this._toggleDropDown(t,!1),j.trigger(t,Cr,{relatedTarget:e})):t.classList.remove(Fr)}),t,t.classList.contains(zr)))}_keydown(t){if(![Mr,Pr,Lr,jr,Nr,Ir].includes(t.key))return;t.stopPropagation(),t.preventDefault();const e=this._getChildren().filter((t=>!l(t)));let n;if([Nr,Ir].includes(t.key))n=e[t.key===Nr?0:e.length-1];else{const i=[Pr,jr].includes(t.key);n=y(e,t.target,i,!0)}n&&(n.focus({preventScroll:!0}),Xr.getOrCreateInstance(n).show())}_getChildren(){return q.find(Br,this._parent)}_getActiveElem(){return this._getChildren().find((t=>this._elemIsActive(t)))||null}_setInitialAttributes(t,e){this._setAttributeIfNotExists(t,"role","tablist");for(const t of e)this._setInitialAttributesOnChild(t)}_setInitialAttributesOnChild(t){t=this._getInnerElement(t);const e=this._elemIsActive(t),n=this._getOuterElement(t);t.setAttribute("aria-selected",e),n!==t&&this._setAttributeIfNotExists(n,"role","presentation"),e||t.setAttribute("tabindex","-1"),this._setAttributeIfNotExists(t,"role","tab"),this._setInitialAttributesOnTargetPanel(t)}_setInitialAttributesOnTargetPanel(t){const e=q.getElementFromSelector(t);e&&(this._setAttributeIfNotExists(e,"role","tabpanel"),t.id&&this._setAttributeIfNotExists(e,"aria-labelledby",`${t.id}`))}_toggleDropDown(t,e){const n=this._getOuterElement(t);if(!n.classList.contains("dropdown"))return;const i=(t,i)=>{const r=q.findOne(t,n);r&&r.classList.toggle(i,e)};i($r,Rr),i(".dropdown-menu",Fr),n.setAttribute("aria-expanded",e)}_setAttributeIfNotExists(t,e,n){t.hasAttribute(e)||t.setAttribute(e,n)}_elemIsActive(t){return t.classList.contains(Rr)}_getInnerElement(t){return t.matches(Br)?t:q.findOne(Br,t)}_getOuterElement(t){return t.closest(".nav-item, .list-group-item")||t}static jQueryInterface(t){return this.each((function(){const e=Xr.getOrCreateInstance(this);if("string"==typeof t){if(void 0===e[t]||t.startsWith("_")||"constructor"===t)throw new TypeError(`No method named "${t}"`);e[t]()}}))}}j.on(document,Ar,qr,(function(t){["A","AREA"].includes(this.tagName)&&t.preventDefault(),l(this)||Xr.getOrCreateInstance(this).show()})),j.on(window,Dr,(()=>{for(const t of q.find(Wr))Xr.getOrCreateInstance(t)})),g(Xr);const Yr=".bs.toast",Ur=`mouseover${Yr}`,Vr=`mouseout${Yr}`,Qr=`focusin${Yr}`,Gr=`focusout${Yr}`,Kr=`hide${Yr}`,Jr=`hidden${Yr}`,Zr=`show${Yr}`,to=`shown${Yr}`,eo="hide",no="show",io="showing",ro={animation:"boolean",autohide:"boolean",delay:"number"},oo={animation:!0,autohide:!0,delay:5e3};class so extends ${constructor(t,e){super(t,e),this._timeout=null,this._hasMouseInteraction=!1,this._hasKeyboardInteraction=!1,this._setListeners()}static get Default(){return oo}static get DefaultType(){return ro}static get NAME(){return"toast"}show(){j.trigger(this._element,Zr).defaultPrevented||(this._clearTimeout(),this._config.animation&&this._element.classList.add("fade"),this._element.classList.remove(eo),h(this._element),this._element.classList.add(no,io),this._queueCallback((()=>{this._element.classList.remove(io),j.trigger(this._element,to),this._maybeScheduleHide()}),this._element,this._config.animation))}hide(){this.isShown()&&(j.trigger(this._element,Kr).defaultPrevented||(this._element.classList.add(io),this._queueCallback((()=>{this._element.classList.add(eo),this._element.classList.remove(io,no),j.trigger(this._element,Jr)}),this._element,this._config.animation)))}dispose(){this._clearTimeout(),this.isShown()&&this._element.classList.remove(no),super.dispose()}isShown(){return this._element.classList.contains(no)}_maybeScheduleHide(){this._config.autohide&&(this._hasMouseInteraction||this._hasKeyboardInteraction||(this._timeout=setTimeout((()=>{this.hide()}),this._config.delay)))}_onInteraction(t,e){switch(t.type){case"mouseover":case"mouseout":this._hasMouseInteraction=e;break;case"focusin":case"focusout":this._hasKeyboardInteraction=e}if(e)return void this._clearTimeout();const n=t.relatedTarget;this._element===n||this._element.contains(n)||this._maybeScheduleHide()}_setListeners(){j.on(this._element,Ur,(t=>this._onInteraction(t,!0))),j.on(this._element,Vr,(t=>this._onInteraction(t,!1))),j.on(this._element,Qr,(t=>this._onInteraction(t,!0))),j.on(this._element,Gr,(t=>this._onInteraction(t,!1)))}_clearTimeout(){clearTimeout(this._timeout),this._timeout=null}static jQueryInterface(t){return this.each((function(){const e=so.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t])throw new TypeError(`No method named "${t}"`);e[t](this)}}))}}return B(so),g(so),{Alert:U,Button:Q,Carousel:kt,Collapse:Ht,Dropdown:Wn,Modal:Ci,Offcanvas:Bi,Popover:hr,ScrollSpy:xr,Tab:Xr,Toast:so,Tooltip:lr}})),function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t="undefined"!=typeof globalThis?globalThis:t||self).SplitType=e()}(this,(function(){"use strict";function t(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}function e(e,n,i){return n&&t(e.prototype,n),i&&t(e,i),e}function n(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function i(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,i)}return n}function r(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?i(Object(r),!0).forEach((function(e){n(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function o(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t)){var n=[],i=!0,r=!1,o=void 0;try{for(var s,a=t[Symbol.iterator]();!(i=(s=a.next()).done)&&(n.push(s.value),!e||n.length!==e);i=!0);}catch(t){r=!0,o=t}finally{try{i||null==a.return||a.return()}finally{if(r)throw o}}return n}}(t,e)||a(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function s(t){return function(t){if(Array.isArray(t))return l(t)}(t)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(t)||a(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function a(t,e){if(t){if("string"==typeof t)return l(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?l(t,e):void 0}}function l(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,i=new Array(e);n<e;n++)i[n]=t[n];return i}function c(t,e){return Object.getOwnPropertyNames(Object(t)).reduce((function(n,i){var r=Object.getOwnPropertyDescriptor(Object(t),i),o=Object.getOwnPropertyDescriptor(Object(e),i);return Object.defineProperty(n,i,o||r)}),{})}function u(t){return"string"==typeof t}function h(t){return Array.isArray(t)}function f(){var t,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=c(e);return void 0!==n.types?t=n.types:void 0!==n.split&&(t=n.split),void 0!==t&&(n.types=(u(t)||h(t)?String(t):"").split(",").map((function(t){return String(t).trim()})).filter((function(t){return/((line)|(word)|(char))/i.test(t)}))),(n.absolute||n.position)&&(n.absolute=n.absolute||/absolute/.test(e.position)),n}function d(t){var e=u(t)||h(t)?String(t):"";return{none:!e,lines:/line/i.test(e),words:/word/i.test(e),chars:/char/i.test(e)}}function p(t){return null!==t&&"object"==typeof t}function g(t){return p(t)&&/^(1|3|11)$/.test(t.nodeType)}function m(t){return h(t)?t:null==t?[]:function(t){return p(t)&&function(t){return"number"==typeof t&&t>-1&&t%1==0}(t.length)}(t)?Array.prototype.slice.call(t):[t]}function v(t){var e=t;return u(t)&&(e=/^(#[a-z]\w+)$/.test(t.trim())?document.getElementById(t.trim().slice(1)):document.querySelectorAll(t)),m(e).reduce((function(t,e){return[].concat(s(t),s(m(e).filter(g)))}),[])}!function(){function t(){for(var t=arguments.length,e=0;e<t;e++){var n=e<0||arguments.length<=e?void 0:arguments[e];1===n.nodeType||11===n.nodeType?this.appendChild(n):this.appendChild(document.createTextNode(String(n)))}}function e(){for(;this.lastChild;)this.removeChild(this.lastChild);arguments.length&&this.append.apply(this,arguments)}function n(){for(var t=this.parentNode,e=arguments.length,n=new Array(e),i=0;i<e;i++)n[i]=arguments[i];var r=n.length;if(t)for(r||t.removeChild(this);r--;){var o=n[r];"object"!=typeof o?o=this.ownerDocument.createTextNode(o):o.parentNode&&o.parentNode.removeChild(o),r?t.insertBefore(this.previousSibling,o):t.replaceChild(o,this)}}"undefined"!=typeof Element&&(Element.prototype.append||(Element.prototype.append=t,DocumentFragment.prototype.append=t),Element.prototype.replaceChildren||(Element.prototype.replaceChildren=e,DocumentFragment.prototype.replaceChildren=e),Element.prototype.replaceWith||(Element.prototype.replaceWith=n,DocumentFragment.prototype.replaceWith=n))}();var y=Object.entries,_="_splittype",b={},w=0;function x(t,e,n){if(!p(t))return console.warn("[data.set] owner is not an object"),null;var i=t[_]||(t[_]=++w),o=b[i]||(b[i]={});return void 0===n?e&&Object.getPrototypeOf(e)===Object.prototype&&(b[i]=r(r({},o),e)):void 0!==e&&(o[e]=n),n}function T(t,e){var n=p(t)?t[_]:null,i=n&&b[n]||{};return void 0===e?i:i[e]}function E(t){var e=t&&t[_];e&&(delete t[e],delete b[e])}var C="\\ud800-\\udfff",S="\\u0300-\\u036f\\ufe20-\\ufe23",k="\\u20d0-\\u20f0",A="\\ufe0e\\ufe0f",O="[".concat(C,"]"),D="[".concat(S).concat(k,"]"),M="\\ud83c[\\udffb-\\udfff]",P="(?:".concat(D,"|").concat(M,")"),L="[^".concat(C,"]"),j="(?:\\ud83c[\\udde6-\\uddff]){2}",N="[\\ud800-\\udbff][\\udc00-\\udfff]",I="".concat(P,"?"),R="[".concat(A,"]?"),z=R+I+"(?:\\u200d(?:"+[L,j,N].join("|")+")"+R+I+")*",F="(?:".concat(["".concat(L).concat(D,"?"),D,j,N,O].join("|"),"\n)"),$=RegExp("".concat(M,"(?=").concat(M,")|").concat(F).concat(z),"g"),H=RegExp("[".concat(["\\u200d",C,S,k,A].join(""),"]"));function q(t){return H.test(t)}function B(t,e){var n=document.createElement(t);return e?(Object.keys(e).forEach((function(t){var i=e[t],r=u(i)?i.trim():i;null!==r&&""!==r&&("children"===t?n.append.apply(n,s(m(r))):n.setAttribute(t,r))})),n):n}var W={splitClass:"",lineClass:"line",wordClass:"word",charClass:"char",types:["lines","words","chars"],absolute:!1,tagName:"div"};function X(t,e){var n,i=d((e=c(W,e)).types),r=e.tagName,o=t.nodeValue,a=document.createDocumentFragment(),l=[];return/^\s/.test(o)&&a.append(" "),n=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:" ";return(t?String(t):"").trim().replace(/\s+/g," ").split(e)}(o).reduce((function(t,n,o,c){var h,f;return i.chars&&(f=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return(t=function(t){return null==t?"":String(t)}(t))&&u(t)&&!e&&q(t)?function(t){return q(t)?function(t){return t.match($)||[]}(t):function(t){return t.split("")}(t)}(t):t.split(e)}(n).map((function(t){var n=B(r,{class:"".concat(e.splitClass," ").concat(e.charClass),style:"display: inline-block;",children:t});return x(n,"isChar",!0),l=[].concat(s(l),[n]),n}))),i.words||i.lines?(x(h=B(r,{class:"".concat(e.wordClass," ").concat(e.splitClass),style:"display: inline-block; ".concat(i.words&&e.absolute?"position: relative;":""),children:i.chars?f:n}),{isWord:!0,isWordStart:!0,isWordEnd:!0}),a.appendChild(h)):f.forEach((function(t){a.appendChild(t)})),o<c.length-1&&a.append(" "),i.words?t.concat(h):t}),[]),/\s$/.test(o)&&a.append(" "),t.replaceWith(a),{words:n,chars:l}}function Y(t,e){var n=t.nodeType,i={words:[],chars:[]};if(!/(1|3|11)/.test(n))return i;if(3===n&&/\S/.test(t.nodeValue))return X(t,e);var r=m(t.childNodes);if(r.length&&(x(t,"isSplit",!0),!T(t).isRoot)){t.style.display="inline-block",t.style.position="relative";var o=t.nextSibling,a=t.previousSibling,l=t.textContent||"",c=o?o.textContent:" ",u=a?a.textContent:" ";x(t,{isWordEnd:/\s$/.test(l)||/^\s/.test(c),isWordStart:/^\s/.test(l)||/\s$/.test(u)})}return r.reduce((function(t,n){var i=Y(n,e),r=i.words,o=i.chars;return{words:[].concat(s(t.words),s(r)),chars:[].concat(s(t.chars),s(o))}}),i)}function U(t){T(t).isWord?(E(t),t.replaceWith.apply(t,s(t.childNodes))):m(t.children).forEach((function(t){return U(t)}))}var V=c(W,{});return function(){function t(e,n){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.isSplit=!1,this.settings=c(V,f(n)),this.elements=v(e),this.split()}return e(t,null,[{key:"clearData",value:function(){Object.keys(b).forEach((function(t){delete b[t]}))}},{key:"setDefaults",value:function(t){return V=c(V,f(t)),W}},{key:"revert",value:function(t){v(t).forEach((function(t){var e=T(t),n=e.isSplit,i=e.html,r=e.cssWidth,o=e.cssHeight;n&&(t.innerHTML=i,t.style.width=r||"",t.style.height=o||"",E(t))}))}},{key:"create",value:function(e,n){return new t(e,n)}},{key:"data",get:function(){return b}},{key:"defaults",get:function(){return V},set:function(t){V=c(V,f(t))}}]),e(t,[{key:"split",value:function(t){var e=this;this.revert(),this.elements.forEach((function(t){x(t,"html",t.innerHTML)})),this.lines=[],this.words=[],this.chars=[];var n=[window.pageXOffset,window.pageYOffset];void 0!==t&&(this.settings=c(this.settings,f(t)));var i=d(this.settings.types);i.none||(this.elements.forEach((function(t){x(t,"isRoot",!0);var n=Y(t,e.settings),i=n.words,r=n.chars;e.words=[].concat(s(e.words),s(i)),e.chars=[].concat(s(e.chars),s(r))})),this.elements.forEach((function(t){if(i.lines||e.settings.absolute){var r=function(t,e,n){var i,r,s,a=d(e.types),l=e.tagName,c=t.getElementsByTagName("*"),u=[],h=[],f=null,p=[],g=t.parentElement,v=t.nextElementSibling,y=document.createDocumentFragment(),_=window.getComputedStyle(t),b=_.textAlign,w=.2*parseFloat(_.fontSize);return e.absolute&&(s={left:t.offsetLeft,top:t.offsetTop,width:t.offsetWidth},r=t.offsetWidth,i=t.offsetHeight,x(t,{cssWidth:t.style.width,cssHeight:t.style.height})),m(c).forEach((function(i){var r=i.parentElement===t,s=function(t,e,n,i){if(!n.absolute)return{top:e?t.offsetTop:null};var r=t.offsetParent,s=o(i,2),a=s[0],l=s[1],c=0,u=0;if(r&&r!==document.body){var h=r.getBoundingClientRect();c=h.x+a,u=h.y+l}var f=t.getBoundingClientRect(),d=f.width,p=f.height,g=f.x;return{width:d,height:p,top:f.y+l-u,left:g+a-c}}(i,r,e,n),l=s.width,c=s.height,d=s.top,p=s.left;/^br$/i.test(i.nodeName)||(a.lines&&r&&((null===f||d-f>=w)&&(f=d,u.push(h=[])),h.push(i)),e.absolute&&x(i,{top:d,left:p,width:l,height:c}))})),g&&g.removeChild(t),a.lines&&(p=u.map((function(t){var n=B(l,{class:"".concat(e.splitClass," ").concat(e.lineClass),style:"display: block; text-align: ".concat(b,"; width: 100%;")});x(n,"isLine",!0);var i={height:0,top:1e4};return y.appendChild(n),t.forEach((function(t,e,r){var o=T(t),s=o.isWordEnd,a=o.top,l=o.height,c=r[e+1];i.height=Math.max(i.height,l),i.top=Math.min(i.top,a),n.appendChild(t),s&&T(c).isWordStart&&n.append(" ")})),e.absolute&&x(n,{height:i.height,top:i.top}),n})),a.words||U(y),t.replaceChildren(y)),e.absolute&&(t.style.width="".concat(t.style.width||r,"px"),t.style.height="".concat(i,"px"),m(c).forEach((function(t){var e=T(t),n=e.isLine,i=e.top,r=e.left,o=e.width,a=e.height,l=T(t.parentElement),c=!n&&l.isLine;t.style.top="".concat(c?i-l.top:i,"px"),t.style.left="".concat(n?s.left:r-(c?s.left:0),"px"),t.style.height="".concat(a,"px"),t.style.width="".concat(n?s.width:o,"px"),t.style.position="absolute"}))),g&&(v?g.insertBefore(t,v):g.appendChild(t)),p}(t,e.settings,n);e.lines=[].concat(s(e.lines),s(r))}})),this.isSplit=!0,window.scrollTo(n[0],n[1]),y(b).forEach((function(t){var e=o(t,2),n=e[0],i=e[1],r=i.isRoot,s=i.isSplit;r&&s||(b[n]=null,delete b[n])})))}},{key:"revert",value:function(){this.isSplit&&(this.lines=null,this.words=null,this.chars=null,this.isSplit=!1),t.revert(this.elements)}}]),t}()})),function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t="undefined"!=typeof globalThis?globalThis:t||self).Lenis=e()}(this,(function(){"use strict";function t(t,e,n){return Math.max(t,Math.min(e,n))}class e{advance(e){if(!this.isRunning)return;let n=!1;if(this.lerp)this.value=(i=this.value,r=this.to,o=60*this.lerp,s=e,function(t,e,n){return(1-n)*t+n*e}(i,r,1-Math.exp(-o*s))),Math.round(this.value)===this.to&&(this.value=this.to,n=!0);else{this.currentTime+=e;const i=t(0,this.currentTime/this.duration,1);n=i>=1;const r=n?1:this.easing(i);this.value=this.from+(this.to-this.from)*r}var i,r,o,s;this.onUpdate?.(this.value,n),n&&this.stop()}stop(){this.isRunning=!1}fromTo(t,e,{lerp:n=.1,duration:i=1,easing:r=(t=>t),onStart:o,onUpdate:s}){this.from=this.value=t,this.to=e,this.lerp=n,this.duration=i,this.easing=r,this.currentTime=0,this.isRunning=!0,o?.(),this.onUpdate=s}}class n{constructor({wrapper:t,content:e,autoResize:n=!0,debounce:i=250}={}){this.wrapper=t,this.content=e,n&&(this.debouncedResize=function(t,e){let n;return function(){let i=arguments,r=this;clearTimeout(n),n=setTimeout((function(){t.apply(r,i)}),e)}}(this.resize,i),this.wrapper===window?window.addEventListener("resize",this.debouncedResize,!1):(this.wrapperResizeObserver=new ResizeObserver(this.debouncedResize),this.wrapperResizeObserver.observe(this.wrapper)),this.contentResizeObserver=new ResizeObserver(this.debouncedResize),this.contentResizeObserver.observe(this.content)),this.resize()}destroy(){this.wrapperResizeObserver?.disconnect(),this.contentResizeObserver?.disconnect(),window.removeEventListener("resize",this.debouncedResize,!1)}resize=()=>{this.onWrapperResize(),this.onContentResize()};onWrapperResize=()=>{this.wrapper===window?(this.width=window.innerWidth,this.height=window.innerHeight):(this.width=this.wrapper.clientWidth,this.height=this.wrapper.clientHeight)};onContentResize=()=>{this.wrapper===window?(this.scrollHeight=this.content.scrollHeight,this.scrollWidth=this.content.scrollWidth):(this.scrollHeight=this.wrapper.scrollHeight,this.scrollWidth=this.wrapper.scrollWidth)};get limit(){return{x:this.scrollWidth-this.width,y:this.scrollHeight-this.height}}}class i{constructor(){this.events={}}emit(t,...e){let n=this.events[t]||[];for(let t=0,i=n.length;t<i;t++)n[t](...e)}on(t,e){return this.events[t]?.push(e)||(this.events[t]=[e]),()=>{this.events[t]=this.events[t]?.filter((t=>e!==t))}}off(t,e){this.events[t]=this.events[t]?.filter((t=>e!==t))}destroy(){this.events={}}}const r=100/6;class o{constructor(t,{wheelMultiplier:e=1,touchMultiplier:n=1}){this.element=t,this.wheelMultiplier=e,this.touchMultiplier=n,this.touchStart={x:null,y:null},this.emitter=new i,window.addEventListener("resize",this.onWindowResize,!1),this.onWindowResize(),this.element.addEventListener("wheel",this.onWheel,{passive:!1}),this.element.addEventListener("touchstart",this.onTouchStart,{passive:!1}),this.element.addEventListener("touchmove",this.onTouchMove,{passive:!1}),this.element.addEventListener("touchend",this.onTouchEnd,{passive:!1})}on(t,e){return this.emitter.on(t,e)}destroy(){this.emitter.destroy(),window.removeEventListener("resize",this.onWindowResize,!1),this.element.removeEventListener("wheel",this.onWheel,{passive:!1}),this.element.removeEventListener("touchstart",this.onTouchStart,{passive:!1}),this.element.removeEventListener("touchmove",this.onTouchMove,{passive:!1}),this.element.removeEventListener("touchend",this.onTouchEnd,{passive:!1})}onTouchStart=t=>{const{clientX:e,clientY:n}=t.targetTouches?t.targetTouches[0]:t;this.touchStart.x=e,this.touchStart.y=n,this.lastDelta={x:0,y:0},this.emitter.emit("scroll",{deltaX:0,deltaY:0,event:t})};onTouchMove=t=>{const{clientX:e,clientY:n}=t.targetTouches?t.targetTouches[0]:t,i=-(e-this.touchStart.x)*this.touchMultiplier,r=-(n-this.touchStart.y)*this.touchMultiplier;this.touchStart.x=e,this.touchStart.y=n,this.lastDelta={x:i,y:r},this.emitter.emit("scroll",{deltaX:i,deltaY:r,event:t})};onTouchEnd=t=>{this.emitter.emit("scroll",{deltaX:this.lastDelta.x,deltaY:this.lastDelta.y,event:t})};onWheel=t=>{let{deltaX:e,deltaY:n,deltaMode:i}=t;e*=1===i?r:2===i?this.windowWidth:1,n*=1===i?r:2===i?this.windowHeight:1,e*=this.wheelMultiplier,n*=this.wheelMultiplier,this.emitter.emit("scroll",{deltaX:e,deltaY:n,event:t})};onWindowResize=()=>{this.windowWidth=window.innerWidth,this.windowHeight=window.innerHeight}}return class{constructor({wrapper:t=window,content:r=document.documentElement,wheelEventsTarget:s=t,eventsTarget:a=s,smoothWheel:l=!0,syncTouch:c=!1,syncTouchLerp:u=.075,touchInertiaMultiplier:h=35,duration:f,easing:d=(t=>Math.min(1,1.001-Math.pow(2,-10*t))),lerp:p=!f&&.1,infinite:g=!1,orientation:m="vertical",gestureOrientation:v="vertical",touchMultiplier:y=1,wheelMultiplier:_=1,autoResize:b=!0,__experimental__naiveDimensions:w=!1}={}){this.__isSmooth=!1,this.__isScrolling=!1,this.__isStopped=!1,this.__isLocked=!1,this.onVirtualScroll=({deltaX:t,deltaY:e,event:n})=>{if(n.ctrlKey)return;const i=n.type.includes("touch"),r=n.type.includes("wheel");if(this.options.syncTouch&&i&&"touchstart"===n.type&&!this.isStopped&&!this.isLocked)return void this.reset();const o=0===t&&0===e,s="vertical"===this.options.gestureOrientation&&0===e||"horizontal"===this.options.gestureOrientation&&0===t;if(o||s)return;let a=n.composedPath();if(a=a.slice(0,a.indexOf(this.rootElement)),a.find((t=>{var e,n,o,s,a;return(null===(e=t.hasAttribute)||void 0===e?void 0:e.call(t,"data-lenis-prevent"))||i&&(null===(n=t.hasAttribute)||void 0===n?void 0:n.call(t,"data-lenis-prevent-touch"))||r&&(null===(o=t.hasAttribute)||void 0===o?void 0:o.call(t,"data-lenis-prevent-wheel"))||(null===(s=t.classList)||void 0===s?void 0:s.contains("lenis"))&&!(null===(a=t.classList)||void 0===a?void 0:a.contains("lenis-stopped"))})))return;if(this.isStopped||this.isLocked)return void n.preventDefault();if(this.isSmooth=this.options.syncTouch&&i||this.options.smoothWheel&&r,!this.isSmooth)return this.isScrolling=!1,void this.animate.stop();n.preventDefault();let l=e;"both"===this.options.gestureOrientation?l=Math.abs(e)>Math.abs(t)?e:t:"horizontal"===this.options.gestureOrientation&&(l=t);const c=i&&this.options.syncTouch,u=i&&"touchend"===n.type&&Math.abs(l)>5;u&&(l=this.velocity*this.options.touchInertiaMultiplier),this.scrollTo(this.targetScroll+l,Object.assign({programmatic:!1},c?{lerp:u?this.options.syncTouchLerp:1}:{lerp:this.options.lerp,duration:this.options.duration,easing:this.options.easing}))},this.onNativeScroll=()=>{if(!this.__preventNextScrollEvent&&!this.isScrolling){const t=this.animatedScroll;this.animatedScroll=this.targetScroll=this.actualScroll,this.velocity=0,this.direction=Math.sign(this.animatedScroll-t),this.emit()}},window.lenisVersion="1.0.42",t!==document.documentElement&&t!==document.body||(t=window),this.options={wrapper:t,content:r,wheelEventsTarget:s,eventsTarget:a,smoothWheel:l,syncTouch:c,syncTouchLerp:u,touchInertiaMultiplier:h,duration:f,easing:d,lerp:p,infinite:g,gestureOrientation:v,orientation:m,touchMultiplier:y,wheelMultiplier:_,autoResize:b,__experimental__naiveDimensions:w},this.animate=new e,this.emitter=new i,this.dimensions=new n({wrapper:t,content:r,autoResize:b}),this.toggleClassName("lenis",!0),this.velocity=0,this.isLocked=!1,this.isStopped=!1,this.isSmooth=c||l,this.isScrolling=!1,this.targetScroll=this.animatedScroll=this.actualScroll,this.options.wrapper.addEventListener("scroll",this.onNativeScroll,!1),this.virtualScroll=new o(a,{touchMultiplier:y,wheelMultiplier:_}),this.virtualScroll.on("scroll",this.onVirtualScroll)}destroy(){this.emitter.destroy(),this.options.wrapper.removeEventListener("scroll",this.onNativeScroll,!1),this.virtualScroll.destroy(),this.dimensions.destroy(),this.toggleClassName("lenis",!1),this.toggleClassName("lenis-smooth",!1),this.toggleClassName("lenis-scrolling",!1),this.toggleClassName("lenis-stopped",!1),this.toggleClassName("lenis-locked",!1)}on(t,e){return this.emitter.on(t,e)}off(t,e){return this.emitter.off(t,e)}setScroll(t){this.isHorizontal?this.rootElement.scrollLeft=t:this.rootElement.scrollTop=t}resize(){this.dimensions.resize()}emit(){this.emitter.emit("scroll",this)}reset(){this.isLocked=!1,this.isScrolling=!1,this.animatedScroll=this.targetScroll=this.actualScroll,this.velocity=0,this.animate.stop()}start(){this.isStopped&&(this.isStopped=!1,this.reset())}stop(){this.isStopped||(this.isStopped=!0,this.animate.stop(),this.reset())}raf(t){const e=t-(this.time||t);this.time=t,this.animate.advance(.001*e)}scrollTo(e,{offset:n=0,immediate:i=!1,lock:r=!1,duration:o=this.options.duration,easing:s=this.options.easing,lerp:a=!o&&this.options.lerp,onComplete:l,force:c=!1,programmatic:u=!0}={}){if(!this.isStopped&&!this.isLocked||c){if(["top","left","start"].includes(e))e=0;else if(["bottom","right","end"].includes(e))e=this.limit;else{let t;if("string"==typeof e?t=document.querySelector(e):(null==e?void 0:e.nodeType)&&(t=e),t){if(this.options.wrapper!==window){const t=this.options.wrapper.getBoundingClientRect();n-=this.isHorizontal?t.left:t.top}const i=t.getBoundingClientRect();e=(this.isHorizontal?i.left:i.top)+this.animatedScroll}}if("number"==typeof e){if(e+=n,e=Math.round(e),this.options.infinite?u&&(this.targetScroll=this.animatedScroll=this.scroll):e=t(0,e,this.limit),i)return this.animatedScroll=this.targetScroll=e,this.setScroll(this.scroll),this.reset(),void(null==l||l(this));if(!u){if(e===this.targetScroll)return;this.targetScroll=e}this.animate.fromTo(this.animatedScroll,e,{duration:o,easing:s,lerp:a,onStart:()=>{r&&(this.isLocked=!0),this.isScrolling=!0},onUpdate:(t,e)=>{this.isScrolling=!0,this.velocity=t-this.animatedScroll,this.direction=Math.sign(this.velocity),this.animatedScroll=t,this.setScroll(this.scroll),u&&(this.targetScroll=t),e||this.emit(),e&&(this.reset(),this.emit(),null==l||l(this),this.__preventNextScrollEvent=!0,requestAnimationFrame((()=>{delete this.__preventNextScrollEvent})))}})}}}get rootElement(){return this.options.wrapper===window?document.documentElement:this.options.wrapper}get limit(){return this.options.__experimental__naiveDimensions?this.isHorizontal?this.rootElement.scrollWidth-this.rootElement.clientWidth:this.rootElement.scrollHeight-this.rootElement.clientHeight:this.dimensions.limit[this.isHorizontal?"x":"y"]}get isHorizontal(){return"horizontal"===this.options.orientation}get actualScroll(){return this.isHorizontal?this.rootElement.scrollLeft:this.rootElement.scrollTop}get scroll(){return this.options.infinite?(this.animatedScroll%(t=this.limit)+t)%t:this.animatedScroll;var t}get progress(){return 0===this.limit?1:this.scroll/this.limit}get isSmooth(){return this.__isSmooth}set isSmooth(t){this.__isSmooth!==t&&(this.__isSmooth=t,this.toggleClassName("lenis-smooth",t))}get isScrolling(){return this.__isScrolling}set isScrolling(t){this.__isScrolling!==t&&(this.__isScrolling=t,this.toggleClassName("lenis-scrolling",t))}get isStopped(){return this.__isStopped}set isStopped(t){this.__isStopped!==t&&(this.__isStopped=t,this.toggleClassName("lenis-stopped",t))}get isLocked(){return this.__isLocked}set isLocked(t){this.__isLocked!==t&&(this.__isLocked=t,this.toggleClassName("lenis-locked",t))}get className(){let t="lenis";return this.isStopped&&(t+=" lenis-stopped"),this.isLocked&&(t+=" lenis-locked"),this.isScrolling&&(t+=" lenis-scrolling"),this.isSmooth&&(t+=" lenis-smooth"),t}toggleClassName(t,e){this.rootElement.classList.toggle(t,e),this.emitter.emit("className change",this)}}})),
/*!
 * imagesLoaded PACKAGED v5.0.0
 * JavaScript is all like "You images are done yet or what?"
 * MIT License
 */
function(t,e){"object"==typeof module&&module.exports?module.exports=e():t.EvEmitter=e()}("undefined"!=typeof window?window:this,(function(){function t(){}let e=t.prototype;return e.on=function(t,e){if(!t||!e)return this;let n=this._events=this._events||{},i=n[t]=n[t]||[];return i.includes(e)||i.push(e),this},e.once=function(t,e){if(!t||!e)return this;this.on(t,e);let n=this._onceEvents=this._onceEvents||{};return(n[t]=n[t]||{})[e]=!0,this},e.off=function(t,e){let n=this._events&&this._events[t];if(!n||!n.length)return this;let i=n.indexOf(e);return-1!=i&&n.splice(i,1),this},e.emitEvent=function(t,e){let n=this._events&&this._events[t];if(!n||!n.length)return this;n=n.slice(0),e=e||[];let i=this._onceEvents&&this._onceEvents[t];for(let r of n)i&&i[r]&&(this.off(t,r),delete i[r]),r.apply(this,e);return this},e.allOff=function(){return delete this._events,delete this._onceEvents,this},t})),
/*!
 * imagesLoaded v5.0.0
 * JavaScript is all like "You images are done yet or what?"
 * MIT License
 */
function(t,e){"object"==typeof module&&module.exports?module.exports=e(t,require("ev-emitter")):t.imagesLoaded=e(t,t.EvEmitter)}("undefined"!=typeof window?window:this,(function(t,e){let n=t.jQuery,i=t.console;function r(t,e,o){if(!(this instanceof r))return new r(t,e,o);let s=t;var a;"string"==typeof t&&(s=document.querySelectorAll(t)),s?(this.elements=(a=s,Array.isArray(a)?a:"object"==typeof a&&"number"==typeof a.length?[...a]:[a]),this.options={},"function"==typeof e?o=e:Object.assign(this.options,e),o&&this.on("always",o),this.getImages(),n&&(this.jqDeferred=new n.Deferred),setTimeout(this.check.bind(this))):i.error(`Bad element for imagesLoaded ${s||t}`)}r.prototype=Object.create(e.prototype),r.prototype.getImages=function(){this.images=[],this.elements.forEach(this.addElementImages,this)};const o=[1,9,11];r.prototype.addElementImages=function(t){"IMG"===t.nodeName&&this.addImage(t),!0===this.options.background&&this.addElementBackgroundImages(t);let{nodeType:e}=t;if(!e||!o.includes(e))return;let n=t.querySelectorAll("img");for(let t of n)this.addImage(t);if("string"==typeof this.options.background){let e=t.querySelectorAll(this.options.background);for(let t of e)this.addElementBackgroundImages(t)}};const s=/url\((['"])?(.*?)\1\)/gi;function a(t){this.img=t}function l(t,e){this.url=t,this.element=e,this.img=new Image}return r.prototype.addElementBackgroundImages=function(t){let e=getComputedStyle(t);if(!e)return;let n=s.exec(e.backgroundImage);for(;null!==n;){let i=n&&n[2];i&&this.addBackground(i,t),n=s.exec(e.backgroundImage)}},r.prototype.addImage=function(t){let e=new a(t);this.images.push(e)},r.prototype.addBackground=function(t,e){let n=new l(t,e);this.images.push(n)},r.prototype.check=function(){if(this.progressedCount=0,this.hasAnyBroken=!1,!this.images.length)return void this.complete();let t=(t,e,n)=>{setTimeout((()=>{this.progress(t,e,n)}))};this.images.forEach((function(e){e.once("progress",t),e.check()}))},r.prototype.progress=function(t,e,n){this.progressedCount++,this.hasAnyBroken=this.hasAnyBroken||!t.isLoaded,this.emitEvent("progress",[this,t,e]),this.jqDeferred&&this.jqDeferred.notify&&this.jqDeferred.notify(this,t),this.progressedCount===this.images.length&&this.complete(),this.options.debug&&i&&i.log(`progress: ${n}`,t,e)},r.prototype.complete=function(){let t=this.hasAnyBroken?"fail":"done";if(this.isComplete=!0,this.emitEvent(t,[this]),this.emitEvent("always",[this]),this.jqDeferred){let t=this.hasAnyBroken?"reject":"resolve";this.jqDeferred[t](this)}},a.prototype=Object.create(e.prototype),a.prototype.check=function(){this.getIsImageComplete()?this.confirm(0!==this.img.naturalWidth,"naturalWidth"):(this.proxyImage=new Image,this.img.crossOrigin&&(this.proxyImage.crossOrigin=this.img.crossOrigin),this.proxyImage.addEventListener("load",this),this.proxyImage.addEventListener("error",this),this.img.addEventListener("load",this),this.img.addEventListener("error",this),this.proxyImage.src=this.img.currentSrc||this.img.src)},a.prototype.getIsImageComplete=function(){return this.img.complete&&this.img.naturalWidth},a.prototype.confirm=function(t,e){this.isLoaded=t;let{parentNode:n}=this.img,i="PICTURE"===n.nodeName?n:this.img;this.emitEvent("progress",[this,i,e])},a.prototype.handleEvent=function(t){let e="on"+t.type;this[e]&&this[e](t)},a.prototype.onload=function(){this.confirm(!0,"onload"),this.unbindEvents()},a.prototype.onerror=function(){this.confirm(!1,"onerror"),this.unbindEvents()},a.prototype.unbindEvents=function(){this.proxyImage.removeEventListener("load",this),this.proxyImage.removeEventListener("error",this),this.img.removeEventListener("load",this),this.img.removeEventListener("error",this)},l.prototype=Object.create(a.prototype),l.prototype.check=function(){this.img.addEventListener("load",this),this.img.addEventListener("error",this),this.img.src=this.url,this.getIsImageComplete()&&(this.confirm(0!==this.img.naturalWidth,"naturalWidth"),this.unbindEvents())},l.prototype.unbindEvents=function(){this.img.removeEventListener("load",this),this.img.removeEventListener("error",this)},l.prototype.confirm=function(t,e){this.isLoaded=t,this.emitEvent("progress",[this,this.element,e])},r.makeJQueryPlugin=function(e){(e=e||t.jQuery)&&(n=e,n.fn.imagesLoaded=function(t,e){return new r(this,t,e).jqDeferred.promise(n(this))})},r.makeJQueryPlugin(),r}));