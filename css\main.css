/*! ------------------------------------------------
 * Project Name: Name: <PERSON><PERSON><PERSON> - Personal Portfolio & Resume HTML Template
 * Project Description: Show yourself brightly with <PERSON><PERSON><PERSON> - clean and creative portfolio and resume template!
 * Tags: mix_design, resume, portfolio, personal page, cv, template, one page, responsive, html5, css3, creative, clean 
 * Version: 1.0.0
 * Build Date: June 2024
 * Last Update: June 2024
 * This product is available exclusively on Themeforest
 * Author: mix_design
 * Author URI: https://themeforest.net/user/mix_design */
/*! File name: main.css
 * ------------------------------------------------
 * ------------------------------------------------
 * Table of Contents
 * ------------------------------------------------
 *  01. Fonts
 *  02. Variables
 *  03. Template Backgrounds
 *  04. Basic Styles
 *  05. Animations - Transitions
 *  06. Animations - Backgrounds
 *  07. Elements - Typography
 *  08. Elements - Controls
 *  09. Elements - Forms
 * 11. Sections Main
 * 12. Sections Inner
 * 13. Blocks Common
 * 14. Blocks Header
 * 15. Blocks Menu
 * 16. Blocks Footer
 * 17. Blocks Popup Dialogs
 * 18. Features Marquee
 * 19. Features About
 * 20. Features Gallery
 * 21. Features Achievements
 * 22. Features Stacking Cards
 * 23. Features Services
 * 24. Features Resume
 * 25. Features Tools
 * 26. Features Slider
 * 27. Features Accordion
 * 28. Features Socials
 * 29. Features Contact Data
 * ------------------------------------------------
 * Table of Contents End
 * ------------------------------------------------ */
/* ------------------------------------------------*/
/* Fonts Start */
/* ------------------------------------------------*/
/* Urbanist font */
@import url("https://fonts.googleapis.com/css2?family=Urbanist:ital,wght@0,100..900;1,100..900&display=swap");
/* ------------------------------------------------*/
/* Fonts End */
/* ------------------------------------------------*/
/* ------------------------------------------------*/
/* Variables Start */
/* ------------------------------------------------*/

:root {
  --_size: 1.6rem;
  --_font-default: "Urbanist", sans-serif;
  --_font-accent: "Urbanist", sans-serif;
  --_radius: 2rem;
  --_radius-s: 1rem;
  --_animspeed-fast: 0.1s;
  --_animspeed-medium: 0.3s;
  --_animspeed-slow: 0.6s;
  --_animbezier: cubic-bezier(0.23, 0.65, 0.74, 1.09);

  /* dark color scheme */
  --base: #141414;
  --base-rgb: 20, 20, 20;
  --base-shade: #000000;
  --base-tint: #242424;
  --stroke-controls: #F2F5FC;
  --stroke-elements: #535762;
  --font-weight-base: 300;
  --font-weight-medium: 500;
  --t-bright: #F2F5FC;
  --t-medium: #AEB5C5;
  --t-muted: #505258;
  --t-opp-bright: #151617;
  --t-opp-medium: #313437;
  --t-opp-muted: #797D83;
  --neutral-bright: #ffffff;

  /* permanent colors */
  --per-base: #141414;
  --t-per-bright-light: #F2F5FC;
  --t-per-medium-light: #AEB5C5;
  --t-per-muted-light: #3E4045;
  --t-per-bright-dark: #151617;
  --t-per-medium-dark: #313437;
  --t-per-muted-dark: #797D83;
  --per-stroke-controls: #F2F5FC;
  --per-stroke-elements: #8F93A1;

  /* neutral colors */
  --neutral-white: #ffffff;
  --neutral-black: #000000;
  --neutral-gray: #878787;
  --neutral-gray-dark: #404040;
}

/* Remove light mode media query */

/* Newsletter Section */
section#newsletter .newsletter {
  position: relative;
  display: flex;
  flex-direction: row;
  align-items: stretch;
  border: 1px solid var(--stroke-elements);
  border-radius: 12px;
  overflow: hidden;
  height: auto;
  width: 100%;
  transition: all 0.3s ease;
  z-index: 1;
  background: var(--base);
  background-image: url('../img/backgrounds/palette.png') !important;
  background-size: cover !important;
  background-position: center !important;
}

/* Remove light/dark mode specific styles */
html[data-theme="light"] section#newsletter .newsletter,
html[data-theme="dark"] section#newsletter .newsletter {
  background: var(--base);
  background-image: url('../img/backgrounds/palette.png');
  background-size: cover;
  background-position: center;
}

/* defaults */
@media (prefers-color-scheme: light) {
  :root {
    --base: var(--base--light);
    --base-rgb: var(--base-rgb--light);
    --base-shade: var(--base-shade--light);
    --base-tint: var(--base-tint--light);
    --stroke-controls: var(--stroke-controls--light);
    --stroke-elements: var(--stroke-elements--light);
    --font-weight-base: var(--font-weight-base--light);
    --font-weight-medium: var(--font-weight-medium--light);
    --t-bright: var(--t-bright--light);
    --t-medium: var(--t-medium--light);
    --t-muted: var(--t-muted--light);
    --t-opp-bright: var(--t-opp-bright--light);
    --t-opp-medium: var(--t-opp-medium--light);
    --t-opp-muted: var(--t-opp-muted--light);
    --neutral-bright: var(--neutral-bright--light);
  }
}

@media (prefers-color-scheme: dark) {
  :root {
    --base: var(--base--dark);
    --base-rgb: var(--base-rgb--dark);
    --base-shade: var(--base-shade--dark);
    --base-tint: var(--base-tint--dark);
    --stroke-controls: var(--stroke-controls--dark);
    --stroke-elements: var(--stroke-elements--dark);
    --font-weight-base: var(--font-weight-base--dark);
    --font-weight-medium: var(--font-weight-medium--dark);
    --t-bright: var(--t-bright--dark);
    --t-medium: var(--t-medium--dark);
    --t-muted: var(--t-muted--dark);
    --t-opp-bright: var(--t-opp-bright--dark);
    --t-opp-medium: var(--t-opp-medium--dark);
    --t-opp-muted: var(--t-opp-muted--dark);
    --neutral-bright: var(--neutral-bright--dark);
  }
}

[color-scheme=light] {
  --base: var(--base--light);
  --base-rgb: var(--base-rgb--light);
  --base-shade: var(--base-shade--light);
  --base-tint: var(--base-tint--light);
  --stroke-controls: var(--stroke-controls--light);
  --stroke-elements: var(--stroke-elements--light);
  --font-weight-base: var(--font-weight-base--light);
  --font-weight-medium: var(--font-weight-medium--light);
  --t-bright: var(--t-bright--light);
  --t-medium: var(--t-medium--light);
  --t-muted: var(--t-muted--light);
  --t-opp-bright: var(--t-opp-bright--light);
  --t-opp-medium: var(--t-opp-medium--light);
  --t-opp-muted: var(--t-opp-muted--light);
  --neutral-bright: var(--neutral-bright--light);
}

[color-scheme=dark] {
  --base: var(--base--dark);
  --base-rgb: var(--base-rgb--dark);
  --base-shade: var(--base-shade--dark);
  --base-tint: var(--base-tint--dark);
  --stroke-controls: var(--stroke-controls--dark);
  --stroke-elements: var(--stroke-elements--dark);
  --font-weight-base: var(--font-weight-base--dark);
  --font-weight-medium: var(--font-weight-medium--dark);
  --t-bright: var(--t-bright--dark);
  --t-medium: var(--t-medium--dark);
  --t-muted: var(--t-muted--dark);
  --t-opp-bright: var(--t-opp-bright--dark);
  --t-opp-medium: var(--t-opp-medium--dark);
  --t-opp-muted: var(--t-opp-muted--dark);
  --neutral-bright: var(--neutral-bright--dark);
}

/* Adicione estas regras para garantir que a barra de rolagem seja sempre visível */
html {
    overflow-y: scroll;
    scrollbar-width: thin; /* Para Firefox */
    scrollbar-color: rgba(155, 155, 155, 0.5) transparent; /* Para Firefox */
}

/* Estilização da barra de rolagem para navegadores WebKit (Chrome, Safari, etc) */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: transparent;
}

::-webkit-scrollbar-thumb {
    background-color: rgba(155, 155, 155, 0.5);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background-color: rgba(155, 155, 155, 0.7);
}

/* Certifique-se que o body não está com overflow hidden */
body {
    overflow-y: auto;
}

/* ------------------------------------------------*/
/* Variables End */
/* ------------------------------------------------*/
/* ------------------------------------------------*/
/* Template Backgrounds Start */
/* ------------------------------------------------*/
.main-image-1 {
  background-image: url("https://dummyimage.com/1400x1000/414141/636363");
}

.main-image-2 {
  background-image: url("https://dummyimage.com/1920x1080/414141/636363");
}

.about-image-1 {
  background-image: url("../img/avatars/Me.webp");
}

.marquee-image-single-1 {
  background-image: url("https://dummyimage.com/1000x1300/4d4d4d/636363");
}

.marquee-image-single-2 {
  background-image: url("https://dummyimage.com/1000x1300/4d4d4d/636363");
}

.marquee-image-single-3 {
  background-image: url("https://dummyimage.com/1000x1300/4d4d4d/636363");
}

.marquee-image-single-4 {
  background-image: url("https://dummyimage.com/1000x1300/4d4d4d/636363");
}

.marquee-image-single-5 {
  background-image: url("https://dummyimage.com/1000x1300/4d4d4d/636363");
}

.marquee-image-single-6 {
  background-image: url("https://dummyimage.com/1000x1300/4d4d4d/636363");
}

.portfolio-item-1 {
  background-image: url("https://dummyimage.com/1920x1080/686868/737373");
}

.portfolio-item-2 {
  background-image: url("https://dummyimage.com/1920x1080/414141/636363");
}

.portfolio-item-3 {
  background-image: url("https://dummyimage.com/1920x1080/686868/737373");
}

.portfolio-item-4 {
  background-image: url("https://dummyimage.com/1920x1080/414141/636363");
}

.portfolio-item-5 {
  background-image: url("https://dummyimage.com/1600x1200/686868/737373");
}

.portfolio-item-6 {
  background-image: url("https://dummyimage.com/1600x1200/414141/636363");
}

.portfolio-item-7 {
  background-image: url("https://dummyimage.com/1600x1200/686868/737373");
}

.portfolio-item-8 {
  background-image: url("https://dummyimage.com/1600x1200/414141/636363");
}

.project-image-bg-1 {
  background-image: url("https://dummyimage.com/1920x1280/4d4d4d/636363");
}

.project-image-bg-2 {
  background-image: url("https://dummyimage.com/1920x1080/4d4d4d/636363");
}

.project-image-bg-3 {
  background-image: url("https://dummyimage.com/1920x1080/4d4d4d/636363");
}

/* ------------------------------------------------*/
/* Template Backgrounds End */
/* ------------------------------------------------*/
/* ------------------------------------------------*/
/* Basic Styles Start */
/* ------------------------------------------------*/
*,
*::before,
*::after {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

button:active,
button:focus {
  outline: none !important;
}

button::-moz-focus-inner {
  border: 0 !important;
}

input::-moz-focus-inner {
  border: 0 !important;
}

a:focus,
button:focus,
input:focus,
textarea:focus {
  -webkit-tap-highlight-color: transparent;
}

input,
textarea,
button,
select,
a {
  -webkit-tap-highlight-color: transparent;
}

::-moz-selection {
  background-color: var(--base-tint);
  color: var(--t-medium);
  text-shadow: none;
}

::selection {
  background-color: var(--base-tint);
  color: var(--t-medium);
  text-shadow: none;
}

::-webkit-scrollbar {
  display: none;
  width: 6px;
  background: var(--base-shade);
}

@media only screen and (min-width: 768px) {
  ::-webkit-scrollbar {
    display: block;
  }
}

::-webkit-scrollbar-track {
  background-color: var(--base-shade);
}

::-webkit-scrollbar-thumb {
  background-color: var(--base-tint);
  border-radius: var(--_radius-s);
}

html {
  width: 100%;
  height: 100%;
  font-family: sans-serif;
  font-size: 62.5%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  position: relative;
  width: 100%;
  min-width: 360px;
  overflow-x: unset !important;
  font: normal 400 var(--_size)/1.4 var(--_font-default);
  text-rendering: optimizeLegibility;
  background-color: var(--base);
  color: var(--t-medium);
}

section {
  position: relative;
  min-width: 360px;
}

a {
  text-decoration: none;
  outline: 0;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  -webkit-transition: all var(--_animspeed-medium) var(--_animbezier);
  -moz-transition: all var(--_animspeed-medium) var(--_animbezier);
  transition: all var(--_animspeed-medium) var(--_animbezier);
}

img {
  display: block;
  width: 100%;
  height: auto;
}

.overflow-hidden {
  overflow: hidden !important;
}

.components {
  position: relative;
}

.components__item {
  position: relative;
  padding: 8rem 10%;
  border-bottom: 1px solid var(--stroke-elements);
}

.components__caption {
  display: block;
  font: normal 400 1.3rem/1 var(--_font-default);
  color: var(--t-medium);
  text-transform: uppercase;
  letter-spacing: 0.1rem;
  margin-bottom: 2rem;
}

.spacer {
  height: 300px;
}

/* ------------------------------------------------*/
/* Basic Styles End */
/* ------------------------------------------------*/
/* ------------------------------------------------*/
/* Animations - Transitions Start */
/* ------------------------------------------------*/
/* ------------------------------------------------*/
/* Animations - Transitions End */
/* ------------------------------------------------*/
/* ------------------------------------------------*/
/* Animations - Backgrounds Start */
/* ------------------------------------------------*/
.video-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

#video-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: transparent;
}

.video {
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: transparent;
  background-position: center center;
  -moz-background-size: contain;
  background-size: contain;
  -o-object-fit: cover;
  object-fit: cover;
}

/* ------------------------------------------------*/
/* Animations - Backgrounds End */
/* ------------------------------------------------*/
/* ------------------------------------------------*/
/* Elements - Typography Start */
/* ------------------------------------------------*/
h1 {
  font: normal 300 5.6rem var(--_font-accent);
  color: var(--t-bright);
}

@media only screen and (min-width: 768px) {
  h1 {
    font-size: 9rem;
  }
}

@media only screen and (min-width: 1200px) {
  h1 {
    font-size: 11rem;
  }
}

@media only screen and (min-width: 1400px) {
  h1 {
    font-size: 12.6rem;
  }
}

@media only screen and (min-width: 1600px) {
  h1 {
    font-size: 16rem;
  }
}

@media only screen and (min-width: 992px) {
  .headline-80-desktop .headline__title {
    max-width: 67rem;
  }
}

@media only screen and (min-width: 1200px) {
  .headline-80-desktop .headline__title {
    max-width: none;
  }
}

.headline__subtitle.space-top {
  margin-top: 4.6rem;
}

.headline__subtitle.space-bottom {
  margin-bottom: 3.8rem;
}

@media only screen and (min-width: 768px) {
  .headline__subtitle.space-top {
    margin-top: 5.7rem;
  }

  .headline__subtitle.space-bottom {
    margin-bottom: 4.2rem;
  }
}

@media only screen and (min-width: 992px) {
  .headline__subtitle.space-top {
    margin-top: 6.4rem;
  }

  .headline__subtitle.space-bottom {
    margin-bottom: 4.9rem;
  }
}

@media only screen and (min-width: 1200px) {
  .headline__subtitle.space-top {
    margin-top: 5.8rem;
  }

  .headline__subtitle.space-bottom {
    margin-bottom: 4rem;
  }
}

@media only screen and (min-width: 1400px) {
  .headline__subtitle.space-top {
    margin-top: 6.3rem;
  }

  .headline__subtitle.space-bottom {
    margin-bottom: 5.7rem;
  }
}

@media only screen and (min-width: 1600px) {
  .headline__subtitle.space-top {
    margin-top: 6.8rem;
  }

  .headline__subtitle.space-bottom {
    margin-bottom: 4.1rem;
  }
}

h2 {
  font: normal 300 4.6rem var(--_font-accent);
  color: var(--t-bright);
}

@media only screen and (min-width: 768px) {
  h2 {
    font-size: 8rem;
  }
}

@media only screen and (min-width: 1200px) {
  h2 {
    font-size: 9rem;
  }
}

@media only screen and (min-width: 1400px) {
  h2 {
    font-size: 10rem;
  }
}

@media only screen and (min-width: 1600px) {
  h2 {
    font-size: 14rem;
  }
}

.h2__text {
  margin-top: 4.5rem;
}

@media only screen and (min-width: 768px) {
  .h2__text {
    margin-top: 5.7rem;
    max-width: 67rem;
  }
}

@media only screen and (min-width: 992px) {
  .h2__text {
    margin-top: 6.4rem;
  }
}

@media only screen and (min-width: 1200px) {
  .h2__text {
    margin-top: 5.9rem;
    max-width: 78rem;
  }

  .h2__text.text-half {
    max-width: 54%;
  }

  .h2__text.text-twothirds {
    max-width: 67%;
  }
}

@media only screen and (min-width: 1400px) {
  .h2__text {
    margin-top: 6.6rem;
  }

  .h2__text.text-half {
    max-width: 50%;
  }
}

@media only screen and (min-width: 1600px) {
  .h2__text {
    margin-top: 6.9rem;
  }

  .h2__text.text-half {
    max-width: 45%;
  }
}

h3 {
  font: normal 400 3.4rem var(--_font-accent);
  color: var(--t-bright);
}

h3.dark {
  color: var(--t-per-bright-dark);
  font-weight: 400;
}

h3.light {
  color: var(--t-per-bright-light);
  font-weight: 300;
}

@media only screen and (min-width: 768px) {
  h3 {
    font-size: 4.4rem;
  }
}

@media only screen and (min-width: 1200px) {
  h3 {
    font-size: 5rem;
  }
}

@media only screen and (min-width: 1400px) {
  h3 {
    font-size: 5.6rem;
  }
}

@media only screen and (min-width: 1600px) {
  h3 {
    font-size: 6.4rem;
  }
}

h4 {
  font: normal 400 2.6rem var(--_font-accent);
  color: var(--t-bright);
}

@media only screen and (min-width: 1400px) {
  h4 {
    font-size: 3rem;
  }
}

@media only screen and (min-width: 1600px) {
  h4 {
    font-size: 3.6rem;
  }
}

h5 {
  font: normal 500 2.2rem var(--_font-accent);
  color: var(--t-bright);
}

h6 {
  font: normal var(--font-weight-base) 1.8rem/1.6 var(--_font-default);
  color: var(--t-bright);
}

@media only screen and (min-width: 1200px) {
  h6 {
    font-size: 2rem;
  }
}

@media only screen and (min-width: 1400px) {
  h6 {
    font-size: 2.2rem;
  }
}

@media only screen and (min-width: 1600px) {
  h6 {
    font-size: 2.6rem;
  }
}

.section-name {
  position: relative;
  display: -webkit-inline-box;
  display: -moz-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: start;
  -moz-box-pack: start;
  -ms-flex-pack: start;
  justify-content: start;
  -webkit-box-align: center;
  -moz-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  justify-content: flex-start;
  font: normal var(--font-weight-base) 1.8rem/1.4 var(--_font-default);
  color: var(--t-bright);
}

.section-name .section-name-caption,
.section-name i {
  position: relative;
  display: -webkit-inline-box;
  display: -moz-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
}

.section-name i {
  font-size: 2.3rem;
  overflow: hidden;
}

.section-name.icon-left .section-name-caption {
  margin-left: 1rem;
}

.section-name.icon-right .section-name-caption {
  margin-right: 1rem;
}

@media only screen and (min-width: 1200px) {
  .section-name {
    font-size: 2rem;
  }

  .section-name i {
    font-size: 2.5rem;
  }
}

@media only screen and (min-width: 1400px) {
  .section-name {
    font-size: 2.2rem;
  }

  .section-name i {
    font-size: 2.7rem;
  }
}

@media only screen and (min-width: 1600px) {
  .section-name {
    font-size: 2.6rem;
  }

  .section-name i {
    font-size: 3.2rem;
  }
}

.tagline-chapter {
  font: normal var(--font-weight-medium) 1.8rem var(--_font-default);
  color: var(--t-bright);
}

.tagline-chapter.small {
  font-size: 1.6rem;
}

.tagline-chapter::before {
  content: "[";
  display: inline;
  padding-right: 0.24em;
}

.tagline-chapter::after {
  content: "]";
  display: inline;
  padding-left: 0.24em;
}

@media only screen and (min-width: 1200px) {
  .tagline-chapter {
    font-size: 2rem;
  }
}

@media only screen and (min-width: 1400px) {
  .tagline-chapter {
    font-size: 2.2rem;
  }
}

@media only screen and (min-width: 1600px) {
  .tagline-chapter {
    font-size: 2.6rem;
  }

  .tagline-chapter.small {
    font-size: 1.8rem;
  }
}

.tagline-tool {
  font: normal 500 1.8rem var(--_font-default);
  color: var(--t-bright);
}

@media only screen and (min-width: 1200px) {
  .tagline-tool {
    font-size: 2rem;
  }
}

@media only screen and (min-width: 1400px) {
  .tagline-tool {
    font-size: 2.2rem;
  }
}

@media only screen and (min-width: 1600px) {
  .tagline-tool {
    font-size: 2.6rem;
  }
}

p {
  font: normal var(--font-weight-base) 1.8rem var(--_font-default);
  color: var(--t-bright);
}

p.type-basic-160lh {
  line-height: 1.6;
}

p.type-basic-140lh {
  line-height: 1.4;
}

p.small {
  font-size: 1.6rem;
}

p.dark {
  color: var(--t-per-bright-dark);
  font-weight: 400;
}

p.light {
  color: var(--t-per-bright-light);
  font-weight: 300;
}

@media only screen and (min-width: 1200px) {
  p {
    font-size: 2rem;
  }
}

@media only screen and (min-width: 1400px) {
  p {
    font-size: 2.2rem;
  }
}

@media only screen and (min-width: 1600px) {
  p {
    font-size: 2.6rem;
  }

  p.small {
    font-size: 1.8rem;
  }
}

a {
  font: normal var(--font-weight-base) 1.8rem var(--_font-default);
  color: var(--t-bright);
  text-decoration: none;
}

a.link-small {
  font-size: 1.6rem;
}

a.link-small-underline {
  font-size: 1.6rem;
  text-decoration: underline;
}

a.link-small-160lh {
  display: -webkit-inline-box;
  display: -moz-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  font-size: 1.6rem;
  line-height: 1.6;
}

.no-touch a:hover.link-small {
  color: var(--t-medium);
}

.no-touch a:hover.link-small-underline {
  color: var(--t-medium);
}

.no-touch a:hover.link-small-160lh {
  color: var(--t-medium);
}

@media only screen and (min-width: 1200px) {
  a {
    font-size: 2rem;
  }
}

@media only screen and (min-width: 1400px) {
  a {
    font-size: 2.2rem;
  }
}

@media only screen and (min-width: 1600px) {
  a {
    font-size: 2.6rem;
  }

  a.link-small {
    font-size: 1.8rem;
  }

  a.link-small-underline {
    font-size: 1.8rem;
    text-decoration: underline;
  }

  a.link-small-160lh {
    font-size: 1.8rem;
    line-height: 1.6;
  }
}

blockquote {
  font: normal 300 3.2rem/1.4 var(--_font-default);
  color: var(--t-bright);
}

@media only screen and (min-width: 768px) {
  blockquote {
    font-size: 4.4rem;
    max-width: 76rem;
  }
}

@media only screen and (min-width: 1200px) {
  blockquote {
    font-size: 5rem;
    max-width: none;
  }
}

@media only screen and (min-width: 1400px) {
  blockquote {
    font-size: 5.6rem;
  }
}

@media only screen and (min-width: 1600px) {
  blockquote {
    font-size: 6.8rem;
  }
}

.logo-text {
  font: normal 600 1.8rem var(--_font-default);
  color: var(--t-bright);
}

@media only screen and (min-width: 768px) {
  .logo-text {
    font-size: 2rem;
  }
}

@media only screen and (min-width: 1400px) {
  .logo-text {
    font-size: 2.2rem;
  }
}

@media only screen and (min-width: 1600px) {
  .logo-text {
    font-size: 2.6rem;
  }
}

.menu-link {
  font: normal 500 1.6rem var(--_font-default);
}

@media only screen and (min-width: 1600px) {
  .menu-link {
    font-size: 1.8rem;
  }
}

.text-link {
  color: inherit;
  text-decoration: none;
  background: -webkit-gradient(linear, left top, right top, from(rgba(0, 0, 0, 0)), to(rgba(0, 0, 0, 0))), -webkit-gradient(linear, left top, right top, from(var(--t-bright)), to(var(--t-bright)));
  background: -moz-linear-gradient(left, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0)), -moz-linear-gradient(left, var(--t-bright), var(--t-bright));
  background: linear-gradient(to right, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0)), linear-gradient(to right, var(--t-bright), var(--t-bright));
  -moz-background-size: 100% 1px, 0 1px;
  background-size: 100% 1px, 0 1px;
  background-position: 100% 100%, 0 100%;
  background-repeat: no-repeat;
  -webkit-transition: background-size 400ms, color 300ms;
  -moz-transition: background-size 400ms, color 300ms, -moz-background-size 400ms;
  transition: background-size 400ms, color 300ms;
  transition: background-size 400ms, color 300ms, -moz-background-size 400ms;
}

.text-link.text-link-medium {
  background: -webkit-gradient(linear, left top, right top, from(rgba(0, 0, 0, 0)), to(rgba(0, 0, 0, 0))), -webkit-gradient(linear, left top, right top, from(var(--t-muted)), to(var(--t-muted)));
  background: -moz-linear-gradient(left, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0)), -moz-linear-gradient(left, var(--t-muted), var(--t-muted));
  background: linear-gradient(to right, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0)), linear-gradient(to right, var(--t-muted), var(--t-muted));
  -moz-background-size: 100% 1px, 0 1px;
  background-size: 100% 1px, 0 1px;
  background-position: 100% 100%, 0 100%;
  background-repeat: no-repeat;
  -webkit-transition: background-size 400ms, color 300ms;
  -moz-transition: background-size 400ms, color 300ms, -moz-background-size 400ms;
  transition: background-size 400ms, color 300ms;
  transition: background-size 400ms, color 300ms, -moz-background-size 400ms;
}

.no-touch .text-link:hover {
  -moz-background-size: 0 1px, 100% 1px;
  background-size: 0 1px, 100% 1px;
}

/* ------------------------------------------------*/
/* Elements - Typography End */
/* ------------------------------------------------*/
/* ------------------------------------------------*/
/* Elements - Controls Start */
/* ------------------------------------------------*/
.btn {
  position: relative;
  display: -webkit-inline-box;
  display: -moz-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -moz-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -moz-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  border: none;
  outline: 0;
  padding: 0;
  font: inherit;
  background-color: transparent;
  cursor: pointer;
  text-decoration: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.btn-line {
  -webkit-box-pack: start;
  -moz-box-pack: start;
  -ms-flex-pack: start;
  justify-content: flex-start;
  font: normal var(--font-weight-medium) 1.8rem/1.4 var(--_font-default);
  color: var(--t-bright);
}

.btn-line .btn-caption,
.btn-line i {
  position: relative;
  display: -webkit-inline-box;
  display: -moz-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
}

.btn-line i {
  font-size: 2.3rem;
  overflow: hidden;
}

.btn-line.icon-left .btn-caption {
  margin-left: 1rem;
}

.btn-line.icon-right .btn-caption {
  margin-right: 1rem;
}

.btn-line:hover {
  color: var(--t-medium);
}

@media only screen and (min-width: 1200px) {
  .btn-line {
    font-size: 2rem;
  }

  .btn-line i {
    font-size: 2.5rem;
  }
}

@media only screen and (min-width: 1400px) {
  .btn-line {
    font-size: 2.2rem;
  }

  .btn-line i {
    font-size: 2.7rem;
  }
}

@media only screen and (min-width: 1600px) {
  .btn-line {
    font-size: 2.6rem;
  }

  .btn-line i {
    font-size: 3.2rem;
  }
}

.btn-line-small {
  -webkit-box-pack: start;
  -moz-box-pack: start;
  -ms-flex-pack: start;
  justify-content: flex-start;
  font: normal var(--font-weight-medium) 1.4rem/1.4 var(--_font-default);
  color: var(--t-bright);
}

.btn-line-small .btn-caption,
.btn-line-small i {
  position: relative;
  display: -webkit-inline-box;
  display: -moz-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
}

.btn-line-small i {
  font-size: 1.6rem;
  overflow: hidden;
}

.btn-line-small.icon-left .btn-caption {
  margin-left: 1rem;
}

.btn-line-small.icon-right .btn-caption {
  margin-right: 1rem;
}

.btn-line-small:hover {
  color: var(--t-medium);
}

@media only screen and (min-width: 768px) {
  .btn-line-small {
    font-size: 1.6rem;
  }

  .btn-line-small i {
    font-size: 1.9rem;
  }
}

@media only screen and (min-width: 1600px) {
  .btn-line-small {
    font-size: 1.8rem;
  }

  .btn-line-small i {
    font-size: 2.2rem;
    padding-bottom: 0.2rem;
  }
}

.btn-default {
  width: 100%;
  height: 10rem;
  padding: 0 7.6rem;
  -moz-border-radius: 5rem;
  border-radius: 5rem;
  border: 1px solid var(--neutral-bright);
  background-color: var(--neutral-bright);
  font: normal var(--font-weight-medium) 1.8rem/10rem var(--_font-default);
  color: var(--t-opp-bright);
}

@media only screen and (min-width: 576px) {
  .btn-default {
    width: auto;
  }
}

@media only screen and (min-width: 1200px) {
  .btn-default {
    border: 1px solid var(--stroke-controls);
    background-color: transparent;
    font-size: 2rem;
    color: var(--t-bright);
  }

  .btn-default .btn-caption {
    display: block;
    position: relative;
  }
}

@media only screen and (min-width: 1400px) {
  .btn-default {
    font-size: 2.2rem;
  }
}

@media only screen and (min-width: 1600px) {
  .btn-default {
    height: 11rem;
    line-height: 11rem;
    -moz-border-radius: 6rem;
    border-radius: 6rem;
    font-size: 2.6rem;
  }
}

@media only screen and (min-width: 1200px) {
  .hover-default {
    position: relative;
    overflow: hidden;
    -webkit-transition: color 0.3s 0s, background-color 0s 0s, -webkit-transform 0.3s ease-in-out;
    transition: color 0.3s 0s, background-color 0s 0s, -webkit-transform 0.3s ease-in-out;
    -moz-transition: color 0.3s 0s, background-color 0s 0s, transform 0.3s ease-in-out, -moz-transform 0.3s ease-in-out;
    transition: color 0.3s 0s, background-color 0s 0s, transform 0.3s ease-in-out;
    transition: color 0.3s 0s, background-color 0s 0s, transform 0.3s ease-in-out, -webkit-transform 0.3s ease-in-out, -moz-transform 0.3s ease-in-out;
  }

  .hover-default em {
    position: absolute;
    display: block;
    width: 0;
    height: 0;
    -moz-border-radius: 50%;
    border-radius: 50%;
    background-color: var(--t-bright);
    -webkit-transition: width 0.3s ease-in-out, height 0.3s ease-in-out;
    -moz-transition: width 0.3s ease-in-out, height 0.3s ease-in-out;
    transition: width 0.3s ease-in-out, height 0.3s ease-in-out;
    -webkit-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
  }

  .no-touch .hover-default:hover {
    color: var(--t-opp-bright);
    background-color: var(--t-bright);
    -webkit-transition: color 0.3s 0s, background-color 0.3s 0.2s, -webkit-box-shadow 0.5s, -webkit-transform 0.3s ease-in-out;
    transition: color 0.3s 0s, background-color 0.3s 0.2s, -webkit-box-shadow 0.5s, -webkit-transform 0.3s ease-in-out;
    -moz-transition: color 0.3s 0s, background-color 0.3s 0.2s, box-shadow 0.5s, transform 0.3s ease-in-out, -moz-box-shadow 0.5s, -moz-transform 0.3s ease-in-out;
    transition: color 0.3s 0s, background-color 0.3s 0.2s, box-shadow 0.5s, transform 0.3s ease-in-out;
    transition: color 0.3s 0s, background-color 0.3s 0.2s, box-shadow 0.5s, transform 0.3s ease-in-out, -webkit-box-shadow 0.5s, -moz-box-shadow 0.5s, -webkit-transform 0.3s ease-in-out, -moz-transform 0.3s ease-in-out;
  }

  .no-touch .hover-default:hover em {
    width: 225%;
    height: 22.5rem;
  }
}

.btn-circle-text {
  width: 10rem;
  height: 10rem;
  -moz-border-radius: 50%;
  border-radius: 50%;
  border: 1px solid var(--neutral-bright);
  background-color: var(--neutral-bright);
  font: normal var(--font-weight-medium) 1.2rem var(--_font-default);
  color: var(--t-opp-bright);
}

@media only screen and (min-width: 1200px) {
  .btn-circle-text {
    width: 11rem;
    height: 11rem;
    font-size: 1.4rem;
    border: 1px solid var(--stroke-controls);
    background-color: transparent;
    color: var(--t-bright);
  }

  .btn-circle-text .btn-caption {
    display: block;
    position: relative;
  }
}

@media only screen and (min-width: 1400px) {
  .btn-circle-text {
    width: 14rem;
    height: 14rem;
    font-size: 1.6rem;
  }
}

@media only screen and (min-width: 1600px) {
  .btn-circle-text {
    width: 16rem;
    height: 16rem;
    font-size: 1.8rem;
  }
}

@media only screen and (min-width: 1200px) {
  .hover-circle {
    position: relative;
    overflow: hidden;
    -webkit-transition: color 0.3s 0s, background-color 0s 0s, -webkit-transform 0.3s ease-in-out;
    transition: color 0.3s 0s, background-color 0s 0s, -webkit-transform 0.3s ease-in-out;
    -moz-transition: color 0.3s 0s, background-color 0s 0s, transform 0.3s ease-in-out, -moz-transform 0.3s ease-in-out;
    transition: color 0.3s 0s, background-color 0s 0s, transform 0.3s ease-in-out;
    transition: color 0.3s 0s, background-color 0s 0s, transform 0.3s ease-in-out, -webkit-transform 0.3s ease-in-out, -moz-transform 0.3s ease-in-out;
  }

  .hover-circle em {
    position: absolute;
    display: block;
    width: 0;
    height: 0;
    -moz-border-radius: 50%;
    border-radius: 50%;
    background-color: var(--t-bright);
    -webkit-transition: width 0.3s ease-in-out, height 0.3s ease-in-out;
    -moz-transition: width 0.3s ease-in-out, height 0.3s ease-in-out;
    transition: width 0.3s ease-in-out, height 0.3s ease-in-out;
    -webkit-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
  }

  .no-touch .hover-circle:hover {
    color: var(--t-opp-bright);
    background-color: var(--t-bright);
    -webkit-transition: color 0.3s 0s, background-color 0.3s 0.2s, -webkit-box-shadow 0.5s, -webkit-transform 0.3s ease-in-out;
    transition: color 0.3s 0s, background-color 0.3s 0.2s, -webkit-box-shadow 0.5s, -webkit-transform 0.3s ease-in-out;
    -moz-transition: color 0.3s 0s, background-color 0.3s 0.2s, box-shadow 0.5s, transform 0.3s ease-in-out, -moz-box-shadow 0.5s, -moz-transform 0.3s ease-in-out;
    transition: color 0.3s 0s, background-color 0.3s 0.2s, box-shadow 0.5s, transform 0.3s ease-in-out;
    transition: color 0.3s 0s, background-color 0.3s 0.2s, box-shadow 0.5s, transform 0.3s ease-in-out, -webkit-box-shadow 0.5s, -moz-box-shadow 0.5s, -webkit-transform 0.3s ease-in-out, -moz-transform 0.3s ease-in-out;
  }

  .no-touch .hover-circle:hover em {
    width: 225%;
    height: 225%;
  }

  .no-touch .hover-circle:hover i {
    color: var(--t-opp-bright);
    -webkit-transition: color 0.3s 0s;
    -moz-transition: color 0.3s 0s;
    transition: color 0.3s 0s;
  }
}

.btn-circle-icon {
  width: 10rem;
  height: 10rem;
  -moz-border-radius: 50%;
  border-radius: 50%;
  border: 1px solid var(--neutral-bright);
  background-color: var(--neutral-bright);
  font-size: 2.4rem;
  color: var(--t-opp-bright);
}

@media only screen and (min-width: 768px) {
  .btn-circle-icon {
    width: 15.4rem;
    height: 15.4rem;
    font-size: 3.6rem;
  }
}

@media only screen and (min-width: 1200px) {
  .btn-circle-icon {
    width: 17.4rem;
    height: 17.4rem;
    border: 1px solid var(--stroke-controls);
    background-color: transparent;
    color: var(--t-bright);
    font-size: 3.8rem;
  }

  .btn-circle-icon i {
    display: block;
    position: relative;
    overflow: hidden;
  }
}

@media only screen and (min-width: 1400px) {
  .btn-circle-icon {
    width: 19rem;
    height: 19rem;
    font-size: 4rem;
  }
}

@media only screen and (min-width: 1600px) {
  .btn-circle-icon {
    width: 26.6rem;
    height: 26.6rem;
    font-size: 5rem;
  }
}

.btn-line-circle-icon {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -moz-box-orient: horizontal;
  -moz-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: start;
  -moz-box-pack: start;
  -ms-flex-pack: start;
  justify-content: flex-start;
  -webkit-box-align: center;
  -moz-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font: normal var(--font-weight-medium) 1.8rem var(--_font-default);
  color: var(--t-bright);
}

.btn-line-circle-icon .btn-caption {
  position: relative;
  display: -webkit-inline-box;
  display: -moz-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  margin-right: 2rem;
}

.btn-line-circle-icon .circle {
  position: relative;
  width: 10rem;
  height: 10rem;
  -moz-border-radius: 50%;
  border-radius: 50%;
  border: 1px solid var(--neutral-bright);
  background-color: var(--neutral-bright);
  overflow: hidden;
  -webkit-transition: all 0.3s ease-in-out;
  -moz-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}

.btn-line-circle-icon .circle em {
  position: absolute;
  display: block;
  width: 0;
  height: 0;
  -moz-border-radius: 50%;
  border-radius: 50%;
  background: var(--t-bright);
  -webkit-transition: width 0.3s ease-in-out, height 0.3s ease-in-out;
  -moz-transition: width 0.3s ease-in-out, height 0.3s ease-in-out;
  transition: width 0.3s ease-in-out, height 0.3s ease-in-out;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  z-index: 1;
}

.btn-line-circle-icon .circle i {
  display: block;
  position: absolute;
  left: 50%;
  top: 50%;
  bottom: auto;
  right: auto;
  -webkit-transform: translateX(-50%) translateY(-50%);
  -moz-transform: translateX(-50%) translateY(-50%);
  -ms-transform: translateX(-50%) translateY(-50%);
  transform: translateX(-50%) translateY(-50%);
  font-size: 2.3rem;
  color: var(--t-opp-bright);
  z-index: 2;
  overflow: hidden;
  -webkit-transition: color 0.3s 0s;
  -moz-transition: color 0.3s 0s;
  transition: color 0.3s 0s;
}

.btn-line-circle-icon:hover {
  color: var(--t-medium);
}

.btn-line-circle-icon.permanent-dark {
  color: var(--t-per-bright-dark);
}

.btn-line-circle-icon.permanent-dark .circle {
  border: 1px solid var(--t-per-bright-dark);
  background-color: var(--t-per-bright-dark);
}

.btn-line-circle-icon.permanent-dark em {
  background: var(--t-per-bright-dark);
}

.btn-line-circle-icon.permanent-dark i {
  color: var(--t-per-bright-light);
}

@media only screen and (min-width: 1200px) {
  .btn-line-circle-icon {
    font-size: 2rem;
  }

  .btn-line-circle-icon .circle {
    border: 1px solid var(--stroke-controls);
    background-color: transparent;
  }

  .btn-line-circle-icon .circle i {
    font-size: 2.5rem;
    color: var(--t-bright);
  }
}

@media only screen and (min-width: 1400px) {
  .btn-line-circle-icon {
    font-size: 2.2rem;
  }

  .btn-line-circle-icon .circle i {
    font-size: 2.7rem;
  }
}

@media only screen and (min-width: 1600px) {
  .btn-line-circle-icon {
    font-size: 2.6rem;
  }

  .btn-line-circle-icon .circle i {
    font-size: 3.2rem;
  }
}

.btn-to-top {
  position: fixed;
  right: 2rem;
  bottom: 2rem;
  width: 4rem;
  height: 4rem;
  -moz-border-radius: var(--_radius-s);
  border-radius: var(--_radius-s);
  background-color: rgba(0, 0, 0, 0.03);
  -webkit-backdrop-filter: blur(6px);
  backdrop-filter: blur(6px);
  color: var(--t-bright);
  font-size: 1.8rem;
  z-index: 9;
  overflow: hidden;
}

.btn-to-top i {
  display: -webkit-inline-box;
  display: -moz-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  position: relative;
  overflow: hidden;
}

@media only screen and (min-width: 768px) {
  .btn-to-top {
    position: fixed;
    right: 5rem;
    bottom: 3rem;
  }
}

@media only screen and (min-width: 1200px) {
  .btn-to-top {
    font-size: 1.9rem;
  }

  .no-touch .btn-to-top:hover {
    -webkit-backdrop-filter: blur(6px);
    backdrop-filter: blur(6px);
  }
}

@media only screen and (min-width: 1400px) {
  .btn-to-top {
    width: 5rem;
    height: 5rem;
  }
}

@media only screen and (min-width: 1600px) {
  .btn-to-top {
    font-size: 2.2rem;
  }
}

.slide-right:hover i::before {
  -webkit-animation: 0.4s ease-in-out 0s 1 both slideRight;
  -moz-animation: 0.4s ease-in-out 0s 1 both slideRight;
  animation: 0.4s ease-in-out 0s 1 both slideRight;
}

.slide-right-up:hover i::before {
  -webkit-animation: 0.4s ease-in-out 0s 1 both slideRightUp;
  -moz-animation: 0.4s ease-in-out 0s 1 both slideRightUp;
  animation: 0.4s ease-in-out 0s 1 both slideRightUp;
}

.slide-right-down:hover i::before {
  -webkit-animation: 0.4s ease-in-out 0s 1 both slideRightDown;
  -moz-animation: 0.4s ease-in-out 0s 1 both slideRightDown;
  animation: 0.4s ease-in-out 0s 1 both slideRightDown;
}

.slide-left:hover i::before {
  -webkit-animation: 0.4s ease-in-out 0s 1 both slideLeft;
  -moz-animation: 0.4s ease-in-out 0s 1 both slideLeft;
  animation: 0.4s ease-in-out 0s 1 both slideLeft;
}

.slide-left-up:hover i::before {
  -webkit-animation: 0.4s ease-in-out 0s 1 both slideLeftUp;
  -moz-animation: 0.4s ease-in-out 0s 1 both slideLeftUp;
  animation: 0.4s ease-in-out 0s 1 both slideLeftUp;
}

.slide-left-down:hover i::before {
  -webkit-animation: 0.4s ease-in-out 0s 1 both slideLeftDown;
  -moz-animation: 0.4s ease-in-out 0s 1 both slideLeftDown;
  animation: 0.4s ease-in-out 0s 1 both slideLeftDown;
}

.slide-down:hover i::before {
  -webkit-animation: 0.4s ease-in-out 0s 1 both slideDown;
  -moz-animation: 0.4s ease-in-out 0s 1 both slideDown;
  animation: 0.4s ease-in-out 0s 1 both slideDown;
}

.slide-up:hover i::before {
  -webkit-animation: 0.4s ease-in-out 0s 1 both slideUp;
  -moz-animation: 0.4s ease-in-out 0s 1 both slideUp;
  animation: 0.4s ease-in-out 0s 1 both slideUp;
}

@-webkit-keyframes slideRight {
  0% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }

  50% {
    -webkit-transform: translateX(100%);
    transform: translateX(100%);
  }

  51% {
    opacity: 0;
  }

  52% {
    -webkit-transform: translateX(-100%);
    transform: translateX(-100%);
    opacity: 0;
  }

  53% {
    opacity: 1;
  }

  100% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
}

@-moz-keyframes slideRight {
  0% {
    -moz-transform: translateX(0);
    transform: translateX(0);
  }

  50% {
    -moz-transform: translateX(100%);
    transform: translateX(100%);
  }

  51% {
    opacity: 0;
  }

  52% {
    -moz-transform: translateX(-100%);
    transform: translateX(-100%);
    opacity: 0;
  }

  53% {
    opacity: 1;
  }

  100% {
    -moz-transform: translateX(0);
    transform: translateX(0);
  }
}

@keyframes slideRight {
  0% {
    -webkit-transform: translateX(0);
    -moz-transform: translateX(0);
    transform: translateX(0);
  }

  50% {
    -webkit-transform: translateX(100%);
    -moz-transform: translateX(100%);
    transform: translateX(100%);
  }

  51% {
    opacity: 0;
  }

  52% {
    -webkit-transform: translateX(-100%);
    -moz-transform: translateX(-100%);
    transform: translateX(-100%);
    opacity: 0;
  }

  53% {
    opacity: 1;
  }

  100% {
    -webkit-transform: translateX(0);
    -moz-transform: translateX(0);
    transform: translateX(0);
  }
}

@-webkit-keyframes slideRightUp {
  0% {
    -webkit-transform: translateX(0) translateY(0);
    transform: translateX(0) translateY(0);
  }

  50% {
    -webkit-transform: translateX(100%) translateY(-100%);
    transform: translateX(100%) translateY(-100%);
  }

  51% {
    opacity: 0;
  }

  52% {
    -webkit-transform: translateX(-100%) translateY(100%);
    transform: translateX(-100%) translateY(100%);
    opacity: 0;
  }

  53% {
    opacity: 1;
  }

  100% {
    -webkit-transform: translateX(0) translateY(0);
    transform: translateX(0) translateY(0);
  }
}

@-moz-keyframes slideRightUp {
  0% {
    -moz-transform: translateX(0) translateY(0);
    transform: translateX(0) translateY(0);
  }

  50% {
    -moz-transform: translateX(100%) translateY(-100%);
    transform: translateX(100%) translateY(-100%);
  }

  51% {
    opacity: 0;
  }

  52% {
    -moz-transform: translateX(-100%) translateY(100%);
    transform: translateX(-100%) translateY(100%);
    opacity: 0;
  }

  53% {
    opacity: 1;
  }

  100% {
    -moz-transform: translateX(0) translateY(0);
    transform: translateX(0) translateY(0);
  }
}

@keyframes slideRightUp {
  0% {
    -webkit-transform: translateX(0) translateY(0);
    -moz-transform: translateX(0) translateY(0);
    transform: translateX(0) translateY(0);
  }

  50% {
    -webkit-transform: translateX(100%) translateY(-100%);
    -moz-transform: translateX(100%) translateY(-100%);
    transform: translateX(100%) translateY(-100%);
  }

  51% {
    opacity: 0;
  }

  52% {
    -webkit-transform: translateX(-100%) translateY(100%);
    -moz-transform: translateX(-100%) translateY(100%);
    transform: translateX(-100%) translateY(100%);
    opacity: 0;
  }

  53% {
    opacity: 1;
  }

  100% {
    -webkit-transform: translateX(0) translateY(0);
    -moz-transform: translateX(0) translateY(0);
    transform: translateX(0) translateY(0);
  }
}

@-webkit-keyframes slideRightDown {
  0% {
    -webkit-transform: translateX(0) translateY(0);
    transform: translateX(0) translateY(0);
  }

  50% {
    -webkit-transform: translateX(100%) translateY(100%);
    transform: translateX(100%) translateY(100%);
  }

  51% {
    opacity: 0;
  }

  52% {
    -webkit-transform: translateX(-100%) translateY(-100%);
    transform: translateX(-100%) translateY(-100%);
    opacity: 0;
  }

  53% {
    opacity: 1;
  }

  100% {
    -webkit-transform: translateX(0) translateY(0);
    transform: translateX(0) translateY(0);
  }
}

@-moz-keyframes slideRightDown {
  0% {
    -moz-transform: translateX(0) translateY(0);
    transform: translateX(0) translateY(0);
  }

  50% {
    -moz-transform: translateX(100%) translateY(100%);
    transform: translateX(100%) translateY(100%);
  }

  51% {
    opacity: 0;
  }

  52% {
    -moz-transform: translateX(-100%) translateY(-100%);
    transform: translateX(-100%) translateY(-100%);
    opacity: 0;
  }

  53% {
    opacity: 1;
  }

  100% {
    -moz-transform: translateX(0) translateY(0);
    transform: translateX(0) translateY(0);
  }
}

@keyframes slideRightDown {
  0% {
    -webkit-transform: translateX(0) translateY(0);
    -moz-transform: translateX(0) translateY(0);
    transform: translateX(0) translateY(0);
  }

  50% {
    -webkit-transform: translateX(100%) translateY(100%);
    -moz-transform: translateX(100%) translateY(100%);
    transform: translateX(100%) translateY(100%);
  }

  51% {
    opacity: 0;
  }

  52% {
    -webkit-transform: translateX(-100%) translateY(-100%);
    -moz-transform: translateX(-100%) translateY(-100%);
    transform: translateX(-100%) translateY(-100%);
    opacity: 0;
  }

  53% {
    opacity: 1;
  }

  100% {
    -webkit-transform: translateX(0) translateY(0);
    -moz-transform: translateX(0) translateY(0);
    transform: translateX(0) translateY(0);
  }
}

@-webkit-keyframes slideLeft {
  0% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }

  50% {
    -webkit-transform: translateX(-100%);
    transform: translateX(-100%);
  }

  51% {
    opacity: 0;
  }

  52% {
    -webkit-transform: translateX(100%);
    transform: translateX(100%);
    opacity: 0;
  }

  53% {
    opacity: 1;
  }

  100% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
}

@-moz-keyframes slideLeft {
  0% {
    -moz-transform: translateX(0);
    transform: translateX(0);
  }

  50% {
    -moz-transform: translateX(-100%);
    transform: translateX(-100%);
  }

  51% {
    opacity: 0;
  }

  52% {
    -moz-transform: translateX(100%);
    transform: translateX(100%);
    opacity: 0;
  }

  53% {
    opacity: 1;
  }

  100% {
    -moz-transform: translateX(0);
    transform: translateX(0);
  }
}

@keyframes slideLeft {
  0% {
    -webkit-transform: translateX(0);
    -moz-transform: translateX(0);
    transform: translateX(0);
  }

  50% {
    -webkit-transform: translateX(-100%);
    -moz-transform: translateX(-100%);
    transform: translateX(-100%);
  }

  51% {
    opacity: 0;
  }

  52% {
    -webkit-transform: translateX(100%);
    -moz-transform: translateX(100%);
    transform: translateX(100%);
    opacity: 0;
  }

  53% {
    opacity: 1;
  }

  100% {
    -webkit-transform: translateX(0);
    -moz-transform: translateX(0);
    transform: translateX(0);
  }
}

@-webkit-keyframes slideLeftUp {
  0% {
    -webkit-transform: translateX(0) translateY(0);
    transform: translateX(0) translateY(0);
  }

  50% {
    -webkit-transform: translateX(-100%) translateY(-100%);
    transform: translateX(-100%) translateY(-100%);
  }

  51% {
    opacity: 0;
  }

  52% {
    -webkit-transform: translateX(100%) translateY(100%);
    transform: translateX(100%) translateY(100%);
    opacity: 0;
  }

  53% {
    opacity: 1;
  }

  100% {
    -webkit-transform: translateX(0) translateY(0);
    transform: translateX(0) translateY(0);
  }
}

@-moz-keyframes slideLeftUp {
  0% {
    -moz-transform: translateX(0) translateY(0);
    transform: translateX(0) translateY(0);
  }

  50% {
    -moz-transform: translateX(-100%) translateY(-100%);
    transform: translateX(-100%) translateY(-100%);
  }

  51% {
    opacity: 0;
  }

  52% {
    -moz-transform: translateX(100%) translateY(100%);
    transform: translateX(100%) translateY(100%);
    opacity: 0;
  }

  53% {
    opacity: 1;
  }

  100% {
    -moz-transform: translateX(0) translateY(0);
    transform: translateX(0) translateY(0);
  }
}

@keyframes slideLeftUp {
  0% {
    -webkit-transform: translateX(0) translateY(0);
    -moz-transform: translateX(0) translateY(0);
    transform: translateX(0) translateY(0);
  }

  50% {
    -webkit-transform: translateX(-100%) translateY(-100%);
    -moz-transform: translateX(-100%) translateY(-100%);
    transform: translateX(-100%) translateY(-100%);
  }

  51% {
    opacity: 0;
  }

  52% {
    -webkit-transform: translateX(100%) translateY(100%);
    -moz-transform: translateX(100%) translateY(100%);
    transform: translateX(100%) translateY(100%);
    opacity: 0;
  }

  53% {
    opacity: 1;
  }

  100% {
    -webkit-transform: translateX(0) translateY(0);
    -moz-transform: translateX(0) translateY(0);
    transform: translateX(0) translateY(0);
  }
}

@-webkit-keyframes slideLeftDown {
  0% {
    -webkit-transform: translateX(0) translateY(0);
    transform: translateX(0) translateY(0);
  }

  50% {
    -webkit-transform: translateX(-100%) translateY(100%);
    transform: translateX(-100%) translateY(100%);
  }

  51% {
    opacity: 0;
  }

  52% {
    -webkit-transform: translateX(100%) translateY(-100%);
    transform: translateX(100%) translateY(-100%);
    opacity: 0;
  }

  53% {
    opacity: 1;
  }

  100% {
    -webkit-transform: translateX(0) translateY(0);
    transform: translateX(0) translateY(0);
  }
}

@-moz-keyframes slideLeftDown {
  0% {
    -moz-transform: translateX(0) translateY(0);
    transform: translateX(0) translateY(0);
  }

  50% {
    -moz-transform: translateX(-100%) translateY(100%);
    transform: translateX(-100%) translateY(100%);
  }

  51% {
    opacity: 0;
  }

  52% {
    -moz-transform: translateX(100%) translateY(-100%);
    transform: translateX(100%) translateY(-100%);
    opacity: 0;
  }

  53% {
    opacity: 1;
  }

  100% {
    -moz-transform: translateX(0) translateY(0);
    transform: translateX(0) translateY(0);
  }
}

@keyframes slideLeftDown {
  0% {
    -webkit-transform: translateX(0) translateY(0);
    -moz-transform: translateX(0) translateY(0);
    transform: translateX(0) translateY(0);
  }

  50% {
    -webkit-transform: translateX(-100%) translateY(100%);
    -moz-transform: translateX(-100%) translateY(100%);
    transform: translateX(-100%) translateY(100%);
  }

  51% {
    opacity: 0;
  }

  52% {
    -webkit-transform: translateX(100%) translateY(-100%);
    -moz-transform: translateX(100%) translateY(-100%);
    transform: translateX(100%) translateY(-100%);
    opacity: 0;
  }

  53% {
    opacity: 1;
  }

  100% {
    -webkit-transform: translateX(0) translateY(0);
    -moz-transform: translateX(0) translateY(0);
    transform: translateX(0) translateY(0);
  }
}

@-webkit-keyframes slideDown {
  0% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }

  50% {
    -webkit-transform: translateY(100%);
    transform: translateY(100%);
  }

  51% {
    opacity: 0;
  }

  52% {
    -webkit-transform: translateY(-100%);
    transform: translateY(-100%);
    opacity: 0;
  }

  53% {
    opacity: 1;
  }

  100% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }
}

@-moz-keyframes slideDown {
  0% {
    -moz-transform: translateY(0);
    transform: translateY(0);
  }

  50% {
    -moz-transform: translateY(100%);
    transform: translateY(100%);
  }

  51% {
    opacity: 0;
  }

  52% {
    -moz-transform: translateY(-100%);
    transform: translateY(-100%);
    opacity: 0;
  }

  53% {
    opacity: 1;
  }

  100% {
    -moz-transform: translateY(0);
    transform: translateY(0);
  }
}

@keyframes slideDown {
  0% {
    -webkit-transform: translateY(0);
    -moz-transform: translateY(0);
    transform: translateY(0);
  }

  50% {
    -webkit-transform: translateY(100%);
    -moz-transform: translateY(100%);
    transform: translateY(100%);
  }

  51% {
    opacity: 0;
  }

  52% {
    -webkit-transform: translateY(-100%);
    -moz-transform: translateY(-100%);
    transform: translateY(-100%);
    opacity: 0;
  }

  53% {
    opacity: 1;
  }

  100% {
    -webkit-transform: translateY(0);
    -moz-transform: translateY(0);
    transform: translateY(0);
  }
}

@-webkit-keyframes slideUp {
  0% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }

  50% {
    -webkit-transform: translateY(-100%);
    transform: translateY(-100%);
  }

  51% {
    opacity: 0;
  }

  52% {
    -webkit-transform: translateY(100%);
    transform: translateY(100%);
    opacity: 0;
  }

  53% {
    opacity: 1;
  }

  100% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }
}

@-moz-keyframes slideUp {
  0% {
    -moz-transform: translateY(0);
    transform: translateY(0);
  }

  50% {
    -moz-transform: translateY(-100%);
    transform: translateY(-100%);
  }

  51% {
    opacity: 0;
  }

  52% {
    -moz-transform: translateY(100%);
    transform: translateY(100%);
    opacity: 0;
  }

  53% {
    opacity: 1;
  }

  100% {
    -moz-transform: translateY(0);
    transform: translateY(0);
  }
}

@keyframes slideUp {
  0% {
    -webkit-transform: translateY(0);
    -moz-transform: translateY(0);
    transform: translateY(0);
  }

  50% {
    -webkit-transform: translateY(-100%);
    -moz-transform: translateY(-100%);
    transform: translateY(-100%);
  }

  51% {
    opacity: 0;
  }

  52% {
    -webkit-transform: translateY(100%);
    -moz-transform: translateY(100%);
    transform: translateY(100%);
    opacity: 0;
  }

  53% {
    opacity: 1;
  }

  100% {
    -webkit-transform: translateY(0);
    -moz-transform: translateY(0);
    transform: translateY(0);
  }
}

/* ------------------------------------------------*/
/* Elements - Controls End */
/* ------------------------------------------------*/
/* ------------------------------------------------*/
/* Elements - Forms Start */
/* ------------------------------------------------*/
.form-container {
  position: relative;
}

.form {
  opacity: 1;
  -webkit-transition: opacity var(--_animspeed-slow) ease-in-out;
  -moz-transition: opacity var(--_animspeed-slow) ease-in-out;
  transition: opacity var(--_animspeed-slow) ease-in-out;
}

.form.is-hidden {
  opacity: 0;
}

.form__item {
  padding: 0 1rem;
  margin-bottom: 3rem;
}

.form__item:last-of-type {
  margin-bottom: 0;
}

@media only screen and (min-width: 768px) {
  .form__item {
    padding: 0 1.5rem;
  }
}

@media only screen and (min-width: 1600px) {
  .form__item {
    padding: 0 2.5rem;
    margin-bottom: 5rem;
  }
}

form {
  position: relative;
  width: 100%;
}

form input,
form textarea {
  position: relative;
  display: block;
  width: 100%;
  border: none;
  outline: none;
  margin: 0;
  padding: 1.6rem 0.4rem 1.6rem 0.4rem;
  background-color: transparent;
  border-bottom: 1px solid var(--stroke-elements);
  font: normal 400 1.8rem var(--_font-default);
  color: var(--t-bright);
}

form input:focus,
form textarea:focus {
  border-bottom: 1px solid var(--stroke-controls);
}

form textarea {
  height: 16rem;
  resize: none;
}

form ::-webkit-input-placeholder {
  color: var(--t-muted);
}

form :-moz-placeholder {
  color: var(--t-muted);
}

form ::-moz-placeholder {
  color: var(--t-muted);
}

form :-ms-input-placeholder {
  color: var(--t-muted);
}

form input:focus:required:invalid,
form textarea:focus:required:invalid {
  color: var(--t-bright);
}

form input:required:valid,
form textarea:required:valid {
  color: var(--t-bright);
}

@media only screen and (min-width: 768px) {

  form input,
  form textarea {
    font-size: 2rem;
  }

  form textarea {
    height: 20rem;
  }
}

@media only screen and (min-width: 1400px) {

  form input,
  form textarea {
    font-size: 2.2rem;
  }
}

@media only screen and (min-width: 1600px) {

  form input,
  form textarea {
    font-size: 2.6rem;
  }
}

.form__reply {
  position: absolute;
  top: 0;
  right: auto;
  bottom: auto;
  left: 0;
  opacity: 0;
  -webkit-transition: opacity var(--_animspeed-slow) ease-in-out;
  -moz-transition: opacity var(--_animspeed-slow) ease-in-out;
  transition: opacity var(--_animspeed-slow) ease-in-out;
}

.form__reply.centered {
  position: absolute;
  top: auto;
  right: auto;
  bottom: auto;
  left: 50%;
  top: 50%;
  bottom: auto;
  right: auto;
  -webkit-transform: translateX(-50%) translateY(-50%);
  -moz-transform: translateX(-50%) translateY(-50%);
  -ms-transform: translateX(-50%) translateY(-50%);
  transform: translateX(-50%) translateY(-50%);
  max-width: 300px;
}

.form__reply.is-visible {
  opacity: 1;
}

.reply__icon {
  display: block;
  font-size: 6rem;
  color: var(--t-bright);
  margin-bottom: 1rem;
}

.reply__title {
  font: normal var(--font-weight-base) 2.6rem var(--_font-accent);
  color: var(--t-bright);
  margin-bottom: 1rem;
}

.reply__text {
  display: block;
  font: normal var(--font-weight-base) 1.8rem var(--_font-default);
  color: var(--t-bright);
}

/* ------------------------------------------------*/
/* Elements - Forms End */
/* ------------------------------------------------*/
/* ------------------------------------------------*/
/* Sections Main Start */
/* ------------------------------------------------*/
.main {
  position: relative;
  padding-top: 6rem;
  overflow: hidden;
}

@media only screen and (min-width: 768px) {
  .main {
    padding-top: 7rem;
  }
}

@media only screen and (min-width: 1200px) {
  .main {
    padding-top: 0;
  }
}

.main__intro {
  position: relative;
  height: auto;
  margin-bottom: 3.4rem;
}

.main__intro.intro-95-desktop {
  margin-bottom: 11.5rem;
}

.main__intro.intro-fullheight-centered {
  margin-bottom: 12rem;
}

@media only screen and (min-width: 768px) {
  .main__intro {
    margin-bottom: 4.4rem;
  }

  .main__intro.intro-95-desktop {
    margin-bottom: 14.5rem;
  }

  .main__intro.intro-fullheight-centered {
    margin-bottom: 14.3rem;
  }
}

@media only screen and (min-width: 1200px) {
  .main__intro {
    height: 100vh;
    margin-bottom: 0;
  }

  .main__intro.intro-95-desktop {
    height: 95vh;
    margin-bottom: 0;
    padding-top: 7rem;
  }

  .main__intro.intro-fullheight-centered {
    margin-bottom: 0;
    padding-top: 7rem;
  }
}

@media only screen and (min-width: 1400px) {

  .main__intro.intro-95-desktop,
  .main__intro.intro-fullheight-centered {
    padding-top: 8rem;
  }
}

.intro__background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.intro__background.intro-bg-01 img,
.intro__background.intro-bg-02 img {
  display: block;
  width: 100%;
  height: auto;
}

[data-theme="light"] .intro-bg__shadow {
  background: linear-gradient(to bottom,
      rgba(var(--base-rgb), 0) 65%,
      rgba(var(--base-rgb), 0.7) 97%);
}

[data-theme="dark"] .intro-bg__shadow {
  background: linear-gradient(to bottom,
      rgba(var(--base-rgb), 0) 78%,
      rgba(var(--base-rgb), 1) 97%);
}

.intro-bg-01__01,
.intro-bg-01__02,
.intro-bg-02__01,
.intro-bg-02__02,
.intro-bg-03__01,
.intro-bg-03__02 {
  position: absolute;
  background-repeat: no-repeat;
  background-position: center;
  -moz-background-size: cover;
  background-size: cover;
}

.intro-bg-01__01 {
  top: -160px;
  right: -100px;
  width: 320px;
  height: 320px;
}

@media only screen and (min-width: 768px) {
  .intro-bg-01__01 {
    top: -320px;
    right: -20px;
    width: 500px;
    height: 500px;
  }
}

@media only screen and (min-width: 992px) {
  .intro-bg-01__01 {
    top: -280px;
  }
}

@media only screen and (min-width: 1200px) {
  .intro-bg-01__01 {
    top: -160px;
    right: -80px;
    width: 660px;
    height: 660px;
  }
}

@media only screen and (min-width: 1400px) {
  .intro-bg-01__01 {
    top: -200px;
    right: 0;
    width: 770px;
    height: 770px;
  }
}

@media only screen and (min-width: 1600px) {
  .intro-bg-01__01 {
    top: -300px;
    right: 0;
    width: 860px;
    height: 860px;
  }
}

.intro-bg-01__02 {
  bottom: -80px;
  left: auto;
  right: 0;
  width: 150px;
  height: 150px;
}

@media only screen and (min-width: 768px) {
  .intro-bg-01__02 {
    bottom: -120px;
    left: auto;
    right: 0;
    width: 240px;
    height: 240px;
  }
}

@media only screen and (min-width: 1200px) {
  .intro-bg-01__02 {
    bottom: -40px;
    left: -96px;
    width: 300px;
    height: 300px;
  }
}

@media only screen and (min-width: 1400px) {
  .intro-bg-01__02 {
    bottom: -10px;
    left: -86px;
    width: 360px;
    height: 360px;
  }
}

@media only screen and (min-width: 1600px) {
  .intro-bg-01__02 {
    bottom: -60px;
    left: -175px;
    width: 500px;
    height: 500px;
  }
}

.intro-bg-02__01 {
  top: auto;
  bottom: -60px;
  right: 0;
  width: 160px;
  height: auto;
}

@media only screen and (min-width: 768px) {
  .intro-bg-02__01 {
    top: auto;
    bottom: -100px;
    right: 0;
    width: 260px;
    height: auto;
  }
}

@media only screen and (min-width: 992px) {
  .intro-bg-02__01 {
    top: auto;
    bottom: -80px;
    right: 0;
    width: 340px;
    height: auto;
  }
}

@media only screen and (min-width: 1200px) {
  .intro-bg-02__01 {
    top: auto;
    bottom: 80px;
    right: 0;
    width: 330px;
    height: auto;
  }
}

@media only screen and (min-width: 1400px) {
  .intro-bg-02__01 {
    top: auto;
    bottom: 100px;
    right: 0;
    width: 350px;
    height: auto;
  }
}

@media only screen and (min-width: 1600px) {
  .intro-bg-02__01 {
    top: auto;
    bottom: 60px;
    right: 0;
    width: 530px;
    height: auto;
  }
}

.intro-bg-02__02 {
  bottom: -160px;
  top: auto;
  left: -30px;
  width: 100px;
  height: auto;
}

@media only screen and (min-width: 768px) {
  .intro-bg-02__02 {
    bottom: -200px;
    top: auto;
    left: -42px;
    width: 140px;
    height: auto;
  }
}

@media only screen and (min-width: 992px) {
  .intro-bg-02__02 {
    bottom: -200px;
    top: auto;
    left: -30px;
    width: 140px;
    height: auto;
  }
}

@media only screen and (min-width: 1200px) {
  .intro-bg-02__02 {
    bottom: auto;
    top: 150px;
    left: -68px;
    width: 200px;
    height: auto;
  }
}

@media only screen and (min-width: 1400px) {
  .intro-bg-02__02 {
    bottom: auto;
    top: 210px;
    left: -82px;
    width: 240px;
    height: auto;
  }
}

@media only screen and (min-width: 1600px) {
  .intro-bg-02__02 {
    top: 150px;
    bottom: auto;
    left: -120px;
    width: 350px;
    height: auto;
  }
}

.intro-bg-03__01 {
  height: 440px;
  width: auto;
  bottom: -300px;
  right: -40px;
}

.intro-bg-03__01 img {
  display: block;
  height: 100%;
  width: auto;
}

@media only screen and (min-width: 768px) {
  .intro-bg-03__01 {
    height: 910px;
    width: auto;
    bottom: -150px;
    right: -80px;
  }
}

@media only screen and (min-width: 992px) {
  .intro-bg-03__01 {
    height: 910px;
    width: auto;
    bottom: -150px;
    right: -60px;
  }
}

@media only screen and (min-width: 1200px) {
  .intro-bg-03__01 {
    height: 110vh;
    width: auto;
    top: -10vh;
    right: -60px;
  }
}

@media only screen and (min-width: 1400px) {
  .intro-bg-03__01 {
    height: 110vh;
    width: auto;
    top: -10vh;
    right: -60px;
  }
}

@media only screen and (min-width: 1600px) {
  .intro-bg-03__01 {
    height: 126vh;
    width: auto;
    top: -16vh;
    right: -80px;
  }
}

.intro-bg-03__02 {
  height: 180px;
  width: auto;
  bottom: -220px;
  left: 40px;
}

.intro-bg-03__02 img {
  display: block;
  height: 100%;
  width: auto;
}

@media only screen and (min-width: 768px) {
  .intro-bg-03__02 {
    height: 350px;
    width: auto;
    bottom: -340px;
    left: 80px;
  }
}

@media only screen and (min-width: 992px) {
  .intro-bg-03__02 {
    height: 400px;
    width: auto;
    bottom: -340px;
    left: 150px;
  }
}

@media only screen and (min-width: 1200px) {
  .intro-bg-03__02 {
    height: 420px;
    width: auto;
    bottom: -210px;
    left: auto;
    right: 220px;
  }
}

@media only screen and (min-width: 1400px) {
  .intro-bg-03__02 {
    height: 450px;
    width: auto;
    bottom: -245px;
    right: 220px;
  }
}

@media only screen and (min-width: 1600px) {
  .intro-bg-03__02 {
    height: 540px;
    width: auto;
    bottom: -265px;
    right: 320px;
  }
}

.headline {
  position: relative;
  padding: 0 2rem;
  margin-top: 11.2rem;
}

.headline.headline-95-desktop,
.headline.headline-centered {
  margin-top: 9.5rem;
}

@media only screen and (min-width: 768px) {
  .headline {
    padding: 0 5rem;
    margin-top: 14.2rem;
  }

  .headline.headline-95-desktop,
  .headline.headline-centered {
    margin-top: 10.9rem;
  }
}

@media only screen and (min-width: 1200px) {
  .headline {
    position: absolute;
    bottom: 0;
    left: 0;
    margin-top: 0;
  }

  .headline.headline-95-desktop {
    position: absolute;
    bottom: 0;
    left: 0;
    padding-bottom: 9rem;
  }

  .headline.headline-centered {
    bottom: auto;
    left: auto;
    top: 50%;
    bottom: auto;
    -webkit-transform: translateY(-50%);
    -moz-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    transform: translateY(-50%);
    width: 100%;
    margin-top: 0;
  }
}

@media only screen and (min-width: 1400px) {
  .headline.headline-95-desktop {
    padding-bottom: 9.9rem;
  }
}

@media only screen and (min-width: 1600px) {
  .headline.headline-95-desktop {
    padding-bottom: 11.3rem;
  }
}

@media only screen and (min-width: 768px) {
  .headline__title.fullwidth {
    width: 100%;
    max-width: 1920px;
    margin: 0 auto;
  }
}

.title__line {
  position: relative;
  display: -webkit-inline-box;
  display: -moz-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
}

.title__line em {
  font: normal var(--font-weight-base) 1.8rem/1.4 var(--_font-default);
  color: var(--t-bright);
}

.title__line em,
.title__line .btn {
  display: none;
}

@media only screen and (min-width: 768px) {
  .title__line span {
    white-space: nowrap;
  }

  .title__line .btn {
    display: -webkit-inline-box;
    display: -moz-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    margin-left: 3rem;
    margin-top: 0.5rem;
  }
}

@media only screen and (min-width: 992px) {
  .title__line {
    padding: 0 3rem;
  }

  .title__line .btn {
    margin-left: 4rem;
  }
}

@media only screen and (min-width: 1200px) {
  .title__line em {
    display: -webkit-inline-box;
    display: -moz-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    max-width: 260px;
    margin-right: 5rem;
    padding-top: 3.7rem;
    font-size: 1.8rem;
  }

  .title__line .btn {
    margin-left: 5rem;
  }
}

@media only screen and (min-width: 1400px) {
  .title__line em {
    max-width: 300px;
    padding-top: 4.3rem;
    font-size: 2rem;
  }
}

@media only screen and (min-width: 1600px) {
  .title__line {
    padding: 0 5rem;
  }

  .title__line em {
    max-width: 370px;
    padding-top: 6rem;
    font-size: 2.4rem;
  }
}

.headline__datamobile {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -moz-box-orient: vertical;
  -moz-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -moz-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  width: 100%;
  text-align: left;
  margin-top: 4.5rem;
}

.headline__datamobile p {
  font: normal var(--font-weight-base) 1.8rem/1.4 var(--_font-default);
  color: var(--t-bright);
  margin-bottom: 6rem;
}

@media only screen and (min-width: 768px) {
  .headline__datamobile {
    margin-top: 5.4rem;
    padding: 0 3rem;
    text-align: center;
  }

  .headline__datamobile p {
    max-width: 370px;
    margin: 0 auto;
    font-size: 2rem;
  }

  .headline__datamobile .btn {
    display: none;
  }
}

@media only screen and (min-width: 992px) {
  .headline__datamobile {
    margin-top: 6.1rem;
  }
}

@media only screen and (min-width: 1200px) {
  .headline__datamobile {
    display: none;
  }
}

.headline__btn {
  margin-top: 9.7rem;
}

@media only screen and (min-width: 768px) {
  .headline__btn {
    margin-top: 11.9rem;
  }
}

@media only screen and (min-width: 1200px) {
  .headline__btn {
    margin-top: 5.9rem;
    margin-bottom: 4.4rem;
  }
}

@media only screen and (min-width: 1400px) {
  .headline__btn {
    margin-top: 11rem;
  }
}

@media only screen and (min-width: 1600px) {
  .headline__btn {
    margin-top: 10.1rem;
  }
}

.intro__btn {
  position: relative;
  padding: 0 2rem;
}

@media only screen and (min-width: 768px) {
  .intro__btn {
    padding: 0 5rem;
  }
}

.intro__btn-absolute {
  display: none;
}

@media only screen and (min-width: 1200px) {
  .intro__btn-absolute {
    display: block;
    position: absolute;
    right: 5rem;
    bottom: 4.4rem;
  }
}

.main__media {
  position: relative;
}

.main__media.media-grid-bottom {
  padding-bottom: 2.5rem;
}

.main__media.media-stack-bottom {
  padding-bottom: 0;
}

@media only screen and (min-width: 768px) {
  .main__media.media-grid-bottom {
    padding-bottom: 3.5rem;
  }
}

@media only screen and (min-width: 1200px) {
  .main__media.media-grid-bottom {
    padding-bottom: 3.7rem;
  }
}

@media only screen and (min-width: 1400px) {
  .main__media.media-grid-bottom {
    padding-bottom: 4.5rem;
  }
}

@media only screen and (min-width: 1600px) {
  .main__media.media-grid-bottom {
    padding-bottom: 5.5rem;
  }
}

/* ------------------------------------------------*/
/* Sections Main End */
/* ------------------------------------------------*/
/* ------------------------------------------------*/
/* Sections Inner Start */
/* ------------------------------------------------*/
.inner {
  position: relative;
}

.inner.inner-grid-bottom {
  padding: 11rem 0 2.5rem 0;
}

.inner.inner-stack {
  padding: 2.2rem 0 0.5rem 0;
}

.inner.inner-stack-bottom {
  padding: 11rem 0 0.5rem 0;
}

.inner.inner-type-bottom {
  padding: 11rem 0 2.2rem 0;
}

.inner.no-padding-top {
  padding-top: 0 !important;
}

.inner.no-padding-bottom {
  padding-bottom: 0 !important;
}

.inner.pre-no-padding-top {
  padding: 11rem 0 4.7rem 0;
}

@media only screen and (min-width: 768px) {
  .inner.inner-grid-bottom {
    padding: 16rem 0 3.5rem 0;
  }

  .inner.inner-stack {
    padding: 5.3rem 0 0.5rem 0;
  }

  .inner.inner-stack-bottom {
    padding: 16rem 0 0.5rem 0;
  }

  .inner.inner-type-bottom {
    padding: 16rem 0 2.9rem 0;
  }

  .inner.pre-no-padding-top {
    padding: 16rem 0 8.8rem 0;
  }
}

@media only screen and (min-width: 1200px) {
  .inner.inner-grid-bottom {
    padding: 16rem 0 3.7rem 0;
  }

  .inner.inner-stack {
    padding: 8.3rem 0 0.7rem 0;
  }

  .inner.inner-stack-bottom {
    padding: 16rem 0 0.7rem 0;
  }

  .inner.inner-type-bottom {
    padding: 16rem 0 3.1rem 0;
  }

  .inner.pre-no-padding-top {
    padding: 16rem 0 12rem 0;
  }
}

@media only screen and (min-width: 1400px) {
  .inner.inner-grid-bottom {
    padding: 18rem 0 4.5rem 0;
  }

  .inner.inner-stack {
    padding: 9.5rem 0 1.5rem 0;
  }

  .inner.inner-stack-bottom {
    padding: 18rem 0 1.5rem 0;
  }

  .inner.inner-type-bottom {
    padding: 18rem 0 3.9rem 0;
  }

  .inner.pre-no-padding-top {
    padding: 18rem 0 14rem 0;
  }
}

@media only screen and (min-width: 1600px) {
  .inner.inner-grid-bottom {
    padding: 21rem 0 5.5rem 0;
  }

  .inner.inner-stack {
    padding: 13.5rem 0 0.5rem 0;
  }

  .inner.inner-stack-bottom {
    padding: 21rem 0 0.5rem 0;
  }

  .inner.inner-type-bottom {
    padding: 21rem 0 4.8rem 0;
  }

  .inner.pre-no-padding-top {
    padding: 21rem 0 19rem 0;
  }
}

.inner__wrapper {
  position: relative;
  width: 100%;
  margin: 0 auto;
}

.inner__name {
  margin-bottom: 0.4rem;
}

@media only screen and (min-width: 768px) {
  .inner__name {
    margin-bottom: 1.2rem;
  }
}

@media only screen and (min-width: 1200px) {
  .inner__name {
    margin-bottom: 0;
    margin-top: 1.6rem;
  }
}

@media only screen and (min-width: 1400px) {
  .inner__name {
    margin-top: 1.8rem;
  }
}

@media only screen and (min-width: 1600px) {
  .inner__name {
    margin-top: 2.6rem;
  }
}

.inner__content {
  position: relative;
  width: 100%;
}

.bottom__background {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

.bottom__background.bottom-bg-01 img,
.bottom__background.bottom-bg-02 img,
.bottom__background.bottom-bg-03 img {
  display: block;
  width: 100%;
  height: auto;
}

.bottom-bg__shadow {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: -webkit-gradient(linear, left top, left bottom, color-stop(78%, rgba(var(--base-rgb), 0)), color-stop(97%, rgba(var(--base-rgb), 1)));
  background: -moz-linear-gradient(top, rgba(var(--base-rgb), 0) 78%, rgba(var(--base-rgb), 1) 97%);
  background: linear-gradient(to bottom, rgba(var(--base-rgb), 0) 78%, rgba(var(--base-rgb), 1) 97%);
}

.bottom-bg-01__01,
.bottom-bg-01__02,
.bottom-bg-02__01,
.bottom-bg-02__02,
.bottom-bg-03__01,
.bottom-bg-03__02 {
  position: absolute;
  background-repeat: no-repeat;
  background-position: center;
  -moz-background-size: cover;
  background-size: cover;
}

.bottom-bg-01__01 {
  bottom: 100px;
  right: -50px;
  width: 200px;
  height: auto;
}

@media only screen and (min-width: 768px) {
  .bottom-bg-01__01 {
    bottom: 130px;
    right: -50px;
    width: 200px;
    height: auto;
  }
}

@media only screen and (min-width: 992px) {
  .bottom-bg-01__01 {
    bottom: 120px;
    right: -75px;
    width: 260px;
    height: auto;
  }
}

@media only screen and (min-width: 1200px) {
  .bottom-bg-01__01 {
    bottom: 65px;
    right: -105px;
    width: 360px;
    height: auto;
  }
}

@media only screen and (min-width: 1400px) {
  .bottom-bg-01__01 {
    bottom: 70px;
    right: -120px;
    width: 400px;
    height: auto;
  }
}

@media only screen and (min-width: 1600px) {
  .bottom-bg-01__01 {
    bottom: 80px;
    right: -150px;
    width: 510px;
    height: auto;
  }
}

.bottom-bg-01__02 {
  bottom: 140px;
  left: -30px;
  width: 100px;
  height: auto;
}

@media only screen and (min-width: 768px) {
  .bottom-bg-01__02 {
    bottom: 200px;
    left: -50px;
    width: 150px;
    height: auto;
  }
}

@media only screen and (min-width: 992px) {
  .bottom-bg-01__02 {
    bottom: 200px;
    left: -60px;
    width: 170px;
    height: auto;
  }
}

@media only screen and (min-width: 1200px) {
  .bottom-bg-01__02 {
    bottom: 200px;
    left: -65px;
    width: 240px;
    height: auto;
  }
}

@media only screen and (min-width: 1400px) {
  .bottom-bg-01__02 {
    bottom: 200px;
    left: -80px;
    width: 280px;
    height: auto;
  }
}

@media only screen and (min-width: 1600px) {
  .bottom-bg-01__02 {
    bottom: 260px;
    left: -100px;
    width: 350px;
    height: auto;
  }
}

.bottom-bg-02__01 {
  bottom: -30px;
  right: -30px;
  width: 200px;
  height: auto;
}

@media only screen and (min-width: 768px) {
  .bottom-bg-02__01 {
    bottom: -40px;
    right: -60px;
  }
}

@media only screen and (min-width: 992px) {
  .bottom-bg-02__01 {
    bottom: -50px;
    right: -70px;
    width: 230px;
  }
}

@media only screen and (min-width: 1200px) {
  .bottom-bg-02__01 {
    width: 300px;
  }
}

@media only screen and (min-width: 1400px) {
  .bottom-bg-02__01 {
    bottom: -60px;
    right: -80px;
    width: 340px;
  }
}

@media only screen and (min-width: 1600px) {
  .bottom-bg-02__01 {
    width: 360px;
  }
}

.bottom-bg-02__02 {
  display: none;
}

@media only screen and (min-width: 768px) {
  .bottom-bg-02__02 {
    display: block;
    top: 50px;
    left: -35px;
    width: 140px;
    height: auto;
  }
}

@media only screen and (min-width: 992px) {
  .bottom-bg-02__02 {
    top: 50px;
    left: -35px;
    width: 140px;
  }
}

@media only screen and (min-width: 1200px) {
  .bottom-bg-02__02 {
    top: 50px;
    left: -60px;
    width: 240px;
  }
}

@media only screen and (min-width: 1400px) {
  .bottom-bg-02__02 {
    top: 60px;
    left: -70px;
    width: 280px;
  }
}

@media only screen and (min-width: 1600px) {
  .bottom-bg-02__02 {
    top: 60px;
    left: -75px;
    width: 300px;
  }
}

.bottom-bg-03__01 {
  bottom: 100px;
  right: -30px;
  width: 180px;
  height: auto;
}

@media only screen and (min-width: 768px) {
  .bottom-bg-03__01 {
    bottom: 180px;
    right: -30px;
    width: 180px;
  }
}

@media only screen and (min-width: 992px) {
  .bottom-bg-03__01 {
    bottom: 150px;
    right: -30px;
    width: 180px;
  }
}

@media only screen and (min-width: 1200px) {
  .bottom-bg-03__01 {
    bottom: 170px;
    right: -70px;
    width: 280px;
  }
}

@media only screen and (min-width: 1400px) {
  .bottom-bg-03__01 {
    bottom: 170px;
    right: -80px;
    width: 320px;
  }
}

@media only screen and (min-width: 1600px) {
  .bottom-bg-03__01 {
    bottom: 200px;
    right: -80px;
    width: 360px;
  }
}

.bottom-bg-03__02 {
  display: none;
}

@media only screen and (min-width: 768px) {
  .bottom-bg-03__02 {
    display: block;
    top: auto;
    bottom: -30px;
    left: -100px;
    width: 240px;
  }
}

@media only screen and (min-width: 992px) {
  .bottom-bg-03__02 {
    top: auto;
    bottom: -30px;
    left: -100px;
    width: 240px;
  }
}

@media only screen and (min-width: 1200px) {
  .bottom-bg-03__02 {
    top: auto;
    bottom: -50px;
    left: -150px;
    width: 360px;
  }
}

@media only screen and (min-width: 1400px) {
  .bottom-bg-03__02 {
    top: auto;
    bottom: -60px;
    left: -170px;
    width: 400px;
  }
}

@media only screen and (min-width: 1600px) {
  .bottom-bg-03__02 {
    top: auto;
    bottom: -70px;
    left: -180px;
    width: 440px;
  }
}

/* ------------------------------------------------*/
/* Sections Inner End */
/* ------------------------------------------------*/
/* ------------------------------------------------*/
/* Blocks Common Start */
/* ------------------------------------------------*/
.page-content {
  position: relative;
  width: 100%;
  min-width: 360px;
  height: auto;
  z-index: 3;
}

.fullheight-desktop {
  position: relative;
  height: auto;
}

@media only screen and (min-width: 1200px) {
  .fullheight-desktop {
    height: 100%;
  }
}

.grid-item {
  padding: 0 1rem;
  margin-top: 2rem;
}

@media only screen and (min-width: 768px) {
  .grid-item {
    padding: 0 1.5rem;
    margin-top: 3rem;
  }
}

@media only screen and (min-width: 1600px) {
  .grid-item {
    padding: 0 2.5rem;
    margin-top: 5rem;
  }
}

.grid-item-s {
  padding: 0 1rem;
  margin-top: 2rem;
}

.content__block {
  position: relative;
  padding: 0 2rem;
}

.content__block.no-padding {
  padding: 0;
}

.content__block.name-block {
  padding: 0 2rem;
}

.content__block.grid-block {
  padding: 0 1rem;
}

@media only screen and (min-width: 768px) {
  .content__block {
    padding: 0 5rem;
  }

  .content__block.name-block {
    padding: 0 5rem;
  }

  .content__block.grid-block {
    padding: 0 3.5rem;
  }
}

@media only screen and (min-width: 1200px) {
  .content__block.name-block {
    padding: 0 0 0 5rem;
  }
}

@media only screen and (min-width: 1600px) {
  .content__block.grid-block {
    padding: 0 2.5rem;
  }
}

.content__block.section-title {
  margin-bottom: 4rem;
}

.content__block.section-grid-title {
  margin-bottom: 3.6rem;
}

.content__block.section-grid-text-title {
  margin-bottom: 4rem;
  margin-top: 4rem;
  width: 100%;
  max-width: 100%;
}

.block__descr {
  width: 100%; /* Garante que o container também ocupe toda a largura */
  max-width: 100%;
  margin-top: 4rem;
}

.h2__text.type-basic-160lh {
  max-width: 100%; /* Remove qualquer limitação de largura máxima */
  width: 100%; /* Garante que ocupe toda a largura disponível */
  margin: 0; /* Remove margens que possam estar limitando a largura */
  padding: 0 0 2rem 0; /* Mantém apenas padding vertical se necessário */
  margin-top: 4rem;
}

.content__block.section-stack-title {
  margin-bottom: 0;
}

.content__block.section-tagline-title {
  margin-bottom: 4.8rem;
}

.content__block.section-form-title {
  margin-bottom: 3.5rem;
}

.content__block.large-text-block {
  margin: 10.3rem 0 10.8rem 0;
}

.content__block.large-text-block.pre-stack {
  margin-bottom: 2.8rem;
}

.content__block.pre-grid-items {
  margin-bottom: 4.8rem;
}

.content__block.pre-text-items {
  margin-bottom: 6rem;
}

.content__block.pre-offcanvas-text-block {
  margin-bottom: 12.7rem;
}

.content__block.pre-offcanvas-grid-block {
  margin-bottom: 14rem;
}

.content__block.pre-stack-block {
  position: relative;
  z-index: 1000;
  margin-bottom: -2.4rem;
}

.content__block.pre-stack-text-block {
  position: relative;
  z-index: 1000;
  margin-bottom: -2rem;
}

@media only screen and (min-width: 768px) {
  .content__block.section-text-title {
    margin-bottom: 2.7rem;
  }

  .content__block.section-grid-title {
    margin-bottom: 3.8rem;
  }

  .content__block.section-grid-text-title {
    margin-bottom: 4.9rem;
  }

  .content__block.section-stack-title {
    margin-bottom: 0;
  }

  .content__block.section-tagline-title {
    margin-bottom: 6rem;
  }

  .content__block.section-form-title {
    margin-bottom: 5.4rem;
  }

  .content__block.large-text-block {
    margin: 12.6rem 0 13.4rem 0;
  }

  .content__block.large-text-block.pre-stack {
    margin-bottom: 3.4rem;
  }

  .content__block.pre-grid-items {
    margin-bottom: 5.8rem;
  }

  .content__block.pre-text-items {
    margin-bottom: 8rem;
  }

  .content__block.pre-offcanvas-text-block {
    margin-bottom: 17.8rem;
  }

  .content__block.pre-offcanvas-grid-block {
    margin-bottom: 20rem;
  }

  .content__block.pre-stack-block {
    margin-bottom: -3.2rem;
  }

  .content__block.pre-stack-text-block {
    margin-bottom: -2.1rem;
  }
}

@media only screen and (min-width: 992px) {
  .content__block.section-text-title {
    margin-bottom: 3.4rem;
  }

  .content__block.section-grid-title {
    margin-bottom: 4.5rem;
  }

  .content__block.section-grid-text-title {
    margin-bottom: 5.6rem;
  }

  .content__block.section-stack-title {
    margin-bottom: 0;
  }

  .content__block.section-tagline-title {
    margin-bottom: 6.7rem;
  }

  .content__block.section-form-title {
    margin-bottom: 6.1rem;
  }

  .content__block.large-text-block.pre-stack {
    margin-bottom: 3.4rem;
  }

  .content__block.pre-grid-items {
    margin-bottom: 6.5rem;
  }

  .content__block.pre-text-items {
    margin-bottom: 8.7rem;
  }

  .content__block.pre-stack-block {
    margin-bottom: -2.5rem;
  }

  .content__block.pre-stack-text-block {
    margin-bottom: -1.4rem;
  }
}

@media only screen and (min-width: 1200px) {
  .content__block.section-text-title {
    margin-bottom: 3.9rem;
  }

  .content__block.section-grid-title {
    margin-bottom: 4.2rem;
  }

  .content__block.section-grid-text-title {
    margin-bottom: 5.6rem;
  }

  .content__block.section-stack-title {
    margin-bottom: 0;
  }

  .content__block.section-tagline-title {
    margin-bottom: 6.3rem;
  }

  .content__block.section-form-title {
    margin-bottom: 6.1rem;
  }

  .content__block.large-text-block {
    margin: 14.3rem 0 15.2rem 0;
  }

  .content__block.large-text-block.pre-stack {
    margin-bottom: 5.2rem;
  }

  .content__block.pre-grid-items {
    margin-bottom: 7.5rem;
  }

  .content__block.pre-text-items {
    margin-bottom: 8.6rem;
  }

  .content__block.pre-offcanvas-text-block {
    margin-bottom: 19.5rem;
  }

  .content__block.pre-offcanvas-grid-block {
    margin-bottom: 22rem;
  }

  .content__block.pre-stack-block {
    margin-bottom: -2.8rem;
  }

  .content__block.pre-stack-text-block {
    margin-bottom: -1.4rem;
  }
}

@media only screen and (min-width: 1400px) {
  .content__block.section-text-title {
    margin-bottom: 3.6rem;
  }

  .content__block.section-grid-title {
    margin-bottom: 5rem;
  }

  .content__block.section-grid-text-title {
    margin-bottom: 6.5rem;
  }

  .content__block.section-stack-title {
    margin-bottom: 0;
  }

  .content__block.section-tagline-title {
    margin-bottom: 7rem;
  }

  .content__block.section-form-title {
    margin-bottom: 6.8rem;
  }

  .content__block.large-text-block {
    margin: 17rem 0 17.9rem 0;
  }

  .content__block.large-text-block.pre-stack {
    margin-bottom: 7rem;
  }

  .content__block.pre-grid-items {
    margin-bottom: 7.5rem;
  }

  .content__block.pre-text-items {
    margin-bottom: 9.5rem;
  }

  .content__block.pre-offcanvas-text-block {
    margin-bottom: 22.2rem;
  }

  .content__block.pre-offcanvas-grid-block {
    margin-bottom: 25rem;
  }

  .content__block.pre-stack-block {
    margin-bottom: -3rem;
  }

  .content__block.pre-stack-text-block {
    margin-bottom: -1.5rem;
  }
}

@media only screen and (min-width: 1600px) {
  .content__block.section-text-title {
    margin-bottom: 1.9rem;
  }

  .content__block.section-grid-title {
    margin-bottom: 3.5rem;
  }

  .content__block.section-grid-text-title {
    margin-bottom: 5.8rem;
  }

  .content__block.section-stack-title {
    margin-bottom: 0;
  }

  .content__block.section-tagline-title {
    margin-bottom: 7.3rem;
  }

  .content__block.section-form-title {
    margin-bottom: 7.9rem;
  }

  .content__block.large-text-block {
    margin: 16.3rem 0 17.6rem 0;
  }

  .content__block.large-text-block.pre-stack {
    margin-bottom: 6.6rem;
  }

  .content__block.pre-grid-items {
    margin-bottom: 7rem;
  }

  .content__block.pre-text-items {
    margin-bottom: 10.8rem;
  }

  .content__block.pre-offcanvas-text-block {
    margin-bottom: 26.2rem;
  }

  .content__block.pre-offcanvas-grid-block {
    margin-bottom: 30rem;
  }

  .content__block.pre-stack-block {
    margin-bottom: -2.5rem;
  }

  .content__block.pre-stack-text-block {
    margin-bottom: -0.2rem;
  }
}

.block__subtitle.grid-block-subtitle {
  padding: 0 1rem;
}

@media only screen and (min-width: 768px) {
  .block__subtitle.grid-block-subtitle {
    padding: 0 1.5rem;
  }
}

@media only screen and (min-width: 1600px) {
  .block__subtitle.grid-block-subtitle {
    padding: 0 2.5rem;
  }
}

.block__subtitle {
  margin-bottom: 6.3rem;
}

.block__subtitle.grid-block-subtitle {
  margin-bottom: 4.3rem;
}

@media only screen and (min-width: 768px) {
  .block__subtitle {
    margin-bottom: 8.3rem;
  }

  .block__subtitle.grid-block-subtitle {
    margin-bottom: 5.3rem;
  }
}

@media only screen and (min-width: 992px) {
  .block__subtitle {
    margin-bottom: 9rem;
  }

  .block__subtitle.grid-block-subtitle {
    margin-bottom: 6rem;
  }
}

@media only screen and (min-width: 1200px) {
  .block__subtitle {
    margin-bottom: 9rem;
  }

  .block__subtitle.grid-block-subtitle {
    margin-bottom: 6rem;
  }
}

@media only screen and (min-width: 1400px) {
  .block__subtitle {
    margin-bottom: 9.9rem;
  }

  .block__subtitle.grid-block-subtitle {
    margin-bottom: 6.9rem;
  }
}

@media only screen and (min-width: 1600px) {
  .block__subtitle {
    margin-bottom: 11.3rem;
  }

  .block__subtitle.grid-block-subtitle {
    margin-bottom: 6.3rem;
  }
}

.fullheight-l {
  height: auto;
}

@media only screen and (min-width: 992px) {
  .fullheight-l {
    height: 100%;
  }
}

.divider {
  position: relative;
  width: 100%;
  -moz-border-radius: var(--_radius);
  border-radius: var(--_radius);
  overflow: hidden;
}

.divider-image,
.divider-video {
  background-repeat: no-repeat;
  background-position: center;
  -moz-background-size: cover;
  background-size: cover;
  height: 440px;
}

@media only screen and (min-width: 768px) {

  .divider-image,
  .divider-video {
    height: 500px;
  }
}

@media only screen and (min-width: 1400px) {

  .divider-image,
  .divider-video {
    height: 600px;
  }
}

@media only screen and (min-width: 1600px) {

  .divider-image,
  .divider-video {
    height: 700px;
  }
}

.divider-image-xl,
.divider-video-xl {
  background-repeat: no-repeat;
  background-position: center;
  -moz-background-size: cover;
  background-size: cover;
  height: 440px;
}

@media only screen and (min-width: 768px) {

  .divider-image-xl,
  .divider-video-xl {
    height: 500px;
  }
}

@media only screen and (min-width: 1200px) {

  .divider-image-xl,
  .divider-video-xl {
    height: 600px;
  }
}

@media only screen and (min-width: 1400px) {

  .divider-image-xl,
  .divider-video-xl {
    height: 700px;
  }
}

@media only screen and (min-width: 1600px) {

  .divider-image-xl,
  .divider-video-xl {
    height: 800px;
  }
}

.section-title__name {
  margin-bottom: 0.4rem;
}

@media only screen and (min-width: 768px) {
  .section-title__name {
    margin-bottom: 1.2rem;
  }
}

@media only screen and (min-width: 1200px) {
  .section-title__name {
    margin-bottom: 0;
    margin-top: 1.6rem;
  }
}

@media only screen and (min-width: 1400px) {
  .section-title__name {
    margin-top: 1.8rem;
  }
}

@media only screen and (min-width: 1600px) {
  .section-title__name {
    margin-top: 2.6rem;
  }
}

/* ------------------------------------------------*/
/* Blocks Common End */
/* ------------------------------------------------*/
/* ------------------------------------------------*/
/* Blocks Header Start */
/* ------------------------------------------------*/
.header {
  position: fixed;
  bottom: 2rem;
  left: 2rem;
  width: auto;
  z-index: 11;
  -moz-border-radius: var(--_radius-s);
  border-radius: var(--_radius-s);
  background-color: rgba(0, 0, 0, 0.03);
  -webkit-backdrop-filter: blur(6px);
  backdrop-filter: blur(6px);
}

@media only screen and (min-width: 768px) {
  .header {
    bottom: 3rem;
    left: 5rem;
  }
}

@media only screen and (min-width: 1200px) {
  .header {
    position: fixed;
    top: 3rem;
    bottom: auto;
    left: -moz-calc(16.66666667% + 3rem);
    left: calc(16.66666667% + 3rem);
    background-color: rgba(0, 0, 0, 0);
    z-index: 9;
  }
}

@media only screen and (min-width: 1400px) {
  .header {
    position: fixed;
    top: 3rem;
    bottom: auto;
    left: -moz-calc(16.66666667% + 2.5rem);
    left: calc(16.66666667% + 2.5rem);
  }
}

.header__navigation {
  position: relative;
}

@media only screen and (min-width: 1200px) {
  .header__navigation {
    height: 4rem;
  }
}

@media only screen and (min-width: 1400px) {
  .header__navigation {
    height: 5rem;
  }
}


.logo {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: flex;
  position: fixed;
  top: 2rem;
  left: 2rem;
  overflow: hidden;
  -webkit-backdrop-filter: blur(6px);
  backdrop-filter: blur(6px);
  -moz-border-radius: var(--_radius-s);
  border-radius: var(--_radius-s);
  z-index: 100;
}

@media only screen and (min-width: 768px) {
  .logo {
    top: 3rem;
    left: 5rem;
  }
}

.logo__link {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -moz-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.logo__link svg {
  width: auto;
  height: 1rem;
}

.logo__link span {
  display: -webkit-inline-box;
  display: -moz-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  padding-left: 1.4rem;
  padding-right: 1rem;
}

@media only screen and (min-width: 1400px) {
  .logo__link svg {
    height: 5rem;
  }

  .logo__link span {
    padding-left: 1.6rem;
  }
}




.color {
  display: none;
  /* Hide by default on mobile */
}

@media only screen and (min-width: 768px) {
  .color {
    display: flex;
    /* Show on desktop */
    position: fixed;
    top: 3rem;
    right: 5rem;
    align-items: center;
    justify-content: center;
    width: 4rem;
    height: 4rem;
    backdrop-filter: blur(6px);
    border-radius: var(--_radius-s);
    overflow: hidden;
    z-index: 100;
  }
}

.color-switcher {
  position: relative;
  display: -webkit-inline-box;
  display: -moz-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  border: none;
  outline: 0;
  padding: 0;
  font: inherit;
  background-color: var(--neutral-bright);
  cursor: pointer;
  text-decoration: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  width: 1.8rem;
  height: 1.8rem;
  -moz-border-radius: 50%;
  border-radius: 50%;
}

@media only screen and (min-width: 768px) {
  .color-switcher {
    width: 2.2rem;
    height: 2.2rem;
  }
}

@media only screen and (min-width: 1400px) {
  .color-switcher {
    width: 2.6rem;
    height: 2.6rem;
  }
}

.header-offset {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  height: 8rem;
  background-color: var(--base);
  z-index: 3;
}

@media only screen and (min-width: 768px) {
  .header-offset {
    height: 8.8rem;
  }
}

@media only screen and (min-width: 1200px) {
  .header-offset {
    height: 0;
  }
}

/* ------------------------------------------------*/
/* Blocks Header End */
/* ------------------------------------------------*/
/* ------------------------------------------------*/
/* Blocks Menu Start */
/* ------------------------------------------------*/
.menu__list {
  position: relative;
}

@media only screen and (min-width: 1200px) {
  .menu__list {
    height: 4rem;
  }
}

@media only screen and (min-width: 1400px) {
  .menu__list {
    height: 5rem;
  }
}

.menu__item {
  display: -webkit-inline-box;
  display: -moz-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
}

.menu__link {
  width: 4rem;
  height: 4rem;
  -moz-border-radius: var(--_radius-s);
  border-radius: var(--_radius-s);
  color: var(--t-bright);
  white-space: nowrap;
  font-size: 1.8rem;
  -webkit-transition: all 0.3s ease-in-out;
  -moz-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}

.menu__link.active {
  color: var(--t-opp-bright);
  background-color: var(--neutral-bright);
}

@media only screen and (min-width: 1200px) {
  .menu__link {
    width: auto;
    height: 4rem;
    padding: 0 2rem;
    font: normal var(--font-weight-medium) 1.6rem/4rem var(--_font-default);
    color: var(--t-medium);
  }

  .menu__link i {
    display: none;
  }

  .menu__link:hover {
    color: var(--t-bright);
  }

  .menu__link:hover .menu__caption {
    color: var(--t-bright);
  }

  .menu__link.active {
    color: var(--t-bright);
    background-color: transparent;
  }

  .menu__link.active .menu__caption {
    color: var(--t-bright);
    -moz-background-size: 0 1px, 100% 1px;
    background-size: 0 1px, 100% 1px;
  }
}

@media only screen and (min-width: 1400px) {
  .menu__link {
    height: 5rem;
    line-height: 5rem;
    padding: 0 2.5rem;
  }
}

@media only screen and (min-width: 1600px) {
  .menu__link {
    font-size: 1.8rem;
  }
}

.menu__caption {
  display: none;
}

@media only screen and (min-width: 1200px) {
  .menu__caption {
    display: -webkit-inline-box;
    display: -moz-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    text-decoration: none;
    background: -webkit-gradient(linear, left top, right top, from(rgba(0, 0, 0, 0)), to(rgba(0, 0, 0, 0))), -webkit-gradient(linear, left top, right top, from(var(--t-bright)), to(var(--t-bright)));
    background: -moz-linear-gradient(left, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0)), -moz-linear-gradient(left, var(--t-bright), var(--t-bright));
    background: linear-gradient(to right, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0)), linear-gradient(to right, var(--t-bright), var(--t-bright));
    -moz-background-size: 100% 1px, 0 1px;
    background-size: 100% 1px, 0 1px;
    background-position: 100% 100%, 0 100%;
    background-repeat: no-repeat;
    -webkit-transition: background-size 300ms, color 300ms;
    -moz-transition: background-size 300ms, color 300ms, -moz-background-size 300ms;
    transition: background-size 300ms, color 300ms;
    transition: background-size 300ms, color 300ms, -moz-background-size 300ms;
  }
}

/* ------------------------------------------------*/
/* Blocks Menu End */
/* ------------------------------------------------*/
/* ------------------------------------------------*/
/* Blocks Footer Start */
/* ------------------------------------------------*/
.footer {
  position: relative;
  padding: 10.4rem 0 0 0;
  z-index: 3;
}

.footer.footer-marquee {
  padding: 12.2rem 0 0 0;
}

@media only screen and (min-width: 768px) {
  .footer {
    padding: 14.6rem 0 0 0;
  }

  .footer.footer-marquee {
    padding: 17.2rem 0 0 0;
  }
}

@media only screen and (min-width: 1200px) {
  .footer {
    padding: 16.1rem 0 9.8rem 0;
  }

  .footer.footer-marquee {
    padding: 19rem 0 4rem 0;
  }
}

@media only screen and (min-width: 1400px) {
  .footer {
    padding: 18.1rem 0 9.7rem 0;
  }

  .footer.footer-marquee {
    padding: 21.7rem 0 4rem 0;
  }
}

@media only screen and (min-width: 1600px) {
  .footer {
    padding: 21.2rem 0 11rem 0;
  }

  .footer.footer-marquee {
    padding: 25.6rem 0 4rem 0;
  }
}

.footer__link {
  position: relative;
  margin-bottom: 6.8rem;
}

@media only screen and (min-width: 992px) {
  .footer__link {
    margin-bottom: 7.5rem;
  }
}

@media only screen and (min-width: 1200px) {
  .footer__link {
    margin-bottom: 8.2rem;
  }
}

@media only screen and (min-width: 1400px) {
  .footer__link {
    margin-bottom: 8rem;
  }
}

@media only screen and (min-width: 1600px) {
  .footer__link {
    margin-bottom: 8.5rem;
  }
}

.footer__text {
  font: normal 300 4.6rem var(--_font-accent);
  color: var(--t-bright);
}

@media only screen and (min-width: 768px) {
  .footer__text {
    font-size: 8rem;
  }
}

@media only screen and (min-width: 1200px) {
  .footer__text {
    font-size: 9rem;
  }
}

@media only screen and (min-width: 1400px) {
  .footer__text {
    font-size: 10rem;
  }
}

@media only screen and (min-width: 1600px) {
  .footer__text {
    font-size: 14rem;
  }
}

.footer__btn {
  margin-top: 2.8rem;
}

@media only screen and (min-width: 768px) {
  .footer__btn {
    margin-top: 0;
  }
}

.footer__divider {
  position: relative;
  width: 100%;
  height: 1px;
  background-color: var(--stroke-elements);
}

.footer-logo {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -moz-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.footer-logo svg {
  width: 4rem;
  height: auto;
}

.footer-logo span {
  display: -webkit-inline-box;
  display: -moz-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  padding-left: 1.4rem;
}

@media only screen and (min-width: 1400px) {
  .footer-logo svg {
    width: 5rem;
  }

  .footer-logo span {
    padding-left: 1.6rem;
  }
}

/* ------------------------------------------------*/
/* Blocks Footer End */
/* ------------------------------------------------*/
/* ------------------------------------------------*/
/* Blocks Popup Dialogs Start */
/* ------------------------------------------------*/
.popup {
  position: relative;
  background-color: var(--base);
  -moz-border-radius: var(--_radius);
  border-radius: var(--_radius);
  padding: 0;
  width: -moz-calc(100% - 4rem);
  width: calc(100% - 4rem);
  margin: 2rem auto;
  overflow: hidden;
  -webkit-box-shadow: 0 19px 38px rgba(0, 0, 0, 0.3), 0 15px 12px rgba(0, 0, 0, 0.22);
  -moz-box-shadow: 0 19px 38px rgba(0, 0, 0, 0.3), 0 15px 12px rgba(0, 0, 0, 0.22);
  box-shadow: 0 19px 38px rgba(0, 0, 0, 0.3), 0 15px 12px rgba(0, 0, 0, 0.22);
  -ms-scroll-chaining: none;
  overscroll-behavior: contain;
}

@media only screen and (min-width: 768px) {
  .popup {
    margin: 3rem auto;
    width: -moz-calc(100% - 10rem);
    width: calc(100% - 10rem);
  }
}

@media only screen and (min-width: 1600px) {
  .popup {
    max-width: 1620px;
  }
}

.project__block {
  padding: 0 2rem;
}

.project__block.no-padding {
  padding: 0;
}

.project__block.grid-block {
  padding: 0 1rem;
}

@media only screen and (min-width: 768px) {
  .project__block {
    padding: 0 5rem;
  }

  .project__block.grid-block {
    padding: 0 3.5rem;
  }
}

@media only screen and (min-width: 1600px) {
  .project__block.grid-block {
    padding: 0 2.5rem;
  }
}

.project__block {
  margin-top: 6rem;
  margin-bottom: 5.9rem;
}

.project__block.pre-grid-items {
  margin-bottom: 3.9rem;
}

.project__block.grid-items {
  margin-top: 3.8rem;
}

.project__block.normal-size {
  margin-top: 6rem;
  margin-bottom: 6.8rem;
}

.project__block.small-size {
  margin: 2rem 0;
}

.project__block.no-margin {
  margin: 0;
}

.project__block.no-margin-bottom {
  margin-bottom: 0;
}

@media only screen and (min-width: 768px) {
  .project__block {
    margin-top: 8rem;
    margin-bottom: 7.9rem;
  }

  .project__block.pre-grid-items {
    margin-bottom: 4.9rem;
  }

  .project__block.grid-items {
    margin-top: 5rem;
  }

  .project__block.normal-size {
    margin-top: 8rem;
    margin-bottom: 8.8rem;
  }

  .project__block.small-size {
    margin: 5rem 0;
  }

  .project__block.no-margin-bottom {
    margin-bottom: 0;
  }
}

@media only screen and (min-width: 992px) {
  .project__block {
    margin-top: 8.7rem;
    margin-bottom: 8.6rem;
  }

  .project__block.pre-grid-items {
    margin-bottom: 5.6rem;
  }

  .project__block.grid-items {
    margin-top: 5.7rem;
  }

  .project__block.normal-size {
    margin-top: 8.7rem;
    margin-bottom: 9.5rem;
  }

  .project__block.no-margin-bottom {
    margin-bottom: 0;
  }
}

@media only screen and (min-width: 1200px) {
  .project__block {
    margin-top: 8.2rem;
    margin-bottom: 8.6rem;
  }

  .project__block.pre-grid-items {
    margin-bottom: 5.6rem;
  }

  .project__block.grid-items {
    margin-top: 5.2rem;
  }

  .project__block.normal-size {
    margin-top: 9.5rem;
    margin-bottom: 9.5rem;
  }

  .project__block.no-margin-bottom {
    margin-bottom: 0;
  }
}

@media only screen and (min-width: 1400px) {
  .project__block {
    margin-top: 9.1rem;
    margin-bottom: 9.4rem;
  }

  .project__block.pre-grid-items {
    margin-bottom: 6.5rem;
  }

  .project__block.grid-items {
    margin-top: 6.1rem;
  }

  .project__block.normal-size {
    margin-top: 10.5rem;
    margin-bottom: 10.5rem;
  }

  .project__block.no-margin-bottom {
    margin-bottom: 0;
  }
}

@media only screen and (min-width: 1600px) {
  .project__block {
    margin-top: 10.4rem;
    margin-bottom: 10.7rem;
  }

  .project__block.pre-grid-items {
    margin-bottom: 5.7rem;
  }

  .project__block.grid-items {
    margin-top: 5.4rem;
  }

  .project__block.normal-size {
    margin-top: 12rem;
    margin-bottom: 12rem;
  }

  .project__block.no-margin-bottom {
    margin-bottom: 0;
  }
}

.project-image-bg {
  position: relative;
  width: 100%;
  background-repeat: no-repeat;
  background-position: top center;
  -moz-background-size: cover;
  background-size: cover;
  overflow: hidden;
}

@media only screen and (min-width: 768px) {
  .project-image-bg {
    height: auto;
  }
}

@media (orientation: landscape) {
  .project-image-bg__landscape {
    display: block;
  }

  .project-image-bg__portrait {
    display: none;
  }
}

@media (orientation: portrait) {
  .project-image-bg__portrait {
    display: block;
  }

  .project-image-bg__landscape {
    display: none;
  }
}

.project__title {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 2.4rem 2rem;
}

@media only screen and (min-width: 768px) {
  .project__title {
    padding: 2.6rem 5rem;
  }
}

@media only screen and (min-width: 1200px) {
  .project__title {
    padding: 4rem 5rem 4.6rem 5rem;
  }
}

.project__subtitle {
  margin-bottom: 2.8rem;
}

.project__subtitle.image-top-subtitle {
  margin-bottom: 3rem;
}

@media only screen and (min-width: 768px) {
  .project__subtitle {
    margin-bottom: 3.4rem;
  }

  .project__subtitle.image-top-subtitle {
    margin-bottom: 4.5rem;
  }
}

@media only screen and (min-width: 1200px) {
  .project__subtitle {
    margin-bottom: 0;
    padding-top: 0.4rem;
  }

  .project__subtitle.image-top-subtitle {
    margin-bottom: 0;
    padding-top: 0;
  }
}

.project__illustration,
.project__illustration-xl {
  position: relative;
  -moz-border-radius: var(--_radius);
  border-radius: var(--_radius);
  overflow: hidden;
}

.project__data {
  position: relative;
}

@media only screen and (min-width: 1600px) {
  .pdata__item {
    padding-top: 0.4rem;
  }
}

.project__feedback {
  position: relative;
  margin-bottom: 5.9rem;
}

@media only screen and (min-width: 768px) {
  .project__feedback {
    margin-bottom: 8rem;
  }
}

@media only screen and (min-width: 992px) {
  .project__feedback {
    margin-bottom: 8.6rem;
  }
}

@media only screen and (min-width: 1200px) {
  .project__feedback {
    margin-bottom: 8.6rem;
  }
}

@media only screen and (min-width: 1400px) {
  .project__feedback {
    margin-bottom: 9.5rem;
  }
}

@media only screen and (min-width: 1600px) {
  .project__feedback {
    margin-bottom: 10.7rem;
  }
}

.fauthor__avatar {
  width: 60px;
  min-width: 60px;
  height: 60px;
  margin-right: 2rem;
  -moz-border-radius: var(--_radius);
  border-radius: var(--_radius);
  overflow: hidden;
}

@media only screen and (min-width: 768px) {
  .fauthor__avatar {
    width: 120px;
    min-width: 120px;
    height: 120px;
    margin-right: 3rem;
  }
}

.fauthor__name {
  color: var(--t-bright);
}

.fauthor__position {
  margin-top: 0.6rem;
}

@media only screen and (min-width: 1600px) {
  .fauthor__position {
    margin-top: 0.8rem;
  }
}

.fauthor__rating {
  margin-top: 1.3rem;
}

.fauthor__rating i {
  display: block;
  margin-right: 1rem;
  font-size: 1.6rem;
  color: var(--t-bright);
}

.fauthor__rating i:last-of-type {
  margin-right: 0;
}

@media only screen and (min-width: 1400px) {
  .fauthor__rating i {
    font-size: 1.8rem;
  }
}

@media only screen and (min-width: 1600px) {
  .fauthor__rating {
    margin-bottom: 0.4rem;
  }

  .fauthor__rating i {
    font-size: 2rem;
  }
}

.feedback__descr {
  margin-top: 2.8rem;
}

@media only screen and (min-width: 768px) {
  .feedback__descr {
    margin-top: 3.8rem;
  }
}

@media only screen and (min-width: 1200px) {
  .feedback__descr {
    margin-top: 3.6rem;
  }
}

@media only screen and (min-width: 1400px) {
  .feedback__descr {
    margin-top: 3.4rem;
  }
}

.project__divider {
  position: relative;
  width: 100%;
  height: 1px;
  background-color: var(--stroke-elements);
}

.mfp-wrap {
  position: fixed !important;
}

.mfp-fade {
  overflow: hidden;
}

.mfp-fade .mfp-container {
  position: fixed;
  overflow-y: scroll;
}

.mfp-bg {
  opacity: 1;
  background-color: rgba(0, 0, 0, 0.6);
}

.mfp-container {
  padding: 0;
}

.mfp-close-btn-in .mfp-close {
  width: 4rem;
  height: 4rem;
  -moz-border-radius: var(--_radius-s);
  border-radius: var(--_radius-s);
  border: 1px solid var(--t-per-bright-dark);
  right: 3rem;
  top: 3rem;
  opacity: 1;
  padding: 0;
  font: inherit;
  color: var(--t-per-bright-dark);
  font-size: 1.8rem;
  line-height: 1;
  -webkit-transition: all 0.3s ease-in-out;
  -moz-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}

.mfp-close-btn-in .mfp-close::before {
  display: -webkit-inline-box;
  display: -moz-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  font-family: "Phosphor";
  content: "\eddb";
  position: absolute;
  left: 50%;
  top: 50%;
  bottom: auto;
  right: auto;
  -webkit-transform: translateX(-50%) translateY(-50%);
  -moz-transform: translateX(-50%) translateY(-50%);
  -ms-transform: translateX(-50%) translateY(-50%);
  transform: translateX(-50%) translateY(-50%);
}

.mfp-close-btn-in .mfp-close.permanent-dark {
  border: 1px solid var(--t-per-bright-dark);
  background-color: var(--t-per-bright-dark);
  color: var(--t-per-bright-light);
}

.mfp-close-btn-in .mfp-close.permanent-light {
  border: 1px solid var(--t-per-bright-light);
  background-color: var(--t-per-bright-light);
  color: var(--t-per-bright-dark);
}

@media only screen and (min-width: 1200px) {
  .mfp-close-btn-in .mfp-close.permanent-dark {
    border: 1px solid var(--t-per-bright-dark);
    background-color: transparent;
    color: var(--t-per-bright-dark);
  }

  .mfp-close-btn-in .mfp-close.permanent-light {
    border: 1px solid var(--t-per-bright-light);
    background-color: transparent;
    color: var(--t-per-bright-light);
  }

  .no-touch .mfp-close-btn-in .mfp-close:hover.permanent-dark {
    border: 1px solid var(--t-per-bright-dark);
    background-color: var(--t-per-bright-dark);
    color: var(--neutral-white);
  }

  .no-touch .mfp-close-btn-in .mfp-close:hover.permanent-light {
    border: 1px solid var(--t-per-bright-light);
    background-color: var(--t-per-bright-light);
    color: var(--t-per-bright-dark);
  }
}

@media only screen and (min-width: 1400px) {
  .mfp-close-btn-in .mfp-close {
    width: 5rem;
    height: 5rem;
  }
}

/* overlay at start */
.mfp-fade.mfp-bg {
  opacity: 0;
  -webkit-transition: all 0.3s;
  -moz-transition: all 0.3s;
  transition: all 0.3s;
}

/* overlay animate in */
.mfp-fade.mfp-bg.mfp-ready {
  opacity: 1;
}

/* overlay animate out */
.mfp-fade.mfp-bg.mfp-removing {
  opacity: 0;
}

/* content at start */
.mfp-fade.mfp-wrap .mfp-content {
  opacity: 0;
  -webkit-transform: translateY(3rem);
  -moz-transform: translateY(3rem);
  -ms-transform: translateY(3rem);
  transform: translateY(3rem);
  -webkit-transition: all 0.3s var(--_animbezier);
  -moz-transition: all 0.3s var(--_animbezier);
  transition: all 0.3s var(--_animbezier);
}

/* content animate it */
.mfp-fade.mfp-wrap.mfp-ready .mfp-content {
  opacity: 1;
  -webkit-transform: translateY(0);
  -moz-transform: translateY(0);
  -ms-transform: translateY(0);
  transform: translateY(0);
}

/* content animate out */
.mfp-fade.mfp-wrap.mfp-removing .mfp-content {
  opacity: 0;
  -webkit-transform: translateY(3rem);
  -moz-transform: translateY(3rem);
  -ms-transform: translateY(3rem);
  transform: translateY(3rem);
}

/* ------------------------------------------------*/
/* Blocks Popup Dialogs End */
/* ------------------------------------------------*/
/* ------------------------------------------------*/
/* Features Marquee Start */
/* ------------------------------------------------*/
.items {
  max-width: 100%;
  overflow: hidden;
}

.items--gsap {
  color: #fff;
}

.items--gsap .items__container {
  translate: -moz-calc(-100% + 100vw) !important;
  translate: calc(-100% + 100vw) !important;
}

.items__caption {
  max-width: 50vw;
  padding: 0 1.5rem;
}

.items__container {
  position: relative;
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: flex;
  width: -webkit-max-content;
  width: -moz-max-content;
  width: max-content;
  will-change: transform;
}

.items .item {
  margin-right: 2rem;
}

@media only screen and (min-width: 768px) {
  .items .item {
    margin-right: 3rem;
  }
}

@media only screen and (min-width: 1600px) {
  .items .item {
    margin-right: 5rem;
  }
}

.item {
  position: relative;
  width: 45vw;
  height: auto;
  background-color: var(--base);
  -moz-border-radius: 20px;
  border-radius: 20px;
  overflow: hidden;
  color: var(--t-bright);
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -moz-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 20px;
  -webkit-animation: animateZ 1s infinite;
  -moz-animation: animateZ 1s infinite;
  animation: animateZ 1s infinite;
}

.item.item-regular {
  width: auto;
  -moz-border-radius: 0;
  border-radius: 0;
  gap: 0;
  background-color: transparent;
  color: var(--t-bright);
  -webkit-animation: animateZ 1s infinite;
  -moz-animation: animateZ 1s infinite;
  animation: animateZ 1s infinite;
}

.item.icon {
  border: 1px solid var(--stroke-elements);
  -webkit-box-pack: center;
  -moz-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.item.icon svg,
.item.icon img {
  display: block;
  height: 70px;
  width: auto;
}

.item.image {
  background-repeat: no-repeat;
  background-position: center;
  -moz-background-size: cover;
  background-size: cover;
}

.item.image img {
  display: block;
  width: 100%;
  height: auto;
}

@media only screen and (min-width: 768px) {

  .item.icon svg,
  .item.icon img {
    height: 120px;
  }
}

@media only screen and (min-width: 992px) {
  .item {
    width: 30vw;
  }
}

@media only screen and (min-width: 1200px) {
  .item {
    width: 25vw;
  }
}

@media only screen and (min-width: 1600px) {
  .item {
    width: 20vw;
  }

  .item.icon svg,
  .item.icon img {
    height: 140px;
  }
}

.item__text {
  margin-right: 2rem;
}

.item__text.text-with-gliph {
  margin-right: 0;
}

.item__text.text-with-gliph::after {
  content: "/";
  display: inline;
  padding-left: 2rem;
}

@media only screen and (min-width: 768px) {
  .item__text {
    margin-right: 3rem;
  }

  .item__text.text-with-gliph::after {
    padding-left: 3rem;
  }
}

@media only screen and (min-width: 1600px) {
  .item__text {
    margin-right: 5rem;
  }

  .item__text.text-with-gliph::after {
    padding-left: 5rem;
  }
}

.item__image {
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: 4rem;
  height: 4rem;
}

@media only screen and (min-width: 768px) {
  .item__image {
    width: 7rem;
    height: 7rem;
  }
}

@media only screen and (min-width: 1200px) {
  .item__image {
    width: 8rem;
    height: 8rem;
  }
}

@media only screen and (min-width: 1600px) {
  .item__image {
    width: 11rem;
    height: 11rem;
  }
}

.item__image img,
.item__image video {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
  object-fit: cover;
}

.item__text {
  font: normal 300 4.6rem var(--_font-accent);
  color: var(--t-bright);
}

@media only screen and (min-width: 768px) {
  .item__text {
    font-size: 8rem;
  }
}

@media only screen and (min-width: 1200px) {
  .item__text {
    font-size: 9rem;
  }
}

@media only screen and (min-width: 1400px) {
  .item__text {
    font-size: 10rem;
  }
}

@media only screen and (min-width: 1600px) {
  .item__text {
    font-size: 14rem;
  }
}

@-webkit-keyframes toLeft {
  to {
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
  }
}

@-moz-keyframes toLeft {
  to {
    -moz-transform: translateX(-50%);
    transform: translateX(-50%);
  }
}

@keyframes toLeft {
  to {
    -webkit-transform: translateX(-50%);
    -moz-transform: translateX(-50%);
    transform: translateX(-50%);
  }
}

@-webkit-keyframes toRight {
  to {
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
}

@-moz-keyframes toRight {
  to {
    -moz-transform: translateX(0);
    transform: translateX(0);
  }
}

@keyframes toRight {
  to {
    -webkit-transform: translateX(0);
    -moz-transform: translateX(0);
    transform: translateX(0);
  }
}

@-webkit-keyframes animateZ {
  to {
    -webkit-transform: translateZ(1px);
    transform: translateZ(1px);
  }
}

@-moz-keyframes animateZ {
  to {
    -moz-transform: translateZ(1px);
    transform: translateZ(1px);
  }
}

@keyframes animateZ {
  to {
    -webkit-transform: translateZ(1px);
    -moz-transform: translateZ(1px);
    transform: translateZ(1px);
  }
}

/* ------------------------------------------------*/
/* Features Marquee End */
/* ------------------------------------------------*/
/* ------------------------------------------------*/
/* Features About Start */
/* ------------------------------------------------*/
.about-descr,
.about-info {
  padding-top: 2.8rem;
}

.about-descr.pre-grid,
.about-info.pre-grid {
  padding-top: 3.7rem;
}

.about-descr.pre-title,
.about-info.pre-title {
  padding-top: 2.5rem;
}

@media only screen and (min-width: 768px) {

  .about-descr,
  .about-info {
    padding-top: 0;
  }

  .about-descr.pre-title,
  .about-info.pre-title {
    padding-top: 0rem;
  }

  .about-descr.pre-grid,
  .about-info.pre-grid {
    padding-top: 4.7rem;
  }
}

@media only screen and (min-width: 768px) {

  .about-descr.pre-grid,
  .about-info.pre-grid {
    padding-top: 5.4rem;
  }
}

@media only screen and (min-width: 1200px) {

  .about-descr.pre-grid,
  .about-info.pre-grid {
    padding-top: 5.2rem;
  }
}

@media only screen and (min-width: 1600px) {

  .about-descr.pre-grid,
  .about-info.pre-grid {
    padding-top: 5.4rem;
  }
}

.about-descr__text {
  margin-bottom: 2.1rem;
}

.about-descr__text:last-of-type {
  margin-bottom: 0;
}

@media only screen and (min-width: 768px) {
  .about-descr__text {
    margin-bottom: 3rem;
  }
}

@media only screen and (min-width: 1200px) {
  .about-descr__text {
    margin-bottom: 2.8rem;
  }
}

@media only screen and (min-width: 1400px) {
  .about-descr__text {
    margin-bottom: 2.6rem;
  }
}

@media only screen and (min-width: 1600px) {
  .about-descr__text {
    margin-bottom: 3.2rem;
  }
}

.about-descr__btnholder {
  margin-top: 3.2rem;
}

@media only screen and (min-width: 768px) {
  .about-descr__btnholder {
    margin-top: 4.1rem;
  }
}

@media only screen and (min-width: 1400px) {
  .about-descr__btnholder {
    margin-top: 4rem;
  }
}

@media only screen and (min-width: 1600px) {
  .about-descr__btnholder {
    margin-top: 4.7rem;
  }
}

.about-info__item {
  margin-bottom: 2rem;
}

.about-info__item:last-of-type {
  margin-bottom: 0;
}

@media only screen and (min-width: 768px) {
  .about-info__item {
    margin-bottom: 3rem;
  }
}

@media only screen and (min-width: 1200px) {
  .about-info__item {
    margin-bottom: 2.8rem;
  }
}

@media only screen and (min-width: 1400px) {
  .about-info__item {
    margin-bottom: 2.6rem;
  }
}

@media only screen and (min-width: 1600px) {
  .about-info__item {
    margin-bottom: 3.2rem;
  }
}

/* ------------------------------------------------*/
/* Features About End */
/* ------------------------------------------------*/
/* ------------------------------------------------*/
/* Features Gallery Start */
/* ------------------------------------------------*/
.inner__gallery {
  position: relative;
}

.gallery__btn {
  position: relative;
  width: 100%;
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
  -moz-box-pack: end;
  -ms-flex-pack: end;
  justify-content: end;
  padding: 0 1rem;
  margin-top: 2rem;
}

@media only screen and (min-width: 768px) {
  .gallery__btn {
    position: absolute;
    right: 0;
    bottom: 0;
    width: auto;
    padding: 0 1.5rem;
    margin-top: 0;
  }
}

@media only screen and (min-width: 1600px) {
  .gallery__btn {
    padding: 0 2.5rem;
  }
}

.gallery__item {
  position: relative;
  cursor: pointer;
}

.gallery__link {
  display: block;
  position: relative;
  overflow: hidden;
  -moz-border-radius: var(--_radius);
  border-radius: var(--_radius);
}

.gallery__link .picture {
  position: absolute;
  left: 50%;
  top: 50%;
  bottom: auto;
  right: auto;
  -webkit-transform: translateX(-50%) translateY(-50%);
  -moz-transform: translateX(-50%) translateY(-50%);
  -ms-transform: translateX(-50%) translateY(-50%);
  transform: translateX(-50%) translateY(-50%);
  width: 100%;  /* Alterado de 112% para 100% */
  height: 100%;  /* Alterado de 112% para 100% */
  background-repeat: no-repeat;
  background-position: center center;
  -moz-background-size: cover;
  background-size: cover;
  -webkit-transition: all 0.5s ease-in-out;
  -moz-transition: all 0.5s ease-in-out;
  transition: all 0.5s ease-in-out;
}

@media only screen and (min-width: 1200px) {
  .gallery__link {
    -webkit-transform: scale3d(1, 1, 1);
    -moz-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
    -webkit-transition: -webkit-transform 0.3s var(--_animbezier);
    transition: -webkit-transform 0.3s var(--_animbezier);
    -moz-transition: transform 0.3s var(--_animbezier), -moz-transform 0.3s var(--_animbezier);
    transition: transform 0.3s var(--_animbezier);
    transition: transform 0.3s var(--_animbezier), -webkit-transform 0.3s var(--_animbezier), -moz-transform 0.3s var(--_animbezier);
  }

  .no-touch .gallery__link:hover .picture {
    width: 112%;  /* Aumenta para 112% no hover */
    height: 112%;  /* Aumenta para 112% no hover */
  }
}

.gallery__image {
  opacity: 1;
  display: block;
  width: 100%;
  height: auto;
  object-fit: cover;
}

.gallery__descr {
  position: absolute;
  bottom: 0;
  left: 0.75rem;
  width: -moz-calc(100% - 1.5rem);
  width: calc(100% - 1.5rem);
  padding: 1.875rem;
}

.gallery__descr h5,
.gallery__descr p {
  display: none;
}

@media only screen and (min-width: 1200px) {
  .gallery__descr {
    position: absolute;
    bottom: 0;
    left: 1.25rem;
    width: -moz-calc(100% - 2.5rem);
    width: calc(100% - 2.5rem);
  }
}

.pswp__bg {
  background: var(--per-base);
}

.pswp__img {
  object-fit: contain;
  /* Garante que a imagem não seja distorcida */
  max-width: auto;
  /* Impede que ultrapasse os limites horizontais */
  max-height: auto;
  /* Impede que ultrapasse os limites verticais */

}

.pswp__top-bar,
.pswp__ui--fit .pswp__top-bar {
  height: 74px;
  padding: 1.5rem;
  background-color: transparent;
}

.pswp__counter {
  left: 1.5rem;
  top: 1.5rem;
  padding: 0 1.4rem;
  -moz-border-radius: var(--_radius-s);
  border-radius: var(--_radius-s);
  background-color: rgba(0, 0, 0, 0.6);
  font: normal 400 1.8rem/44px var(--_font-default);
  font-variant-numeric: lining-nums;
  color: var(--t-per-bright-light);
  opacity: 1;
}

.pswp__counter:active {
  opacity: 1;
}

.pswp__caption,
.pswp__ui--fit .pswp__caption {
  padding: 1.5rem;
  background-color: transparent;
}

.pswp__caption__center {
  -moz-border-radius: var(--_radius);
  border-radius: var(--_radius);
  padding: 2.6rem 3rem;
  background-color: rgba(0, 0, 0, 0.6);
}

.pswp__caption__center h5 {
  font: normal 500 2.4rem var(--_font-accent);
  color: var(--t-per-bright-light);
  margin-bottom: 1.8rem;
  letter-spacing: normal;
}

.pswp__caption__center h5 small {
  display: block;
  font: normal 400 1.6rem var(--_font-default);
  color: var(--t-per-medium-light);
  margin-top: 0.4rem;
}

.pswp__caption__center p {
  font: normal 300 1.8rem/1.4 var(--_font-default);
  color: var(--t-per-bright-light);
}

@media only screen and (min-width: 1200px) {
  .pswp__caption__center {
    max-width: 600px;
  }
}

.pswp__button {
  background-color: var(--per-base);
  border: 1px solid var(--per-stroke-elements);
  -moz-border-radius: var(--_radius-s);
  border-radius: var(--_radius-s);
  opacity: 1;
}

.pswp__button:active {
  opacity: 1;
}

.pswp__button--zoom,
.pswp__button--fs,
.pswp__button--share {
  margin-right: 1rem;
}

.pswp__button--close {
  position: relative;
  background-color: var(--neutral-white);
  border: none;
  opacity: 1;
}

.pswp__button--arrow--left,
.pswp__button--arrow--right {
  width: 74px;
  opacity: 1;
  border: none;
  background-color: transparent;
}

.pswp__button--arrow--left::before,
.pswp__button--arrow--right::before {
  top: 28px;
  width: 44px;
  height: 44px;
  line-height: 44px;
  -moz-border-radius: var(--_radius-s);
  border-radius: var(--_radius-s);
  background-color: var(--neutral-white);
  color: var(--t-per-bright-dark);
  font-size: 1.6rem;
}

.pswp__button--arrow--left:before {
  left: 1.5rem;
  background-position: -132px -44px;
}

.pswp__button--arrow--right:before {
  right: 1.5rem;
  background-position: -88px -44px;
}

/* ------------------------------------------------*/
/* Features Gallery End */
/* ------------------------------------------------*/
/* ------------------------------------------------*/
/* Features Achievements Start */
/* ------------------------------------------------*/
.numblock {
  padding: 1rem 1.25rem;
  margin-right: 1rem;
  border: 1px solid var(--stroke-elements);
  background-color: var(--base);
  -moz-border-radius: var(--_radius);
  border-radius: var(--_radius);
}

.numblock:last-of-type {
  margin-right: 0;
}

.numblock__number {
  display: block;
  font: normal 700 2.25rem/1 var(--_font-accent);
  font-variant-numeric: lining-nums;
  color: var(--t-bright);
}

.numblock__descr {
  font: normal 700 -moz-calc(var(--_size) * 0.938)/1.2 var(--_font-default);
  font: normal 700 calc(var(--_size) * 0.938)/1.2 var(--_font-default);
  color: var(--t-muted);
  margin-top: 0.375rem;
}

@media only screen and (min-width: 768px) {
  .numblock__descr {
    margin-top: 0;
    margin-left: 0.375rem;
  }
}

.achievements__item {
  text-align: left;
  width: 100%;
}

@media only screen and (min-width: 768px) {
  .achievements__item {
    text-align: left;
    width: 33.33333334%;
  }
}

.achievements__card {
  height: 100%;
  padding: 3rem;
  background-color: var(--base);
  border: 1px solid var(--stroke-elements);
  -moz-border-radius: var(--_radius);
  border-radius: var(--_radius);
  text-align: center;
}

@media only screen and (min-width: 1600px) {
  .achievements__card {
    padding: 3rem 1rem;
  }
}

.achievements__number {
  font: normal 300 4.6rem/0.8 var(--_font-accent);
  font-variant-numeric: lining-nums;
  color: var(--t-bright);
}

@media only screen and (min-width: 768px) {
  .achievements__number {
    font-size: 8rem;
  }
}

@media only screen and (min-width: 1200px) {
  .achievements__number {
    font-size: 9rem;
  }
}

@media only screen and (min-width: 1400px) {
  .achievements__number {
    font-size: 10rem;
  }
}

@media only screen and (min-width: 1600px) {
  .achievements__number {
    font-size: 12rem;
  }
}

.achievements__descr {
  font: normal 400 1.8rem var(--_font-default);
  color: var(--t-bright);
  margin-top: 1.5rem;
}

@media only screen and (min-width: 768px) {
  .achievements__descr {
    font-size: 2rem;
  }
}

@media only screen and (min-width: 1400px) {
  .achievements__descr {
    font-size: 2.2rem;
  }
}

@media only screen and (min-width: 1600px) {
  .achievements__descr {
    font-size: 2.6rem;
  }
}


/* ------------------------------------------------*/
/* Features Achievements End */
/* ------------------------------------------------*/
/* ------------------------------------------------*/
/* Features Stacking Cards Start */
/* ------------------------------------------------*/
.stack-wrapper {
  position: relative;
  width: 100%;
}

.stack-offset {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  height: 8rem;
  background-color: var(--base);
}

.stack-offset.solid-block {
  height: 8rem;
}

@media only screen and (min-width: 768px) {
  .stack-offset {
    height: 10rem;
  }

  .stack-offset.solid-block {
    height: 10rem;
  }
}

@media only screen and (min-width: 992px) {
  .stack-offset {
    height: 10rem;
  }

  .stack-offset.solid-block {
    height: 10rem;
  }
}

@media only screen and (min-width: 1200px) {
  .stack-offset.solid-block {
    height: 10rem;
  }
}

@media only screen and (min-width: 1400px) {
  .stack-offset {
    height: 11rem;
  }

  .stack-offset.solid-block {
    height: 11rem;
  }
}

@media only screen and (min-width: 1600px) {
  .stack-offset.solid-block {
    height: 11rem;
  }
}

.services-stack {
  width: 100%;
  position: relative;
  height: 65vh;
  min-height: 615px;
}

@media only screen and (min-width: 768px) {
  .services-stack {
    height: 500px;
    min-height: 0;
  }
}

@media only screen and (min-width: 1400px) {
  .services-stack {
    height: 600px;
  }
}

@media only screen and (min-width: 1600px) {
  .services-stack {
    height: 700px;
  }
}

.portfolio-stack {
  width: 100%;
  position: relative;
  height: 460px;
}

@media only screen and (min-width: 768px) {
  .portfolio-stack {
    height: 530px;
  }
}

@media only screen and (min-width: 1400px) {
  .portfolio-stack {
    height: 630px;
  }
}

@media only screen and (min-width: 1600px) {
  .portfolio-stack {
    height: 750px;
  }
}

.stack-item {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: flex;
  position: absolute;
  width: 100%;
  height: 100%;
  padding-bottom: 2rem;
}

@media only screen and (min-width: 768px) {
  .stack-item {
    padding-bottom: 3rem;
  }
}

@media only screen and (min-width: 1600px) {
  .stack-item {
    padding-bottom: 5rem;
  }
}

.services-stack__inner {
  position: relative;
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -moz-box-orient: vertical;
  -moz-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  overflow: hidden;
  width: 100%;
  height: 100%;
  background-color: var(--base);
  border: 1px solid var(--stroke-elements);
  -moz-border-radius: var(--_radius);
  border-radius: var(--_radius);
}

.services-stack__title {
  padding: 2.6rem 3rem 0 3rem;
  margin-bottom: 2.3rem;
}

@media only screen and (min-width: 768px) {
  .services-stack__title {
    position: absolute;
    top: 0;
    left: 0;
    max-width: 60%;
    margin-bottom: 0;
  }
}

@media only screen and (min-width: 1400px) {
  .services-stack__title {
    padding: 4rem 5rem 4.6rem 5rem;
  }
}

.services-stack__descr {
  padding: 0 3rem;
  margin-bottom: 2.3rem;
}

.services-stack__descr i {
  display: inline-block;
  font-size: 5.4rem;
  color: var(--t-bright);
  margin-bottom: 2.3rem;
  margin-left: -0.2rem;
}

@media only screen and (min-width: 768px) {
  .services-stack__descr {
    position: absolute;
    bottom: 0;
    left: 0;
    max-width: 50%;
    margin-bottom: 0;
    padding: 2.6rem 3rem;
  }
}

@media only screen and (min-width: 1400px) {
  .services-stack__descr {
    padding: 4rem 5rem 4.6rem 5rem;
  }
}

.services-stack__image {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: flex;
  padding-left: 0rem;
  position: relative;
  overflow: hidden;
  -webkit-box-flex: 1;
  -moz-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.services-stack__image .service-img {
  max-width: 100%;
  max-height: 100%;
  -o-object-fit: cover;
  object-fit: cover;
}

.services-stack__image .service-img-m {
  display: none;
}

@media only screen and (min-width: 768px) {
  .services-stack__image {
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    padding-left: 0;
  }

  .services-stack__image .service-img {
    position: absolute;
    right: -10%;
    bottom: 0;
    height: 100%;
    width: auto;
  }

  .services-stack__image .service-img-s {
    display: none;
  }

  .services-stack__image .service-img-m {
    display: block;
  }
}

@media only screen and (min-width: 992px) {
  .services-stack__image .service-img {
    position: absolute;
    right: 0;
    bottom: 0;
  }
}

.portfolio-stack__inner {
  position: relative;
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -moz-box-orient: vertical;
  -moz-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  height: 100%;
  background-color: var(--base);
  -moz-border-radius: var(--_radius);
  border-radius: var(--_radius);
  overflow: hidden;
  cursor: pointer;
  background-repeat: no-repeat;
  background-position: center center;
  -moz-background-size: cover;
  background-size: cover;
}

.portfolio-stack__descr {
  display: none;
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 2.4rem 3rem;
}

@media only screen and (min-width: 768px) {
  .portfolio-stack__descr {
    display: block;
    width: 60%;
    padding: 2.6rem 3rem;
  }
}

@media only screen and (min-width: 1200px) {
  .portfolio-stack__descr {
    width: 40%;
    padding: 4rem 5rem 4.6rem 5rem;
  }
}

@media only screen and (min-width: 1600px) {
  .portfolio-stack__descr {
    width: 33.3334%;
  }
}

.portfolio-stack__title {
  color: var(--t-per-bright-light);
}

.portfolio-stack__title.dark {
  color: var(--t-per-bright-dark);
  font-weight: 400;
}

.portfolio-stack__title.light {
  color: var(--neutral-white);
  font-weight: 300;
}

.portfolio-stack__text {
  margin-top: 3rem;
  font-weight: 300;
  color: var(--t-per-bright-light);
}

.portfolio-stack__text.dark {
  font-weight: 400;
  color: var(--t-per-bright-dark);
}

@media only screen and (min-width: 1400px) {
  .portfolio-stack__text {
    margin-top: 4rem;
  }
}

/* ------------------------------------------------*/
/* Features Stacking Cards End */
/* ------------------------------------------------*/
/* ------------------------------------------------*/
/* Features Services Start */
/* ------------------------------------------------*/
.services {
  position: relative;
}

.services__item {
  position: relative;
  overflow: hidden;
}

@media only screen and (min-width: 768px) {
  .services__item {
    height: 500px;
  }
}

@media only screen and (min-width: 1400px) {
  .services__item {
    height: 600px;
  }
}

@media only screen and (min-width: 1600px) {
  .services__item {
    height: 700px;
  }
}

.services__card {
  position: relative;
  height: 100%;
  background-color: var(--base);
  border: 1px solid var(--stroke-elements);
  -moz-border-radius: var(--_radius);
  border-radius: var(--_radius);
  overflow: hidden;
}

.services__title {
  padding: 2.6rem 3rem 0 3rem;
  margin-bottom: 2.3rem;
}

@media only screen and (min-width: 768px) {
  .services__title {
    position: absolute;
    top: 0;
    left: 0;
    max-width: 60%;
    z-index: 2;
    margin-bottom: 0;
  }
}

@media only screen and (min-width: 1400px) {
  .services__title {
    padding: 4rem 5rem 4.6rem 5rem;
  }
}

.services__descr {
  padding: 0 3rem;
  margin-bottom: 2.3rem;
}

.services__descr i {
  display: inline-block;
  font-size: 5.4rem;
  color: var(--t-bright);
  margin-bottom: 2.3rem;
  margin-left: -0.2rem;
}

@media only screen and (min-width: 768px) {
  .services__descr {
    position: absolute;
    bottom: 0;
    left: 0;
    max-width: 50%;
    z-index: 2;
    margin-bottom: 0;
    padding: 2.6rem 3rem;
  }
}

@media only screen and (min-width: 1400px) {
  .services__descr {
    padding: 4rem 5rem 4.6rem 5rem;
  }
}

@media only screen and (min-width: 768px) {
  .services__text {
    margin-bottom: 0;
  }
}

.services__image {
  padding-left: 3rem;
  position: relative;
  overflow: hidden;
  -webkit-box-flex: 1;
  -moz-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.services__image .service-img {
  max-width: 100%;
  max-height: 100%;
  -o-object-fit: cover;
  object-fit: cover;
}

.services__image .service-img-m {
  display: none;
}

@media only screen and (min-width: 768px) {
  .services__image {
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    padding-left: 0;
  }

  .services__image .service-img {
    position: absolute;
    right: -10%;
    bottom: 0;
    height: 100%;
    width: auto;
  }

  .services__image .service-img-s {
    display: none;
  }

  .services__image .service-img-m {
    display: block;
  }
}

@media only screen and (min-width: 992px) {
  .services__image .service-img {
    position: absolute;
    right: 0;
    bottom: 0;
  }
}

.cards {
  position: relative;
}

.cards__item {
  position: relative;
  overflow: hidden;
}

.cards__card {
  height: 100%;
  background-color: var(--base);
  border: 1px solid var(--stroke-elements);
  -moz-border-radius: var(--_radius);
  border-radius: var(--_radius);
  overflow: hidden;
}

.cards__descr {
  padding: 2.6rem 3rem;
}

@media only screen and (min-width: 1400px) {
  .cards__descr {
    padding: 4rem 5rem 4.6rem 5rem;
  }
}

.cards__text {
  margin-top: 3rem;
}

@media only screen and (min-width: 1400px) {
  .cards__text {
    margin-top: 4rem;
  }
}

.cards__image {
  position: relative;
  overflow: hidden;
  -webkit-box-flex: 1;
  -moz-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  -moz-border-radius-topleft: var(--_radius);
  border-top-left-radius: var(--_radius);
  -moz-border-radius-topright: var(--_radius);
  border-top-right-radius: var(--_radius);
}

.cards__image img {
  -o-object-fit: cover;
  object-fit: cover;
  max-width: 100%;
  max-height: 100%;
}

.cards__image-v2 {
  padding-left: 3rem;
}

@media only screen and (min-width: 1400px) {
  .cards__image-v2 {
    padding-left: 5rem;
  }
}

/* ------------------------------------------------*/
/* Features Services End */
/* ------------------------------------------------*/
/* ------------------------------------------------*/
/* Features Resume Start */
/* ------------------------------------------------*/
.resume-lines,
.resume-lines-number {
  position: relative;
}

.resume-lines__item,
.resume-lines-number__item {
  padding: 4.3rem 0 4.1rem 0;
}

@media only screen and (min-width: 1600px) {

  .resume-lines__item,
  .resume-lines-number__item {
    padding: 5rem 0 4.8rem 0;
  }
}

.resume-divider {
  position: relative;
  width: 100%;
  height: 1px;
  background-color: var(--stroke-elements);
}

.resume-lines__date {
  margin-bottom: 1.9rem;
}

@media only screen and (min-width: 768px) {
  .resume-lines__date {
    margin-bottom: 0;
  }
}

.resume-lines-number__date {
  margin-top: 2.6rem;
}

@media only screen and (min-width: 768px) {
  .resume-lines-number__date {
    margin-top: 0;
  }
}

.resume-lines__title,
.resume-lines__source {
  padding-right: 3rem;
}

@media only screen and (min-width: 1600px) {

  .resume-lines__title,
  .resume-lines__source {
    padding-right: 5rem;
  }
}

@media only screen and (min-width: 768px) {

  .resume-lines-number__title,
  .resume-lines-number__source {
    padding-right: 3rem;
  }
}

@media only screen and (min-width: 1600px) {

  .resume-lines-number__title,
  .resume-lines-number__source {
    padding-right: 5rem;
  }
}

.resume-lines__title {
  margin-bottom: 0.6rem;
}

.resume-lines__source,
.resume-lines-number__source {
  display: inline-block;
}

.resume-lines__descr {
  list-style: none;
  padding: 0;
  margin: 2rem 0 0 0;  /* Added top margin for mobile */
  will-change: transform, opacity;
}

@media only screen and (min-width: 768px) {
  .resume-lines__descr {
    margin-top: 0;  /* Remove extra margin for desktop */
  }
}

.resume-lines__descr li {
  position: relative;
  padding-left: 1.8rem;
  margin-bottom: 0.8rem;
  line-height: 1.6;
}

.resume-lines__descr li::before {
  content: "";
  position: absolute;
  left: 0.4rem;
  top: 1rem;  /* Moved down from 0.8rem */
  width: 0.6rem;
  height: 0.6rem;
  background-color: var(--t-bright);
  border-radius: 50%;
}

.resume-lines__descr li li {
  padding-left: 2rem;
  margin-top: 0.6rem;
  margin-bottom: 0.6rem;
}

.resume-lines__descr li li::before {
  content: "";
  position: absolute;
  left: 0.4rem;
  top: 1.2rem;  /* Moved down from 1rem */
  width: 1rem;
  height: 2px;
  background-color: var(--t-medium);
}

.resume-lines__descr li li::after {
  display: none;
}

.project__descr ul {
  list-style: none;
  padding-left: 0;
}

.project__descr li {
  position: relative;
  padding-left: 2rem;
  margin-bottom: 0.8rem;
}

.project__descr li::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0.5rem;
  width: 0.8rem;
  height: 0.8rem;
  background-color: var(--t-bright);
  border-radius: 50%;
}

.project__descr li li {
  padding-left: 2.5rem;
}

.project__descr li li::before {
  width: 0.6rem;
  height: 0.6rem;
  left: 1rem;
}

.project__descr li li::after {
  content: "";
  position: absolute;
  left: 0.4rem;
  top: 0;
  height: 100%;
  width: 1px;
  background-color: var(--t-medium);
}

/* ------------------------------------------------*/
/* Features Resume End */
/* ------------------------------------------------*/
/* ------------------------------------------------*/
/* Features Tools Start */
/* ------------------------------------------------*/
.tools-cards__item {
  width: 50%;
}

@media only screen and (min-width: 768px) {
  .tools-cards__item {
    width: 33.33333334%;
  }
}

@media only screen and (min-width: 992px) {
  .tools-cards__item {
    width: 25%;
  }
}

.tools-cards__card {
  position: relative;
  width: 100%;
  padding: 3rem;
  background-color: var(--base);
  border: 1px solid var(--stroke-elements);
  -moz-border-radius: var(--_radius);
  border-radius: var(--_radius);
  text-align: center;
}

@media only screen and (min-width: 1600px) {
  .tools-cards__card {
    padding: 5rem 3rem;
  }
}

.tools-cards__icon {
  width: auto;
  height: 6rem;
  margin: 0 auto;
}

@media only screen and (min-width: 1400px) {
  .tools-cards__icon {
    height: 7rem;
  }
}

@media only screen and (min-width: 1600px) {
  .tools-cards__icon {
    height: 9rem;
  }
}

.tools-cards__caption {
  line-height: 1;
  margin-top: 1.5rem;
}

.tools-card {
  padding: 3rem;
  background-color: var(--base);
  border: 1px solid var(--stroke-elements);
  -moz-border-radius: var(--_radius-xl);
  border-radius: var(--_radius-xl);
  overflow: hidden;
}

.tools-card__icon {
  width: auto;
  height: 60px;
  margin: 0 auto;
}

@media only screen and (min-width: 768px) {
  .tools-card__icon {
    height: 90px;
  }
}

.tools-card__caption {
  line-height: 1;
  margin-top: 1.5rem;
}

/* ------------------------------------------------*/
/* Features Tools End */
/* ------------------------------------------------*/
/* ------------------------------------------------*/
/* Features Accordion Start */
/* ------------------------------------------------*/
.accordion-card {
  position: relative;
  width: 100%;
  padding: 3rem;
  background-color: var(--base);
  border: 1px solid var(--stroke-elements);
  -moz-border-radius: var(--_radius);
  border-radius: var(--_radius);
  overflow: hidden;
}

@media only screen and (min-width: 1600px) {
  .accordion-card {
    padding: 5rem 3rem;
  }
}

.accordion-card__title {
  margin-bottom: 2.5rem;
}

@media only screen and (min-width: 768px) {
  .accordion-card__title {
    margin-bottom: 3.5rem;
  }
}

@media only screen and (min-width: 1400px) {
  .accordion-card__title {
    margin-bottom: 3.4rem;
  }
}

@media only screen and (min-width: 1600px) {
  .accordion-card__title {
    margin-bottom: 4.2rem;
  }
}

.accordion-card__item {
  position: relative;
  width: 100%;
  margin-bottom: 2rem;
}

.accordion-card__item:last-of-type {
  margin-bottom: 0;
}

.accordion-card__item-title {
  position: relative;
  display: block;
  width: 100%;
  padding: 2rem 0;
  background-color: var(--base);
  border: 1px solid var(--stroke-elements);
  -moz-border-radius: var(--_radius);
  border-radius: var(--_radius);
  color: var(--t-bright);
  -webkit-transition: all 0.3s ease-in-out;
  -moz-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}

.accordion-card__item-title::before {
  content: "";
  display: block;
  position: absolute;
  top: 50%;
  right: 2rem;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
  width: 1.2rem;
  height: 1.2rem;
  background-color: var(--t-bright);
  -moz-border-radius: 50%;
  border-radius: 50%;
  -webkit-transition: all 0.3s ease-in-out;
  -moz-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}

.accordion-card__item-title::after {
  content: "";
  display: block;
  position: absolute;
  top: 50%;
  right: 2rem;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
  width: 1.2rem;
  height: 1.2rem;
  background-color: var(--t-bright);
  -moz-border-radius: 50%;
  border-radius: 50%;
  -webkit-transition: all 0.3s ease-in-out;
  -moz-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}

.accordion-card__item-title h4 {
  font-size: 1.8rem;
  font-weight: 400;
  line-height: 1;
}

@media only screen and (min-width: 768px) {
  .accordion-card__item-title h4 {
    font-size: 2rem;
  }
}

@media only screen and (min-width: 1400px) {
  .accordion-card__item-title h4 {
    font-size: 2.2rem;
  }
}

@media only screen and (min-width: 1600px) {
  .accordion-card__item-title h4 {
    font-size: 2.6rem;
  }
}

.accordion-card__item-title.collapsed {
  background-color: var(--t-bright);
  color: var(--t-opp-bright);
}

.accordion-card__item-title.collapsed::before {
  background-color: var(--t-opp-bright);
}

.accordion-card__item-title.collapsed::after {
  background-color: var(--t-opp-bright);
  -webkit-transform: translateY(-50%) rotate(45deg);
  -moz-transform: translateY(-50%) rotate(45deg);
  -ms-transform: translateY(-50%) rotate(45deg);
  transform: translateY(-50%) rotate(45deg);
}

.accordion-card__item-content {
  position: relative;
  width: 100%;
  padding: 2rem 0;
  background-color: var(--base);
  border: 1px solid var(--stroke-elements);
  -moz-border-radius: var(--_radius);
  border-radius: var(--_radius);
  color: var(--t-bright);
  -webkit-transition: all 0.3s ease-in-out;
  -moz-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}

.accordion-card__item-content p {
  margin-bottom: 0;
}

.accordion-card__item-content.collapsing {
  background-color: var(--t-bright);
  color: var(--t-opp-bright);
}

/* ------------------------------------------------*/
/* Features Accordion End */
/* ------------------------------------------------*/
/* ------------------------------------------------*/
/* Features Socials Start */
/* ------------------------------------------------*/
/* Socials Cards */
.socials-cards {
  position: relative;
}

.socials-cards__item {
  width: 50%;
}

@media only screen and (min-width: 768px) {
  .socials-cards__item {
    width: 33.33333334%;
  }
}

@media only screen and (min-width: 992px) {
  .socials-cards__item {
    width: 20%;
  }
}

.socials-cards__link {
  position: relative;
  width: 100%;
  height: auto;
  padding: 2.6rem 0;
  background-color: var(--base);
  border: 1px solid var(--stroke-elements);
  -moz-border-radius: var(--_radius);
  border-radius: var(--_radius);
  overflow: hidden;
}

.socials-cards__link em {
  position: absolute;
  display: block;
  width: 0;
  height: 0;
  -moz-border-radius: 50%;
  border-radius: 50%;
  background-color: var(--t-bright);
  -webkit-transition: width 0.3s ease-in-out, height 0.3s ease-in-out;
  -moz-transition: width 0.3s ease-in-out, height 0.3s ease-in-out;
  transition: width 0.3s ease-in-out, height 0.3s ease-in-out;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

.socials-cards__link i {
  position: relative;
  display: block;
  font-size: 7rem;
  line-height: 1;
  color: var(--t-bright);
  -webkit-transform: scale3d(1, 1, 1);
  -moz-transform: scale3d(1, 1, 1);
  transform: scale3d(1, 1, 1);
  -webkit-transition: color 0.3s 0s, -webkit-transform 0.3s 0.1s ease-in-out;
  transition: color 0.3s 0s, -webkit-transform 0.3s 0.1s ease-in-out;
  -moz-transition: color 0.3s 0s, transform 0.3s 0.1s ease-in-out, -moz-transform 0.3s 0.1s ease-in-out;
  transition: color 0.3s 0s, transform 0.3s 0.1s ease-in-out;
  transition: color 0.3s 0s, transform 0.3s 0.1s ease-in-out, -webkit-transform 0.3s 0.1s ease-in-out, -moz-transform 0.3s 0.1s ease-in-out;
  will-change: transform;
}

.no-touch .socials-cards__link:hover {
  color: var(--t-opp-bright);
  background-color: var(--t-bright);
  -webkit-transition: color 0.3s 0s, background-color 0.3s 0.24s, -webkit-box-shadow 0.5s, -webkit-transform 0.3s ease-in-out;
  transition: color 0.3s 0s, background-color 0.3s 0.24s, -webkit-box-shadow 0.5s, -webkit-transform 0.3s ease-in-out;
  -moz-transition: color 0.3s 0s, background-color 0.3s 0.24s, box-shadow 0.5s, transform 0.3s ease-in-out, -moz-box-shadow 0.5s, -moz-transform 0.3s ease-in-out;
  transition: color 0.3s 0s, background-color 0.3s 0.24s, box-shadow 0.5s, transform 0.3s ease-in-out;
  transition: color 0.3s 0s, background-color 0.3s 0.24s, box-shadow 0.5s, transform 0.3s ease-in-out, -webkit-box-shadow 0.5s, -moz-box-shadow 0.5s, -webkit-transform 0.3s ease-in-out, -moz-transform 0.3s ease-in-out;
}

.no-touch .socials-cards__link:hover i {
  color: var(--t-opp-bright);
  -webkit-transform: scale3d(0.96, 0.96, 1);
  -moz-transform: scale3d(0.96, 0.96, 1);
  transform: scale3d(0.96, 0.96, 1);
}

.no-touch .socials-cards__link:hover em {
  width: 225%;
  height: 40.95rem;
}

@media only screen and (min-width: 1400px) {
  .socials-cards__link i {
    font-size: 8rem;
  }
}

@media only screen and (min-width: 1600px) {
  .socials-cards__link {
    padding: 4rem 0;
  }

  .socials-cards__link i {
    font-size: 10rem;
  }
}

/* Socials Lines */
.socials-lines {
  position: relative;
}

.socials-lines__item {
  position: relative;
  display: block;
}

.socials-lines__divider {
  position: relative;
  width: 100%;
  height: 1px;
  background-color: var(--stroke-elements);
}

.socials-lines__link {
  position: relative;
  padding: 3rem 0.4rem;
  color: var(--t-bright);
  overflow: hidden;
}

.socials-lines__link::before {
  content: "";
  display: block;
  position: absolute;
  left: 0;
  top: 50%;
  bottom: auto;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
  width: 100%;
  height: 0;
  background: var(--t-bright);
  -webkit-transition: height var(--_animspeed-medium) var(--_animbezier);
  -moz-transition: height var(--_animspeed-medium) var(--_animbezier);
  transition: height var(--_animspeed-medium) var(--_animbezier);
}

.socials-lines__link h4,
.socials-lines__link i {
  color: var(--t-bright);
  -webkit-transition: all var(--_animspeed-medium) var(--_animbezier);
  -moz-transition: all var(--_animspeed-medium) var(--_animbezier);
  transition: all var(--_animspeed-medium) var(--_animbezier);
}

@media only screen and (min-width: 768px) {
  .socials-lines__link {
    padding: 5rem 0.4rem;
  }
}

@media only screen and (min-width: 1200px) {
  .no-touch .socials-lines__link:hover::before {
    height: 100%;
  }

  .no-touch .socials-lines__link:hover h4 {
    -webkit-transform: translate(5rem, 0px) !important;
    -moz-transform: translate(5rem, 0px) !important;
    -ms-transform: translate(5rem, 0px) !important;
    transform: translate(5rem, 0px) !important;
  }

  .no-touch .socials-lines__link:hover i {
    -webkit-transform: rotate(45deg);
    -moz-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    transform: rotate(45deg);
  }

  .no-touch .socials-lines__link:hover h4,
  .no-touch .socials-lines__link:hover i {
    color: var(--t-opp-bright);
  }
}

.socials-lines__icon {
  font-size: 3rem;
}

@media only screen and (min-width: 1400px) {
  .socials-lines__icon {
    font-size: 3.6rem;
  }
}

@media only screen and (min-width: 1600px) {
  .socials-lines__icon {
    font-size: 4rem;
  }
}

/* ------------------------------------------------*/
/* Features Socials End */
/* ------------------------------------------------*/
/* ------------------------------------------------*/
/* Features Contact Data Start */
/* ------------------------------------------------*/
.contact-data__item {
  margin-bottom: 3.2rem;
}

.contact-data__item:last-of-type {
  margin-bottom: 0;
}

@media only screen and (min-width: 768px) {
  .contact-data__item {
    margin-bottom: 4.3rem;
  }

  .contact-data__item:nth-of-type(3),
  .contact-data__item:last-of-type {
    margin-bottom: 0;
  }
}

@media only screen and (min-width: 992px) {
  .contact-data__item {
    margin-bottom: 0;
  }
}

.contact-data__title {
  margin-bottom: 2.5rem;
}

@media only screen and (min-width: 768px) {
  .contact-data__title {
    margin-bottom: 3.5rem;
  }
}

@media only screen and (min-width: 1400px) {
  .contact-data__title {
    margin-bottom: 3.4rem;
  }
}

@media only screen and (min-width: 1600px) {
  .contact-data__title {
    margin-bottom: 4.2rem;
  }
}


/* conteúdo personalizado */
.flex-container {
  --t-speed: 1.2s;
  --h-flex: 4;
  --min-flex: 0.5;
  width: 100%;
  height: min(1000px, 90vh);
  border-radius: 12px;
  position: relative;
  display: flex;
  flex-wrap: wrap;
}

.flex-container .flex-item {
  flex: 1 1 calc(20% - 10px);
  height: 100%;
  position: relative;
  transition: all var(--t-speed) cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.flex-container .flex-item:not(:hover) {
  flex: var(--min-flex);
}

.flex-container .flex-item:hover,
.flex-container .flex-item:focus {
  flex: var(--h-flex);
  z-index: 1;
}

.flex-container .flex-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform var(--t-speed) ease-in-out;
}

.flex-container .flex-item:hover img {
  transform: scale(1.05);
}

.flex-container .flex-item:not(:first-child) {
  position: relative;
}

.flex-container .flex-item:not(:first-child)::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 15px;
  height: 100%;
  background: linear-gradient(to right, rgba(0, 0, 0, 1), transparent);
  z-index: 5;
  pointer-events: none;
}

.fitter {
  object-fit: cover;
  overflow: hidden;
  position: relative;
  z-index: 0;
}


/* Ajustes responsivos para ocultar colunas da direita */
@media (max-width: 1400px) {
  .flex-container .flex-item:nth-last-child(-n+1) {
    display: none;
    /* Ocultar a última coluna */
  }
}

@media (max-width: 1200px) {
  .flex-container .flex-item:nth-last-child(-n+2) {
    display: none;
    /* Ocultar as duas últimas colunas */
  }
}

@media (max-width: 900px) {
  .flex-container .flex-item:nth-last-child(-n+3) {
    display: none;
    /* Ocultar as três últimas colunas */
  }
}

/* Exibir apenas duas imagens entre 600px e 400px */
@media (max-width: 600px) {
  .flex-container .flex-item {
    display: none;
    /* Ocultar todas as colunas inicialmente */
  }

  .flex-container .flex-item:nth-child(-n+2) {
    display: flex;
    /* Mostrar as duas primeiras colunas */
  }
}

/* Exibir apenas uma imagem abaixo de 400px */
@media (max-width: 400px) {
  .flex-container .flex-item {
    display: none;
    /* Ocultar todas as colunas */
  }

  .flex-container .flex-item:nth-child(1) {
    display: flex;
    /* Mostrar apenas a primeira coluna */
  }

  /* Para garantir que a imagem não desapareça */
  .flex-item img {
    display: block;
    /* Mostrar a imagem */
    width: 100%;
    /* Ajustar a largura da imagem ao item */
    height: auto;
    /* Manter a proporção */
  }
}


/* ------------------------------------------------*/
/* Features Contact Data End */
/* ------------------------------------------------*/

/* Logo adjustments for smaller screens */
@media (max-width: 768px) {
  .logo {
    margin-right: 20px;
    /* Adjust as necessary */
  }

  .logo-text {
    font-size: 2.4rem;
    /* Adjust text size */
  }

  .header__navigation {
    margin-left: auto;
    /* Pushes the menu to the right */
  }
}

@media (max-width: 1400px) {
  .logo {
    margin-right: 20px;
    /* Adjust as necessary */
  }

  .logo-text {
    font-size: 1.7rem;
    /* Adjust text size */
  }

  .header__navigation {
    margin-left: auto;
    /* Pushes the menu to the right */
  }
}

/* Ensure box-sizing is set correctly */
*,
*::before,
*::after {
  box-sizing: border-box;
}

.container .row a {
  margin-bottom: 15px;
}

@media (max-width: 768px) {
  .container .row a {
    margin-bottom: 15px;
    /* Ajuste o valor conforme a necessidade */
  }
}

.container .row a img {
  transition: transform 0.3s ease;
}

.container .row a:hover img {
  transform: scale(1.05);
}

/* Container da galeria */
.nova-galeria {
  display: flex;
  flex-wrap: wrap;
  /* Permite quebrar linha */
  gap: 15px;
  /* Espaçamento entre imagens */
  margin-top: 6rem;
  justify-content: center;
  /* Centraliza as imagens quando houver sobra de espaço */
}

/* Configuração das figuras (thumbnails) */
.nova-galeria figure {
  flex: 1 1 auto;
  /* Ajusta dinamicamente o tamanho */
  max-width: var(--thumbnail-width, 275px);
  /* Controla o tamanho máximo pelo CSS */
  margin: 0;
  /* Remove margem padrão */
}

/* Imagens dentro das figuras */
.nova-galeria img {
  width: 100%;
  /* Ocupa toda a largura da figura */
  height: auto;
  /* Mantém a proporção original */
  border-radius: 8px;
  /* Borda arredondada */
  object-fit: cover;
  /* Corta a imagem para caber perfeitamente */
}

/* Responsividade: imagens empilhadas em telas pequenas */
@media (max-width: 768px) {
  .nova-galeria figure {
    flex: 1 1 100%;
    /* Cada figura ocupa 100% da largura */
  }
}

/* Container da galeria2 */
.nova-galeria2 {
  display: flex;
  flex-wrap: wrap;
  /* Permite quebrar linha */
  gap: 15px;
  /* Espaçamento entre imagens */
  margin-top: 6rem;
  justify-content: center;
  /* Centraliza as imagens quando houver sobra de espaço */
}

/* Configuração das figuras (thumbnails) */
.nova-galeria2 figure {
  flex: 1 1 auto;
  /* Ajusta dinamicamente o tamanho */
  max-width: var(--thumbnail-width, 175px);
  /* Controla o tamanho máximo pelo CSS */
  margin: 0;
  /* Remove margem padrão */
}

/* Imagens dentro das figuras */
.nova-galeria2 img {
  width: 100%;
  /* Ocupa toda a largura da figura */
  height: auto;
  /* Mantém a proporção original */
  border-radius: 8px;
  /* Borda arredondada */
  object-fit: cover;
  /* Corta a imagem para caber perfeitamente */
}

/* Responsividade: imagens empilhadas em telas pequenas */
@media (max-width: 768px) {
  .nova-galeria2 figure {
    flex: 1 1 100%;
    /* Cada figura ocupa 100% da largura */
  }
}

/* Estilo para o título "Meu Papel" */
.project__subtitle {
  font: normal var(--font-weight-medium) 2rem var(--_font-accent);
  color: var(--t-bright);
  margin-bottom: 1.5rem;
  /* Espaçamento para dar respiro abaixo do título */
}

/* Estilo para os parágrafos dentro da seção */
.project__descr p {
  font: normal var(--font-weight-base) 1.8rem var(--_font-default);
  color: var(--t-bright);
  line-height: 1.6;
}

/* Estilo para a lista de responsabilidades */
.project__descr ul {
  padding-left: 20px;
  font-size: 1.6rem;
  line-height: 2;
  color: var(--t-bright);
  list-style-type: disc;
  /* Garante que os marcadores apareçam */
}

.project__descr li {
  margin-bottom: 1.6rem;
  /* Espaço entre os itens da lista */
}

.project__descr strong {
  font-weight: 600;
  /* Destaque para as responsabilidades */
}

/* Ajuste para telas grandes */
@media only screen and (min-width: 1200px) {
  .project__subtitle {
    font-size: 2.4rem;
  }
}

.project__descr p {
  font-size: 2rem;
}

.project__descr ul {
  font-size: 1.8rem;
}

/* Estilo geral para os títulos dentro do Project Block */
.project__block h3,
.project__block .project__subtitle {
  font: normal var(--font-weight-medium) 2rem var(--_font-accent);
  color: var(--t-bright);
  margin-bottom: 1.5rem;
  /* Espaçamento abaixo dos títulos */
  font-weight: var(--font-weight-medium);
  /* Garantir que todos os títulos tenham o mesmo peso */
}

/* Estilo para os parágrafos dentro do Project Block */
.project__block p {
  font: normal var(--font-weight-base) 1.8rem var(--_font-default);
  color: var(--t-bright);
  line-height: 1.6;
  margin-bottom: 1.6rem;
  /* Espaçamento entre os parágrafos */
}

/* Estilo para a lista dentro do Project Block */
.project__block ul {
  padding-left: 20px;
  font-size: 1.6rem;
  line-height: 2;
  color: var(--t-bright);
  list-style-type: disc;
  /* Marcadores visíveis */
}

.project__block li {
  margin-bottom: 1.6rem;
  /* Espaçamento entre os itens da lista */
}

.project__block strong {
  font-weight: 600;
  /* Destaque para os itens importantes na lista */
}

/* Ajuste para telas grandes */
@media only screen and (min-width: 1200px) {

  .project__block h3,
  .project__block .project__subtitle {
    font-size: 2.4rem;
  }

  .project__block p {
    font-size: 2rem;
  }

  .project__block ul {
    font-size: 1.8rem;
  }
}

/* Estilo para o título do projeto no popup */
.project__block h3 {
  font-size: 4rem;
  /* Aumenta o tamanho do título */
  font-weight: var(--font-weight-medium);
  /* Mantém o peso da fonte consistente */
  color: var(--t-bright);
  /* Mantém a cor do texto */
  margin-bottom: 2rem;
  /* Mais espaçamento abaixo do título */
}

/* Ajuste responsivo para telas grandes */
@media only screen and (min-width: 1200px) {
  .project__block h3 {
    font-size: 5rem;
    /* Tamanho maior para telas grandes */
  }
}

@media only screen and (min-width: 1600px) {
  .project__block h3 {
    font-size: 6rem;
    /* Tamanho ainda maior para telas maiores */
  }
}

.project-header {
  padding: 120px 24px 80px;
  max-width: 1200px;
  margin: 0 auto;
}

.project-title {
  font-size: 64px;
  line-height: 1.1;
  margin-bottom: 24px;
  color: var(--t-bright);
}

.project-tags {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  margin-bottom: 32px;
}

.tag {
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 100px;
  font-size: 14px;
  color: var(--t-bright);
}

.tag.vale {
  border: 2px solid #0d8e82 !important;
  }

.tag.accenture {
  border: 2px solid #a81af8 !important;
}

.tag.direcTV {
  border: 2px solid #045b7e !important;
}


.view-project-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background: var(--t-bright);
  color: var(--base);
  border-radius: 100px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.view-project-btn:hover {
  background: var(--t-medium);
  color: var(--t-bright);
}

@media (max-width: 768px) {
  .project-header {
    padding: 100px 16px 60px;
  }

  .project-title {
    font-size: 40px;
  }

  .project-description {
    font-size: 18px;
  }
}

/* Estilos para o layout de duas colunas */
.sidebar-nav {
  padding: 50px;
  position: sticky;
  top: 80px;
  height: fit-content;
}

.sidebar-nav .nav {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.sidebar-nav .nav-link {
  color: var(--t-bright);
  padding: 10px 15px;
  font-size: 1.6rem;
  transition: all 0.3s ease;
  opacity: 0.4;
  position: relative;
  text-decoration: none;
  display: block;
  width: 100%;
}

/* Estado hover - link em branco com opacidade total */
.sidebar-nav .nav-link:hover {
  opacity: 1;
  color: var(--t-bright);
}

/* Estado ativo - link em azul */
.sidebar-nav .nav-link.active {
  opacity: 1;
  color: #0066FF;
  font-weight: 500;
}

/* Estilos do conteúdo principal */
.main-content {
  padding: 20px;
  padding-top: 50px;
}

.project__block {
  margin-bottom: 60px;
  scroll-margin-top: 100px;
}

.project__block:first-child {
  padding-top: 0;
  margin-top: 0;
}

.project__subtitle {
  margin-top: 0;
  margin-bottom: 2rem;
}

/* Responsividade */
@media (max-width: 768px) {
  .sidebar-nav {
    position: relative;
    top: 0;
    margin-bottom: 30px;
    padding: 20px;
  }
}

@media only screen and (min-width: 1200px) {
  .sidebar-nav {
    max-width: 250px;
  }

  .main-content {
    padding-left: 40px;
  }
}


/* Estilos gerais */
.project-showcase {
  min-height: 400px;
  margin: 0;
  padding: 0;
}

.navigate-left,
.navigate-right {
  position: absolute;
  top: 50%;
  /* Centraliza verticalmente */
  transform: translateY(-50%);
  /* Ajusta para o centro exato */
  background: rgba(0, 0, 0, 0.5);
  border: none;
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
}

.navigate-left {
  left: calc(5%);
}

.navigate-right {
  right: calc(5%);
}

.slides-container {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.project-header {
  padding: 40px 2rem;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
}

.project-title {
  font-size: 64px;
  line-height: 1.1;
  margin-bottom: 24px;
  color: var(--t-bright);
}

.project-tags {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  margin-bottom: 32px;
}

.tag {
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 100px;
  font-size: 14px;
  color: var(--t-bright);
}

.data-item-wrapper {
  padding: 2rem;
  height: 100%;
  margin: 0 auto;
}

.project__data .row {
  margin: 0 -2rem;
}

.pdata__item {
  width: 25%;
}

.data__title,
.data__descr {
  text-align: left;
}

/* Media Queries */
@media (max-width: 768px) {
  .project-showcase {
    min-height: 400px;
  }

  .project-header {
    padding: 40px 5rem;
  }

  .project-title {
    font-size: 40px;
  }

  .project-description {
    font-size: 18px;
  }

  .pdata__item {
    width: 100%;
    margin-bottom: 2rem;
  }

  .pdata__item:last-child {
    margin-bottom: 0;
  }

  .slide {
    max-width: 400px;
  }

  .navigate-left {
    left: 10px;
  }

  .navigate-right {
    right: 10px;
  }
}

@media (min-width: 768px) {
  .project-header {
    padding: 80px 5rem;
  }

  .project-description {
    font-size: 18px;
  }
}

@media (min-width: 1200px) {
  .project-header {
    padding: 80px 5rem 80px;
    max-width: none;
  }
}

@media (min-width: 1600px) {
  .project-header {
    padding: 80px 10rem;
  }

  .project-description {
    font-size: 24px;
  }
}

@media (min-width: 1920px) {
  .project-description {
    font-size: 24px;
  }
}

@media (min-width: 2560px) {
  .project-header {
    padding: 80px 80px;
  }
}

@media (max-width: 768px) {
  .sidebar-nav {
    display: none;
    /* Oculta o menu lateral em telas menores que 768px */
  }
}

@media (max-width: 991px) {
  .sidebar-nav {
    display: none;
    /* Oculta o menu lateral em telas menores que 768px */
  }
}

/* Ajustes do carrossel para mobile */
@media (max-width: 768px) {
  .project__data {
    margin-top: 40px;
    padding: 0 20px;
  }

  /* Ajuste das slides */
  .slides-container {
    touch-action: pan-x;
    user-select: none;
    -webkit-user-select: none;
    cursor: grab;
  }

  .slides-container:active {
    cursor: grabbing;
  }
}

/* Ajuste do header para mobile */
@media (max-width: 768px) {
  .project-header {
    padding-top: 40px;
    /* Aumentado de 40px para 120px */
    padding-bottom: 40px;
    margin-top: 40px;
    /* Adicionado margem extra no topo */
  }

  /* Ajuste do título principal */
  .project-title {
    margin-top: 20px;
    /* Espaço adicional no topo do título */
  }

}

/* Ajuste adicional para telas muito pequenas */
@media (max-width: 480px) {
  .project-header {
    padding-top: 40px;
    /* Ainda mais espaço para telas menores */
  }
}

/* Project Data Section Styles */
.project__data {
  position: relative;
  padding: 0 24px;
  /* Mesmo padding do conteúdo principal */
  margin-top: 40px;
}

@media (max-width: 768px) {
  .project__data {
    padding: 0 24px;
    /* Mantém o padding consistente */
  }

  .data-item-wrapper {
    padding: 0;
    /* Remove o padding adicional que estava causando desalinhamento */
  }

  .pdata__item {
    margin-bottom: 24px;
    /* Espaço entre itens em mobile */
    padding: 0;
    /* Remove padding extra */
  }

  /* Ajuste do alinhamento do texto */
  .data__title,
  .data__descr {
    text-align: left;
    padding: 0;
  }

  /* Ajuste da grid em mobile */
  .project__data .row {
    margin: 0;
    /* Remove margens extras da row */
  }
}

/* Ajuste para desktop */
@media (min-width: 769px) {
  .project__data {
    max-width: 1200px;
    margin: 40px auto;
    padding: 0 24px;
  }

  .pdata__item {
    padding: 0 15px;
    /* Espaçamento entre colunas em desktop */
  }
}

/* Ajuste das tags em mobile */
.project-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 32px;
  padding: 0;
}

.tag {
  display: inline-flex;
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 100px;
  font-size: 14px;
  color: var(--t-bright);
  border: 1px solid var(--stroke-elements);
  /* Adicionado borda */
}

/* Ajustes específicos para mobile */
@media (max-width: 768px) {
  .project-tags {
    gap: 8px;
    /* Reduz o espaçamento entre tags */
    margin-bottom: 24px;
  }

  .tag {
    padding: 6px 12px;
    /* Reduz o padding */
    font-size: 12px;
    /* Reduz o tamanho da fonte */
    white-space: nowrap;
    /* Evita quebra de texto */
    background: var(--base);
    /* Fundo sólido */
    border: 2px solid var(--stroke-elements);
    /* Garante que a borda seja visível */
  }

  /* Ajuste do container das tags */
  .project-header {
    padding: 40px 20px 40px;
    /* Ajusta o padding do header */
  }

  /* Garante que o texto dentro das tags seja visível */
  .tag span {
    color: var(--t-bright);
  }
}

/* Ajuste adicional para telas muito pequenas */
@media (max-width: 360px) {
  .project-tags {
    gap: 6px;
  }

  .tag {
    padding: 4px 10px;
    font-size: 11px;
  }
}

/* Estilos unificados para tags */
.tag {
  display: inline-flex;
  align-items: center;
  padding: 8px 16px;
  background: var(--base);
  border: 2px solid var(--stroke-elements);
  border-radius: 100px;
  font-size: 14px;
  color: var(--t-bright);
  margin: 4px;
  white-space: nowrap;
  transition: all 0.3s ease;
}

/* Hover effect */
.tag:hover {
  background: var(--t-bright);
  color: var(--base);
}

.project-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 24px;
  padding: 0;
}

/* Responsividade */
@media (max-width: 768px) {
  .tag {
    font-size: 12px;
    padding: 6px 12px;
  }

  .project-tags {
    gap: 6px;
    margin-bottom: 20px;
  }
}

@media (max-width: 480px) {
  .tag {
    font-size: 11px;
    padding: 4px 10px;
  }

  .project-tags {
    gap: 4px;
  }
}

/* Swiper */

.slide {
  position: absolute;
  width: auto;
  height: 100%;
  transition: transform 0.5s ease, opacity 0.5s ease;
}

.slide img {
  height: 100%;
  width: auto;
  object-fit: contain;
  border-radius: 28px;
  aspect-ratio: 0.462;
  /* Proporção específica para phones (similar ao iPhone) */
  box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
}

/* Swiper */

.swiper {
  width: 100%;
  padding-top: 10px;
}

.swiper-slide {
  width: 800px;
  height: 400px;
  filter: blur(1px);
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  justify-content: end;
  align-items: self-start;
}

.swiper-slide-active {
  filter: blur(0px);
}

.swiper-pagination-bullet,
.swiper-pagination-bullet-active {
  background: #fff;
}

.swiper-slide--one {
  background: url(/2.png) no-repeat 50% 50% / cover;
}

.swiper-slide--two {
  background: url("/2.png") no-repeat 50% 50% / cover;
}

.swiper-slide--three {
  background: url("/2.png") no-repeat 50% 50% / cover;
}

.swiper-slide--four {
  background: url("/2.png") no-repeat 50% 50% / cover;
}

.swiper-slide--five {
  background: url("/2.png") no-repeat 50% 50% / cover;
}

.swiper-3d .swiper-slide-shadow-left,
.swiper-3d .swiper-slide-shadow-right {
  background-image: none;
}

/* Infinite Scroll Tags Animation */
.tag-list {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-shrink: 0;
  flex-direction: column;
  position: relative;
  /* Mantém position relative aqui */
  padding: 1.5rem 0;
  /* Reduzido de 3rem para 1.5rem */
  overflow: hidden;
}

/* Estilo dos cards de imagem */

.loop-slider .inner {
  display: flex;
  width: fit-content;
  animation-name: loop;
  animation-timing-function: linear;
  animation-iteration-count: infinite;
  animation-direction: var(--direction);
  animation-duration: var(--duration);
  padding: 0.5rem 0;
  /* Reduzido de 1rem para 0.5rem */
}

.loop-slider .image-card {
  width: auto;  /* Changed from fixed width */
  height: auto;  /* Fixed height for consistency */
  margin-right: 1rem;
  border-radius: 12px;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(4px);
  border: 1px solid rgba(255, 255, 255, 0.12);
  box-shadow:
      0 4px 8px rgba(0, 0, 0, 0.2),
      0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.loop-slider .image-card img {
  height: 100%;
  width: auto;  /* Changed from 100% */
  object-fit: contain;  /* Changed from cover */
  transition: transform 0.3s ease;
}

.loop-slider .image-card:hover {
  transform: translateY(-5px);
  box-shadow:
      0 8px 16px rgba(0, 0, 0, 0.3),
      0 4px 8px rgba(0, 0, 0, 0.2);
}

.loop-slider .image-card:hover img {
  transform: scale(1.05);
}

/* Fade effect */
.fade {
  pointer-events: none;
  background: linear-gradient(90deg, #1e293b, transparent 30%, transparent 70%, #1e293b);
  position: absolute;
  inset: 0;
}

@keyframes loop {
  0% {
    transform: translateX(0);
  }

  100% {
    transform: translateX(-50%);
  }
}

/* Responsividade */
@media (max-width: 768px) {
  .loop-slider .image-card {
    height: 140px;  /* Smaller height for mobile */
    margin-right: 1.5rem;
  }

  .tag-list {
    padding: 1rem 0;
    gap: 1rem 0;
  }
}

.loop-slider {
  position: relative;
  /* Adiciona position relative ao slider */
}

/* Ajuste para garantir que as imagens fiquem abaixo do fade */
.loop-slider .inner {
  position: relative;
  z-index: 1;
}

/* Ajuste do fade para ficar por cima */
.fade {
  pointer-events: none;
  background: linear-gradient(90deg,
      var(--base) 0%,
      transparent 20%,
      transparent 80%,
      var(--base) 100%);
  position: absolute;
  inset: 0;
  z-index: 10;
  /* Aumentado o z-index para garantir que fique por cima */
}

.loop-slider {
  position: relative;
  /* Adiciona position relative ao slider */
}

/* Ajuste para garantir que as imagens fiquem abaixo do fade */
.loop-slider .inner {
  position: relative;
  z-index: 1;
}

.full-width-image-container {
  width: 100%;
  margin: 4rem 0;
  padding: 0 24px;
  /* Mantém consistência com o padding do conteúdo */
}

.full-width-image {
  width: 100%;
  height: auto;
  border-radius: 12px;
  object-fit: cover;
}

/* Responsividade */
@media (max-width: 768px) {
  .full-width-image-container {
    padding: 0 16px;
    margin: 3rem 0;
  }
}

@media (min-width: 1200px) {
  .full-width-image-container {
    max-width: 1200px;
    margin: 5rem auto;
  }

  .full-width-image {
    border-radius: 16px;
  }
}

@media (min-width: 1920px) {
  .full-width-image-container {
    max-width: 1400px;
  }
}

/* Estilo base do swiper */

/* Swiper Base Styles */
.swiper {
  width: 100%;
  padding-top: 10px;
}

.swiper-slide {
  width: 800px;
  height: 400px;
  border-radius: 20px;
  display: flex;
  flex-direction: column;
  justify-content: end;
  align-items: self-start;
  overflow: visible;
}

.swiper-slide img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Swiper Navigation */
.swiper-button-prev,
.swiper-button-next {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  border: none;
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
}

.swiper-button-prev::after,
.swiper-button-next::after {
  content: none !important;
}

.swiper-button-prev i,
.swiper-button-next i {
  color: white;
  font-size: 16px;
}

.swiper-button-disabled {
  display: none;
}

.swiper-button-prev:hover,
.swiper-button-next:hover {
  background: none;
}

.swiper-button-prev:hover i,
.swiper-button-next:hover i {
  color: #000000;
}

/* Swiper 3D Effects */
.swiper-3d .swiper-slide-shadow-left,
.swiper-3d .swiper-slide-shadow-right {
  background-image: none;
}


/* Swiper Mobile Styles */
@media (max-width: 768px) {
  .swiper:not(.custom-swiper) {
    height: auto;
    padding: 0 0;
  }

  .swiper:not(.custom-swiper) .swiper-slide {
    width: 100% !important;
    height: auto !important;
    margin-bottom: 24px;
    filter: none;
    transform: none !important;
    border-radius: 12px;
  }

  .swiper:not(.custom-swiper) .swiper-slide img {
    width: 100%;
    height: auto;
    display: block;
    border-radius: 12px;
  }

  .swiper:not(.custom-swiper) .swiper-wrapper {
    display: flex !important;
    flex-direction: column !important;
    transform: none !important;
    padding: 0 !important;
  }

  .swiper:not(.custom-swiper) .swiper-3d {
    perspective: none !important;
  }

  .swiper:not(.custom-swiper) .swiper-3d .swiper-slide-shadow-left,
  .swiper:not(.custom-swiper) .swiper-3d .swiper-slide-shadow-right,
  .swiper:not(.custom-swiper) .swiper-pagination,
  .swiper:not(.custom-swiper) .swiper-button-prev,
  .swiper:not(.custom-swiper) .swiper-button-next {
    display: none;
  }
}

/* Larger Screens */
@media (min-width: 480px) and (max-width: 768px) {
  .swiper {
    padding: 0 24px;
  }
}

@media (min-width: 1200px) {
  .swiper {
    max-width: 1200px;
    margin: 0 auto;
  }
}

@media (min-width: 1920px) {
  .swiper {
    max-width: 1400px;
  }
}


/* App Swiper */

/* Estilos específicos para o primeiro swiper (após o título) */
.custom-swiper.swiper {
  width: 100%;
  padding-top: 50px;
  padding-bottom: 0;
  /* Removido o padding-bottom */
}

/* Esconde as setas de navegação em mobile */
@media (max-width: 768px) {

  .custom-swiper .swiper-button-prev,
  .custom-swiper .swiper-button-next {
    display: none;
  }
}

.custom-swiper .swiper-slide {
  width: 228px !important;
  height: 489px !important;
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  justify-content: end;
  align-items: self-start;
  overflow: hidden;
  transition: transform 0.3s;
}

.custom-swiper .swiper-slide:not(.swiper-slide-active) {
  filter: blur(1px);
  transform: scale(0.85);
}

.custom-swiper .swiper-slide img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.custom-swiper .swiper-slide-active {
  filter: blur(0);
  transform: scale(1);
}

/* Estilos específicos para as setas do primeiro swiper */
.custom-swiper .swiper-button-prev,
.custom-swiper .swiper-button-next {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.5);
  border: none;
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
}

/* Remove o conteúdo padrão das setas do Swiper */
.custom-swiper .swiper-button-prev::after,
.custom-swiper .swiper-button-next::after {
  content: none !important;
}

/* Ajusta a posição das setas para ficarem mais próximas */
.custom-swiper .swiper-button-prev {
  left: 35%;
  /* Aumentado para 35% */
}

.custom-swiper .swiper-button-next {
  right: 35%;
  /* Aumentado para 35% */
}

/* Estilo dos ícones dentro das setas */
.custom-swiper .swiper-button-prev i,
.custom-swiper .swiper-button-next i {
  color: white;
  font-size: 16px;
}

/* Efeito hover nas setas */
.custom-swiper .swiper-button-prev:hover,
.custom-swiper .swiper-button-next:hover {
  background: none;
}

.custom-swiper .swiper-button-prev:hover i,
.custom-swiper .swiper-button-next:hover i {
  color: #000000;
}

/* Esconde setas desabilitadas */
.custom-swiper .swiper-button-disabled {
  display: none;
}

@media (min-width: 768px) {
  .project-header {
    margin-top: 80px;
    /* Aumenta o espaço entre o menu e o início do conteúdo */
  }
}

/* Para telas ainda maiores, podemos aumentar um pouco mais */
@media (min-width: 1200px) {
  .project-header {
    margin-top: 100px;
  }
}

/* App Swiper */
.custom-swiper.swiper {
  width: 100%;
  padding-top: 50px;
  padding-bottom: 0;
  /* Removido o padding-bottom */
}

/* Esconde as setas de navegação em mobile */
@media (max-width: 768px) {

  .custom-swiper .swiper-button-prev,
  .custom-swiper .swiper-button-next {
    display: none;
  }
}

.custom-swiper .swiper-slide {
  width: 229px !important;
  height: 489px !important;
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  justify-content: end;
  align-items: self-start;
  overflow: hidden;
  transition: transform 0.3s;
}

/* App Swiper */
.custom-swiper.swiper {
  width: 100%;
  padding-top: 0;
  /* Removido o padding-top */
  padding-bottom: 0;
}

/* Esconde as setas de navegação em mobile */
@media (max-width: 768px) {

  .custom-swiper .swiper-button-prev,
  .custom-swiper .swiper-button-next {
    display: none;
  }
}

.placeholder-image {
  width: 100%;
  height: auto;
  margin: 40px 0;
  border-radius: 12px;
  overflow: hidden;
}

.placeholder-image img {
  width: 100%;
  height: auto;
  object-fit: cover;
}

/* Newsletter Section */
section#newsletter .container {
  width: 100%;
  padding: 0 24px;

}

section#newsletter .newsletter {
  position: relative;
  display: flex;
  flex-direction: row;
  align-items: stretch;
  border: 1px solid var(--stroke-elements);
  border-radius: 12px;
  overflow: hidden;
  height: auto;
  width: 100%;
  transition: all 0.3s ease;
  z-index: 1;
  background: var(--base);
  background-image: url('../img/backgrounds/palette.png');
  background-size: cover;
  background-position: center;
}


/* Remova ou comente temporariamente outros backgrounds que possam estar interferindo */
section#newsletter .newsletter .content-wrapper {
  position: relative;
  /* Adiciona posicionamento relativo */
  flex: 2;
  display: flex;
  flex-direction: column;
  gap: 2rem;
  padding: 3rem;
  align-items: flex-start;
  z-index: 2;
  /* Garante que o conteúdo fique acima do background */
  background: transparent;
  /* Garante que não há background interferindo */
}

section#newsletter .newsletter:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
}

section#newsletter .newsletter .image-wrapper {
  flex: 1;
  min-width: 40%;
  overflow: hidden;
  border-radius: 1.5rem;
}

section#newsletter .newsletter .image-wrapper img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

section#newsletter .newsletter .image-wrapper img:hover {
  transform: scale(1.05);
}

/* Title Wrapper */
section#newsletter .newsletter .title-wrapper .title {
  font-size: 4.8rem;
  line-height: 1.1;
  color: var(--t-bright);
  margin-bottom: 1rem;
  font-weight: 500;
}

/* Tags */
section#newsletter .newsletter .tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

section#newsletter .newsletter .tags span {
  padding: 8px 16px;
  background: var(--base);
  border: 2px solid var(--stroke-elements);
  border-radius: 100px;
  font-size: 14px;
  color: var(--t-bright);
  transition: all 0.3s ease;
}

section#newsletter .newsletter .tags span:hover {
  background: var(--t-bright);
  color: var(--base);
}

/* Description */
section#newsletter .newsletter .description {
  color: var(--t-sub);
  font-size: 1.8rem;
  line-height: 1.6;
  max-width: 100%;
}


/* Responsividade */
@media (max-width: 1200px) {
  section#newsletter .container {
    max-width: 1000px;
  }

  section#newsletter .newsletter .content-wrapper {
    padding: 2.5rem;
  }

  section#newsletter .newsletter .title-wrapper .title {
    font-size: 4.2rem;
  }
}

@media (max-width: 992px) {
  section#newsletter .container {
    max-width: 800px;
  }

  section#newsletter .newsletter .image-wrapper {
    min-width: 45%;
  }

  section#newsletter .newsletter .content-wrapper {
    padding: 2rem;
  }

  section#newsletter .newsletter .title-wrapper .title {
    font-size: 3.8rem;
  }
}

@media (max-width: 768px) {
  section#newsletter .newsletter {
    flex-direction: column;
  }

  section#newsletter .newsletter .image-wrapper {
    min-width: 100%;
    height: 300px;
  }

  section#newsletter .newsletter .content-wrapper {
    padding: 2rem;
  }

  section#newsletter .newsletter .title-wrapper .title {
    font-size: 3.4rem;
  }

  section#newsletter .tags {
    gap: 6px;
  }

  section#newsletter .tags span {
    padding: 6px 12px;
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  section#newsletter .container {
    padding: 0 16px;
  }

  section#newsletter .newsletter .image-wrapper {
    height: 250px;
  }

  section#newsletter .newsletter .content-wrapper {
    padding: 1.5rem;
  }

  section#newsletter .newsletter .title-wrapper .title {
    font-size: 3rem;
  }

  section#newsletter .newsletter .description {
    font-size: 1.6rem;
  }
}

/* Telas maiores */
@media (min-width: 1920px) {
  section#newsletter .container {
    max-width: 1400px;
  }

  section#newsletter .newsletter .content-wrapper {
    padding: 4rem;
  }

  section#newsletter .newsletter .title-wrapper .title {
    font-size: 4rem;
  }

  section#newsletter .newsletter .description {
    font-size: 2.3rem;
  }
}

/* Ajustes específicos para telas maiores */
@media (min-width: 1920px) {
  section#newsletter .container {
    max-width: 1400px;
  }

  section#newsletter .newsletter {
    max-height: 800px;
    /* Limita a altura máxima */
  }

  section#newsletter .newsletter .image-wrapper {
    min-width: 35%;
    /* Reduz a proporção da imagem */
    max-width: 40%;
  }

  section#newsletter .newsletter .content-wrapper {
    padding: 4rem;
    max-width: 65%;
    /* Limita a largura do conteúdo */
  }
}

/* Ajuste para telas ainda maiores */
@media (min-width: 2560px) {
  section#newsletter .container {
    max-width: 100%;
  }

  section#newsletter .newsletter {
    max-height: 900px;
  }

  section#newsletter .newsletter .image-wrapper {
    min-width: 30%;
    max-width: 35%;
  }
}

section#newsletter .newsletter::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: 0 solid rgba(255, 255, 255, 0.8);
  transition: all 0.6s cubic-bezier(0.19, 1, 0.22, 1);
  pointer-events: none;
  z-index: 1;
  border-radius: 1.5rem;
  opacity: 0;
}

section#newsletter .newsletter:hover::before {
  border-width: 1px;
  opacity: 0.8;
}

/* Newsletter Swiper Styles */
.newsletter-swiper {
  overflow: hidden;
  padding: 0;
  /* Removed top padding */
}

.newsletter-swiper .swiper-slide {
  transition: transform 0.3s ease;
  opacity: 0.5;
  transform: scale(0.9);
  height: auto;
  /* Allow natural height */
  width: 1000px !important;
  /* Increased card width */
}

/* Media queries for larger screens */
@media (min-width: 1920px) {
  .newsletter-swiper .swiper-slide {
    width: 1200px !important;
    /* Even wider for larger screens */
  }
}

.newsletter-swiper .swiper-slide-active {
  opacity: 1;
  transform: scale(1);
}

/* Ensure consistent card sizes */
.newsletter-swiper .newsletter {
  margin: 0;
  min-height: 400px;
  display: flex;
  flex-direction: row;
}

.newsletter-swiper .newsletter .content-wrapper {
  min-height: 400px;
  display: flex;
  flex-direction: column;
  padding: 3rem;
  flex: 2;
}

/* Content layout adjustments */
.newsletter-swiper .newsletter .title-wrapper {
  margin-bottom: 2rem;
}

.newsletter-swiper .newsletter .title {
  font-size: 4.8rem;
  line-height: 1.1;
}

.newsletter-swiper .newsletter .tags {
  margin-bottom: 2rem;
}

.newsletter-swiper .newsletter .description {
  font-size: 1.8rem;
  line-height: 1.6;
  margin-bottom: auto;
  /* Push the button to the bottom */
}

/* Project button styles */
.newsletter-swiper .newsletter .project-link {
  display: inline-flex;
  align-items: center;
  gap: 0.8rem;
  /* Space between text and arrow */
  padding: 0.8rem 1.6rem;
  /* Smaller padding */
  background: var(--t-bright);
  color: var(--base);
  border-radius: 100px;
  font-weight: 500;
  font-size: 1.4rem;
  transition: all 0.3s ease;
  margin-top: 2rem;
  text-decoration: none;
}

.newsletter-swiper .newsletter .project-link i {
  font-size: 1.6rem;
  transform: rotate(-45deg) translateX(0px) translateY(0px);
  /* Initial diagonal position */
  transition: transform 0.3s ease;
}

.newsletter-swiper .newsletter .project-link:hover {
  background: var(--t-medium);
  color: var(--t-bright);
  transform: translateY(-2px);
}

.newsletter-swiper .newsletter .project-link:hover i {
  transform: rotate(0deg) translateX(4px) translateY(0);
  /* Return to horizontal on hover */
}

/* Newsletter Swiper Styles */
.newsletter-swiper .newsletter {
  margin: 0;
  min-height: 400px;
  display: flex;
  flex-direction: row;
}

.newsletter-swiper .newsletter .image-wrapper {
  min-height: 400px;
  flex: 1;
  order: 1;
  /* Ensures image stays on the left */
}

.newsletter-swiper .newsletter .content-wrapper {
  min-height: 400px;
  display: flex;
  flex-direction: column;
  padding: 3rem;
  flex: 2;
  order: 2;
  /* Ensures content stays on the right */
}

/* Mobile adjustments */
@media (max-width: 768px) {
  .newsletter-swiper .newsletter {
    flex-direction: column;
  }

  .newsletter-swiper .newsletter .image-wrapper,
  .newsletter-swiper .newsletter .content-wrapper {
    order: 0;
    /* Reset order for mobile */
  }
}

/* Rest of your existing styles... */

.grid-container {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* Newsletter alternating layout */
.newsletter.image-right {
  flex-direction: row-reverse;
  /* Inverte a ordem dos elementos flex */
}

/* Ajustes responsivos para o layout alternado */
@media (max-width: 768px) {
  .newsletter.image-right {
    flex-direction: column;
    /* Em mobile, mantém a ordem vertical padrão */
  }

  .newsletter.image-right .image-wrapper {
    order: -1;
    /* Força a imagem a ficar no topo em mobile */
  }
}

/* Ajustes específicos para o swiper */
.newsletter-swiper .newsletter.image-right {
  flex-direction: row-reverse;
}

@media (max-width: 768px) {
  .newsletter-swiper .newsletter.image-right {
    flex-direction: column;
  }

  .newsletter-swiper .newsletter.image-right .image-wrapper {
    order: -1;
  }
}

/* Newsletter layout */
section#newsletter .newsletter {
  display: flex;
  flex-direction: row;
}

/* Classe para inverter a ordem dos elementos */
section#newsletter .newsletter.newsletter-reverse {
  flex-direction: row-reverse;
}

/* Ajustes responsivos */
@media (max-width: 768px) {

  section#newsletter .newsletter,
  section#newsletter .newsletter.newsletter-reverse {
    flex-direction: column;
  }

  section#newsletter .newsletter .image-wrapper {
    width: 100%;
    min-height: 300px;
  }
}

/* Newsletter layout */
section#newsletter .newsletter {
  display: flex;
  flex-direction: row;
}

/* Classe para inverter a ordem apenas do segundo newsletter */
section#newsletter .newsletter:nth-of-type(2) {
  flex-direction: row-reverse;
}

/* Ajustes responsivos */
@media (max-width: 768px) {

  section#newsletter .newsletter,
  section#newsletter .newsletter:nth-of-type(2) {
    flex-direction: column;
  }

  section#newsletter .newsletter .image-wrapper {
    width: 100%;
    min-height: 100%;
  }
}

/* Project Navigation Styles */
.project-navigation {
  padding: 6rem 0;
  border-top: 1px solid var(--stroke-elements);
  margin-top: 6rem;
}

.project-navigation .container {
  display: flex;
  flex-direction: row;
  gap: 2rem;
}

.project-navigation .newsletter {
  position: relative;
  display: flex;
  flex-direction: row;
  align-items: stretch;
  background-color: var(--base);
  border: 1px solid var(--stroke-elements);
  border-radius: 12px;
  overflow: hidden;
  height: 200px;
  /* Altura fixa mais compacta */
  width: 50%;
  /* Cada item ocupa metade do container */
  transition: all 0.3s ease;
  z-index: 1;
  text-decoration: none;
}

.project-navigation .newsletter:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
}

.project-navigation .content-wrapper {
  position: relative;
  flex: 2;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 2rem;
  z-index: 2;
}

.project-navigation .nav-label {
  font-size: 1.4rem;
  color: var(--t-sub);
}

.project-navigation .title {
  font-size: 2.4rem;
  line-height: 1.2;
  color: var(--t-bright);
  margin-bottom: auto;
}

.project-navigation .nav-icon {
  display: flex;
  align-items: center;
  gap: 0.8rem;
}

.project-navigation .nav-icon i {
  font-size: 1.8rem;
  /* Ícone menor */
  padding: 0.8rem;
  background: var(--t-bright);
  color: var(--base);
  border-radius: 50%;
  transition: all 0.3s ease;
}

.project-navigation .newsletter:hover .nav-icon i {
  background: var(--t-medium);
  color: var(--t-bright);
}

.project-navigation .image-wrapper {
  flex: 1;
  min-width: 35%;
  overflow: hidden;
}

.project-navigation .image-wrapper img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.project-navigation .newsletter:hover .image-wrapper img {
  transform: scale(1.05);
}

/* Inverte a direção do segundo newsletter */
.project-navigation .newsletter:nth-of-type(2) {
  flex-direction: row-reverse;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .project-navigation {
    padding: 4rem 0;
  }

  .project-navigation .container {
    flex-direction: column;
  }

  .project-navigation .newsletter {
    width: 100%;
    height: 180px;
  }

  .project-navigation .newsletter,
  .project-navigation .newsletter:nth-of-type(2) {
    flex-direction: row;
    /* Mantém a direção em linha mesmo em mobile */
  }

  .project-navigation .content-wrapper {
    padding: 1.5rem;
  }

  .project-navigation .title {
    font-size: 2rem;
  }

  .project-navigation .image-wrapper {
    min-width: 40%;
  }
}

/* ... restante do CSS mantido igual ... */

/* Primeiro newsletter com imagem à direita */
.project-navigation .newsletter:nth-of-type(1) {
  flex-direction: row-reverse;
}

/* Segundo newsletter com imagem à esquerda */
.project-navigation .newsletter:nth-of-type(2) {
  flex-direction: row;
}

/* Ajuste responsivo */
@media (max-width: 768px) {
  /* ... outros estilos responsivos ... */

  .project-navigation .newsletter,
  .project-navigation .newsletter:nth-of-type(1),
  .project-navigation .newsletter:nth-of-type(2) {
    flex-direction: row;
    /* Mantém a direção em linha mesmo em mobile */
  }
}

.project-navigation .nav-icon {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  padding: 0.8rem 1.6rem;
  /* Aumentado o padding para acomodar o texto */
  background: var(--t-bright);
  color: var(--base);
  border-radius: 100px;
  font-size: 1.4rem;
  font-weight: 500;
  transition: all 0.3s ease;
  width: fit-content;
}

.project-navigation .nav-icon i {
  font-size: 1.6rem;
  color: inherit;
  /* Herda a cor do pai */
  transition: transform 0.3s ease;
}

.project-navigation .newsletter:hover .nav-icon {
  background: var(--t-medium);
  color: var(--t-bright);
}

/* Ajuste específico para o primeiro card (seta à esquerda) */
.project-navigation .newsletter:nth-of-type(1) .nav-icon i {
  order: -1;
  /* Coloca a seta antes do texto */
}

/* Ajuste específico para o segundo card (seta à direita) */
.project-navigation .newsletter:nth-of-type(2) .nav-icon i {
  order: 2;
  /* Coloca a seta depois do texto */
}

@media (max-width: 768px) {
  .project-navigation .nav-icon {
    padding: 0.6rem 1.2rem;
    font-size: 1.2rem;
  }

  .project-navigation .nav-icon i {
    font-size: 1.4rem;
  }
}

/* ... restante do CSS mantido igual ... */

.project-navigation .content-wrapper {
  position: relative;
  flex: 2;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 2rem;
  z-index: 2;
}

/* Alinhamento padrão para o primeiro card */
.project-navigation .newsletter:nth-of-type(1) .content-wrapper {
  align-items: flex-start;
  /* Alinha à esquerda */
}

/* Alinhamento específico para o segundo card */
.project-navigation .newsletter:nth-of-type(2) .content-wrapper {
  align-items: flex-end;
  /* Alinha à direita */
  text-align: right;
  /* Alinha o texto à direita */
}

@media (max-width: 768px) {

  .project-navigation .newsletter:nth-of-type(1) .content-wrapper,
  .project-navigation .newsletter:nth-of-type(2) .content-wrapper {
    align-items: flex-start;
    /* Em mobile, sempre alinha à esquerda */
    text-align: left;
  }
}

@media (max-width: 768px) {
  .project-navigation .container {
    flex-direction: column;
  }

  .project-navigation .newsletter {
    width: 100%;
    height: 180px;
  }

  /* Projeto anterior (em cima): imagem à esquerda */
  .project-navigation .newsletter:nth-of-type(1) {
    flex-direction: row;
    /* Força imagem à esquerda */
  }

  /* Projeto seguinte (embaixo): imagem à direita */
  .project-navigation .newsletter:nth-of-type(2) {
    flex-direction: row-reverse;
    /* Força imagem à direita */
  }

  .project-navigation .content-wrapper {
    padding: 1.5rem;
  }

  .project-navigation .newsletter:nth-of-type(1) .content-wrapper,
  .project-navigation .newsletter:nth-of-type(2) .content-wrapper {
    align-items: flex-start;
    text-align: left;
  }

  .project-navigation .title {
    font-size: 2rem;
  }

  .project-navigation .image-wrapper {
    min-width: 40%;
  }
}

/* Desktop styles */
.project-navigation .newsletter:nth-of-type(1) {
  flex-direction: row-reverse;
}

.project-navigation .newsletter:nth-of-type(2) {
  flex-direction: row;
}

/* Mobile styles - com maior especificidade e removendo conflitos */
@media (max-width: 768px) {
  .project-navigation .container {
    flex-direction: column;
  }

  .project-navigation .newsletter {
    width: 100%;
    height: 180px;
  }

  /* Reset explícito das direções do desktop */
  .project-navigation .newsletter:nth-of-type(1),
  .project-navigation .newsletter:nth-of-type(2) {
    flex-direction: initial;
  }

  /* Aplicar novas direções para mobile */
  .project-navigation .newsletter:nth-of-type(1) {
    flex-direction: row-reverse !important;
  }

  .project-navigation .newsletter:nth-of-type(2) {
    flex-direction: row !important;
  }

  /* Ajustes de conteúdo */
  .project-navigation .content-wrapper {
    padding: 1.5rem;
  }

  .project-navigation .newsletter:nth-of-type(1) .content-wrapper,
  .project-navigation .newsletter:nth-of-type(2) .content-wrapper {
    align-items: flex-start;
    text-align: left;
  }

  .project-navigation .title {
    font-size: 2rem;
  }

  .project-navigation .image-wrapper {
    min-width: 40%;
  }

  /* Alinhamento do primeiro card (mantém à esquerda) */
  .project-navigation .newsletter:nth-of-type(1) .content-wrapper {
    align-items: flex-start;
    text-align: left;
  }

  /* Alinhamento do segundo card (alinha à direita) */
  .project-navigation .newsletter:nth-of-type(2) .content-wrapper {
    align-items: flex-end;
    text-align: right;
  }

  /* Ajuste específico para o botão do segundo card */
  .project-navigation .newsletter:nth-of-type(2) .nav-icon {
    margin-left: auto;
    /* Empurra o botão para a direita */
  }
}

/* Project Navigation Styles */
.project-navigation {
  padding: 6rem 0;
  border-top: 1px solid var(--stroke-elements);
  margin-top: 6rem;
}

.project-navigation .newsletter {
  position: relative;
  display: flex;
  flex-direction: row;
  align-items: stretch;
  background: var(--base);
  background-image: url('../img/backgrounds/palette.png');
  background-size: cover;
  background-position: center;
  border: 1px solid var(--stroke-elements);
  border-radius: 12px;
  overflow: hidden;
  height: 200px;
  transition: all 0.3s ease;
  z-index: 1;
  text-decoration: none;
}

/* Light mode - explicitly set no background */
html[data-theme="light"] .project-navigation .newsletter {
  background: var(--base) !important;
  background-image: none !important;
}

/* Dark mode - explicitly set background image */
html[data-theme="dark"] .project-navigation .newsletter {
  background: var(--base);
  background-image: url('../img/backgrounds/palette.png') !important;
  background-size: cover !important;
  background-position: center !important;
}
/* Estilo para o container do mockup */
.custom-swiper {
    position: relative;
}

.mockup-overlay {
    position: absolute;
}

/* Ajustes para imagem em largura total */
.content__block.grid-block {
    margin: 0;
    padding: 0;
    width: 100%;
}

.content__block .container-fluid {
    padding: 0 !important;
    margin: 0 !important;
    width: 100%;
}

.full-width-image-container {
    margin: 0;
    padding: 0;
    width: 100%;
}

.full-width-image {
    width: 100%;
    height: auto;
    display: block;
    object-fit: cover;
}

.gallery__link {
    display: block;
    width: 100%;
    margin: 0;
    padding: 1rem;
}

/* Remove margens da galeria */
.my-gallery {
    margin: 0;
    padding: 0;
    width: 100%;
}

/* Adjust gallery spacing */
.my-gallery {
  margin: 0;
  padding: 0;
  width: 100%;
}

.gallery__item {
  margin-bottom: 1rem; /* Reduced from default spacing */
}

/* Remove margin from last gallery item */
.gallery__item:last-child {
  margin-bottom: 0;
}

/* Adjust row spacing */
.my-gallery .row {
  margin-bottom: 1rem; /* Reduced spacing between rows */
}

.my-gallery .row:last-child {
  margin-bottom: 0; /* Remove margin from last row */
}

/* Remove extra spacing from mt-4 class */
.my-gallery .mt-4 {
  margin-top: 1rem; /* Override Bootstrap's mt-4 spacing */
}

/* Footer Section Styles */
.footer-section {
    background-color: var(--base);
    padding: 8rem 0;
    margin-top: 6rem;
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.footer-grid {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 4rem;
}

.footer-text {
    text-align: left;
    max-width: 600px;
}

.footer-section h2 {
    font-size: 6rem;
    color: var(--t-bright);
    margin-bottom: 2rem;
    font-weight: var(--font-weight-medium);
    text-align: left;
}

.footer-section p {
    font-size: 2rem;
    color: var(--t-sub);
    line-height: 1.6;
    text-align: left;
}

.social-icons {
    display: flex;
    gap: 2rem;
    align-items: center;
}

.social-icon {
    width: 8rem;  /* Increased size */
    height: 8rem; /* Increased size */
    border-radius: 1.6rem;
    background-color: rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.social-icon i {
    font-size: 3.2rem; /* Increased icon size */
    color: var(--t-bright);
}

.social-icon:hover {
    background-color: var(--t-bright);
    transform: translateY(-5px);
}

.social-icon:hover i {
    color: var(--base);
}

/* Responsive adjustments */
@media (max-width: 968px) {
    .footer-grid {
        flex-direction: column;
        text-align: center;
        gap: 4rem;
    }

    .footer-text {
        text-align: center;
    }

    .footer-section h2 {
        text-align: center;
        font-size: 4.5rem;
    }

    .footer-section p {
        text-align: center;
    }

    .social-icons {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .footer-section {
        padding: 6rem 0;
    }

    .footer-section h2 {
        font-size: 3.6rem;
    }

    .footer-section p {
        font-size: 1.8rem;
    }

    .social-icon {
        width: 6rem;
        height: 6rem;
    }

    .social-icon i {
        font-size: 2.4rem;
    }
}

/* Disclaimer Styles */
.disclaimer {
    margin: 3rem 0;
    padding: 2rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 1.2rem;
    border-left: 4px solid var(--t-bright);
}

.disclaimer__content {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.disclaimer__content i {
    font-size: 2.4rem;
    color: var(--t-bright);
    flex-shrink: 0;
    margin-top: 0;
}

.disclaimer__text {
    font-size: 1.4rem;
    color: var(--t-medium);
    margin: 0;
    line-height: 1.6;
}

@media (max-width: 768px) {
    .disclaimer {
        margin: 2rem 0;
        padding: 1.5rem;
    }

    .disclaimer__content {
        align-items: flex-start;
    }

    .disclaimer__text {
        font-size: 1.3rem;
    }
}

/* Estilos específicos para o swiper da página Sports Experience */
.custom-swiper.sports-swiper .swiper-slide {
  width: 375px !important;
  height: 475px !important;
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  justify-content: end;
  align-items: self-start;
  overflow: hidden;
  transition: transform 0.3s;
}

/* Ajuste responsivo para telas menores */
@media (max-width: 1400px) {
  .custom-swiper.sports-swiper .swiper-slide {
      width: 521px !important;  /* 75% do tamanho original */
      height: 668px !important;
  }
}

@media (max-width: 992px) {
  .custom-swiper.sports-swiper .swiper-slide {
      width: 348px !important;  /* 50% do tamanho original */
      height: 445px !important;
  }
}

@media (max-width: 768px) {
  .custom-swiper.sports-swiper .swiper-slide {
      width: 174px !important;  /* 25% do tamanho original */
      height: 223px !important;
  }
}
